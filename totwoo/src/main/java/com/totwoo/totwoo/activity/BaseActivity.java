package com.totwoo.totwoo.activity;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.util.Log;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.animation.Animation;
import android.view.animation.Animation.AnimationListener;
import android.view.animation.AnimationUtils;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.ColorInt;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.util.Consumer;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.utils.EdgeToEdgeUtils;
import com.totwoo.totwoo.utils.WrapperSubscriber;
import com.totwoo.totwoo.widget.LoadingDialog;
import com.umeng.analytics.MobclickAgent;

import java.lang.ref.WeakReference;
import java.util.NoSuchElementException;
import java.util.concurrent.atomic.AtomicBoolean;

import rx.Observable;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * App 所有 Activity 的基类， 用户对 App全局的界面定制，统一操作的相关处理
 *
 * <AUTHOR>
 * @date 2015-2015年7月3日
 */
public class BaseActivity extends AppCompatActivity {

    public static String KILLACTIVITYS = "Kill_com.totwoo.towoo";

    private ImageView backBtn;
    private ImageView cancelBtn;
    private TextView titleView;
    private TextView rightTv;
    private ImageView rightIcon;
    private ImageView right2Icon;
    private EditText searchView;
    private View backgroundView;
    /**
     * BaseActivity 提供的Hanlder, 默认已初始化, 如需要定制可重新赋值
     */
    public Handler mHandler;

    private boolean skipCalligraphy;

    private BroadcastReceiver killReceive;
    private boolean initTopBar;

    private LoadingDialog loadingDialog;


    public void dismissProgressDialog() {
        if (!isFinishing() && !isDestroyed()) {
            if (loadingDialog != null && loadingDialog.isShowing()) {
                loadingDialog.dismiss();
            }
        }
    }


    public void showProgressDialog() {
        showProgressDialog("");
    }

    public void showProgressDialog(String msg) {
        if (loadingDialog == null) {
            loadingDialog = new LoadingDialog(this, msg);
        }
        if (!isFinishing() && !isDestroyed()) {
            if (!loadingDialog.isShowing()) {
                loadingDialog.show();
            }
        }
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // 启用Edge-to-Edge适配 (支持Android 6+，Android 15+强制)
        EdgeToEdgeUtils.enableEdgeToEdge(this);
        super.onCreate(savedInstanceState);

        mHandler = new MHandler(this);
        registerKillActivityBR();
    }

    private class MHandler extends Handler {
        private final WeakReference<BaseActivity> mActivity;

        public MHandler(BaseActivity activity) {
            this.mActivity = new WeakReference<>(activity);
        }

        @Override
        public void handleMessage(Message msg) {
            BaseActivity baseActivity = mActivity.get();
            if (baseActivity == null) {
                return;
            }
        }
    }

    /**
     * 注册一个广播 用来统一关闭activity
     */
    public static AtomicBoolean isTokenDialogShow = new AtomicBoolean(false);

    private void registerKillActivityBR() {
        IntentFilter filter = new IntentFilter(KILLACTIVITYS);
        killReceive = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                Log.w("BaseActivity", "onReceive, kill activity broadcast, finish(): " + this);
                finish();
            }
        };

        LocalBroadcastManager.getInstance(this).registerReceiver(killReceive, filter);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mHandler.removeCallbacksAndMessages(null);

        try {
            if (killReceive != null)
                unregisterReceiver(killReceive);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 发送关闭所有activity的广播
     */
    public void sendCloseActivitiesBR(boolean isTokenout) {
        Log.d("BaseActivity", "sendCloseActivitiesBR() called with: isTokenout = [" + isTokenout + "]");
        Intent intent = new Intent();
        intent.setAction(KILLACTIVITYS);
        intent.putExtra("is_token", isTokenout);
        sendBroadcast(intent);

        LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
    }

    @Override
    public void setContentView(int layoutResID) {
        super.setContentView(layoutResID);
        // 默认适配行为
        setupDefaultEdgeToEdge();
    }

    @Override
    public void setContentView(View view) {
        super.setContentView(view);
        // 在setContentView后自动设置根布局的Insets处理
        setupDefaultEdgeToEdge();
    }

    /**
     * 为Activity的内容视图设置Edge-to-Edge适配
     *
     * 统一适配方案：
     * - Android 15+: 系统强制Edge-to-Edge，必须正确处理Insets
     * - 替代XML中的android:fitsSystemWindows="true"
     * - 替代TopBar的单独Insets处理
     * - 为整个内容区域提供统一的上下边距适配
     *
     * 子类可以重写此方法来自定义适配行为
     */
    protected void setupDefaultEdgeToEdge() {
        View topBar = findViewById(R.id.totwoo_topbar_layout);
        View contentView = findViewById(android.R.id.content);

        // 默认行为：TopBar处理状态栏，ContentView处理导航栏
        if (topBar != null) {
            EdgeToEdgeUtils.setupTopInsets(topBar);
        }
        if (contentView != null) {
            EdgeToEdgeUtils.setupBottomInsets(contentView);
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        //仅仅初始化一次 Topbar
        if (!initTopBar) {
            initTopBar();
            initTopBar = true;
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        MobclickAgent.onResume(this);


        // 检查应用从后台切换前台， 切换实时模式
        if (!ToTwooApplication.isForeground) {
            ToTwooApplication.isForeground = true;
            // 应用切换前台
//            BluetoothManage.getInstance().changeDataMode(1);
            BluetoothManage.getInstance().reconnect(false);
        }
        Log.d("TAG", "onStart: " + ToTwooApplication.isForeground);
    }

    @Override
    protected void onPause() {
        super.onPause();
        MobclickAgent.onPause(this);


        if (ToTwooApplication.isForeground) {
            ToTwooApplication.isForeground = false;
            // 切换到后台
//            BluetoothManage.getInstance().changeDataMode(3);
        }

        Log.d("TAG", "onStop: " + ToTwooApplication.isForeground);
    }

    @Override
    protected void onStop() {
        super.onStop();
    }

    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);

        if (level == TRIM_MEMORY_UI_HIDDEN && ToTwooApplication.isForeground) {
            ToTwooApplication.isForeground = false;
            // 切换到后台
//            BluetoothManage.getInstance().changeDataMode(3);
        }
        Log.d("TAG", "onTrimMemory: " + ToTwooApplication.isForeground);
    }

    /**
     * 初始化顶部栏， 对于需要定制顶部栏的Activity， 直接重写此方法即可
     */
    protected void initTopBar() {
    }

    /**
     * 获取当前的顶部栏整个视图
     * 已适配Edge-to-Edge，自动处理状态栏高度
     */
    public RelativeLayout getTopBar() {
        RelativeLayout topBar = (RelativeLayout) getWindow().findViewById(
                R.id.totwoo_topbar_layout);

        if (topBar == null) {
            throw new NoSuchElementException(
                    "The acitvity contentView must be include 'totwoo_actionbar_layout'!!");
        }
        return topBar;
    }



    public View getTopBarBackgroundObject() {
        backgroundView = getTopBar().findViewById(R.id.top_bar_background_obj);

        return backgroundView;
    }

    /**
     * 设置顶部栏左侧返回键图标<br>
     * 左侧返回键默认具有点击关闭当前Activity的监听事件，如果需要关闭此事件
     */
    public void setTopBackIcon(int resId) {
        if (backBtn == null) {
            backBtn = (ImageView) getTopBar().findViewById(R.id.top_bar_back_btn);
        }
        backBtn.setVisibility(View.VISIBLE);
        backBtn.setImageResource(resId);
        backBtn.setOnClickListener(v -> finish());
    }

    /**
     * 设置顶部栏左侧返回键图标<br>
     * 左侧返回键默认具有点击关闭当前Activity的监听事件，如果需要关闭此事件
     */
    public void setTopCancelIcon(int resId) {
        if (cancelBtn == null) {
            cancelBtn = (ImageView) getTopBar().findViewById(R.id.top_bar_cancel_btn);
        }
        cancelBtn.setVisibility(View.GONE);
        cancelBtn.setImageResource(resId);
        cancelBtn.setOnClickListener(v -> finish());
    }

    public void setTopCancelIconVisibility(int visibility) {
        if (cancelBtn == null) {
            cancelBtn = (ImageView) getTopBar().findViewById(R.id.top_bar_cancel_btn);
        }
        cancelBtn.setVisibility(visibility);
    }

    /**
     * 设置顶部栏标题
     *
     * @param stringId
     */
    public void setTopTitle(int stringId) {
        if (titleView == null) {
            titleView = (TextView) getTopBar().findViewById(R.id.top_bar_title_view);
        }
        titleView.setVisibility(View.VISIBLE);
        titleView.setText(stringId);
    }

    /**
     * 设置顶部栏标题
     *
     * @param
     */
    public void setTopTitle(String title) {
        if (titleView == null) {
            titleView = (TextView) getTopBar().findViewById(
                    R.id.top_bar_title_view);
        }
        titleView.setVisibility(View.VISIBLE);
        titleView.setText(title);
    }

    public void setTopTitleColor(int color) {
        if (titleView == null) {
            titleView = (TextView) getTopBar().findViewById(
                    R.id.top_bar_title_view);
        }
        titleView.setTextColor(color);
    }

    /**
     * 设置顶部左侧按钮点击监听， 如果需要设置点击无反应 设置listener 为null 即可
     *
     * @param
     */
    public void setTopLeftOnclik(OnClickListener listener) {
        if (listener != null) {
            backBtn.setOnClickListener(listener);
        } else {
            backBtn.setClickable(false);
        }
    }

    public void setTopLeftIcon(int resId) {
        if (backBtn == null) {
            backBtn = (ImageView) getTopBar().findViewById(R.id.top_bar_back_btn);
        }
        backBtn.setVisibility(View.VISIBLE);
        backBtn.setImageResource(resId);
    }


    /**
     * 设置顶部栏最右侧图标
     *
     * @param iconId
     */
    public void setTopRightIcon(int iconId) {
        if (rightIcon == null) {
            rightIcon = (ImageView) getTopBar().findViewById(
                    R.id.top_bar_right_icon);
        }
        rightIcon.setClickable(true);
        rightIcon.setVisibility(View.VISIBLE);
        rightIcon.setImageResource(iconId);
    }


    public void setTopRigh2tIcon(int iconId) {
        if (right2Icon == null) {
            right2Icon = (ImageView) getTopBar().findViewById(
                    R.id.top_bar_right_2_icon);
        }
        right2Icon.setClickable(true);
        right2Icon.setVisibility(View.VISIBLE);
        right2Icon.setImageResource(iconId);
    }

    public void setTopRightGone() {
        if (rightIcon == null)
            return;
        rightIcon.setVisibility(View.GONE);
        rightIcon.setOnClickListener(null);
        rightIcon.setClickable(false);
    }

    /**
     * 设置右侧菜单的文字
     *
     * @param stringId
     */
    public void setTopRightString(int stringId) {
        if (rightTv == null) {
            rightTv = (TextView) getTopBar()
                    .findViewById(R.id.top_bar_right_tv);
        }
        rightTv.setVisibility(View.VISIBLE);
        rightTv.setText(stringId);
    }

    /**
     * 获取顶部栏搜索框
     *
     * @return searchView
     */
    public EditText getTopSearchView() {
        if (searchView == null) {
            searchView = (EditText) getTopBar().findViewById(
                    R.id.top_bar_search_et);
        }
        searchView.setVisibility(View.VISIBLE);

        Animation anim = AnimationUtils.loadAnimation(this,
                R.anim.search_view_show);
        anim.setAnimationListener(new AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {
            }

            @Override
            public void onAnimationRepeat(Animation animation) {
            }

            @Override
            public void onAnimationEnd(Animation animation) {
                searchView.setHint(R.string.search_contact);
                searchView.requestFocus();
            }
        });
        searchView.startAnimation(anim);

        return searchView;
    }

    /**
     * 获取顶部栏 title
     *
     * @return
     */
    public TextView getTopTitleView() {
        if (titleView == null) {
            titleView = (TextView) getTopBar().findViewById(
                    R.id.top_bar_title_view);
        }

        titleView.setVisibility(View.VISIBLE);
        return titleView;
    }

    /**
     * 设置顶部栏标题， 默认靠左 如果需居中需调用： {@link #
     *
     * @param
     */
    public void setTopRightString(String title) {
        if (rightTv == null) {
            rightTv = (TextView) getTopBar()
                    .findViewById(R.id.top_bar_right_tv);
        }
        rightTv.setVisibility(View.VISIBLE);
        rightTv.setText(title);
    }

    /**
     * 设置顶部栏标题， 默认靠左 如果需居中需调用： {@link #
     *
     * @param
     */
    public void setTopRightString(String title, int color) {
        if (rightTv == null) {
            rightTv = (TextView) getTopBar()
                    .findViewById(R.id.top_bar_right_tv);
        }
        rightTv.setVisibility(View.VISIBLE);
        rightTv.setText(title);
        rightTv.setTextColor(color);
    }

    /**
     * 设置右侧点击监听<br>
     * 右侧图标及文字， 哪个不为空设置哪个。
     *
     * @param listener
     */
    public void setTopRightOnClick(OnClickListener listener) {
        if (rightIcon != null) {
            rightIcon.setOnClickListener(listener);
        } else if (rightTv != null) {
            rightTv.setOnClickListener(listener);
        }
    }

    /**
     * 获得右侧TextView， 可以更特殊化定制。
     *
     * @param
     */
    public TextView getTopRightTv() {
        if (rightTv == null) {
            rightTv = (TextView) getTopBar()
                    .findViewById(R.id.top_bar_right_tv);
        }
        rightTv.setVisibility(View.VISIBLE);
        return rightTv;
    }

    /**
     * 获取右侧第二位的 Icon
     *
     * @return
     */
    public ImageView getTopRight2Icon() {
        if (right2Icon == null) {
            right2Icon = (ImageView) getTopBar().findViewById(R.id.top_bar_right_2_icon);
        }

        right2Icon.setVisibility(View.VISIBLE);
        return right2Icon;
    }

    /**
     * 获得右侧ImageView， 可以更特殊化定制。
     *
     * @param
     */
    public ImageView getTopRightIcon() {
        if (rightIcon == null) {
            rightIcon = (ImageView) getTopBar().findViewById(
                    R.id.top_bar_right_icon);
        }
        rightIcon.setVisibility(View.VISIBLE);
        return rightIcon;
    }

    /**
     * 获得左侧 返回 ImageView， 可以更特殊化定制。
     *
     * @param
     */
    public ImageView getTopLeftImage() {
        if (backBtn == null) {
            backBtn = (ImageView) getTopBar().findViewById(
                    R.id.top_bar_back_btn);
        }
        backBtn.setVisibility(View.VISIBLE);
        return backBtn;
    }

    /**
     * 设置顶部栏背景
     * 已适配Edge-to-Edge，自动处理状态栏
     *
     * @param resId
     */
    public void setTopbarBackground(int resId) {
        // Edge-to-Edge已在onCreate中启用，无需额外处理
        getTopBar().setBackgroundResource(resId);
        // TopBar的Insets处理已在getTopBar()中自动设置
    }

    /**
     * 设置顶部栏背景色
     *
     * @param color
     */
    public void setTopbarBackgroundColor(@ColorInt int color) {
        if (backgroundView != null) {
            backgroundView.setBackgroundColor(color);
            backgroundView.setVisibility(View.VISIBLE);
        }
    }


    /**
     * 设置侧滑状态 默认可以侧滑
     *
     * @param state true是可以
     */
    public void setSpinState(boolean state) {
//        frameView.setScrolled(state);
    }


    /**
     * 灵活的错误处理接口
     */
    public interface FlexibleErrorHandler<T> {
        /**
         * 处理错误
         * @param error 错误信息
         * @return true表示已处理（不执行默认Toast），false表示继续执行默认Toast
         */
        boolean handleError(HttpBaseBean<T> error);
    }

    /**
     * 通用的网络请求封装
     *
     * @param observable  网络请求的 Observable
     * @param success     成功回调
     * @param error       失败回调
     * @param showLoading 是否显示加载框
     */
    public  <T> void launchRequest(Observable<HttpBaseBean<T>> observable,
                                         Consumer<T> success,
                                         Consumer<HttpBaseBean> error,
                                         boolean showLoading) {

        observable.subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .retry(2) // 默认重试 2 次
                .subscribe(new WrapperSubscriber<>(showLoading) {
                    @Override
                    protected void onSuccess(T data) {
                        super.onSuccess(data);
                        success.accept(data);
                    }

                    @Override
                    protected void onFail(HttpBaseBean data) {
                        error.accept(data);
                    }
                });
    }



    /**
     * 通用的网络请求封装
     *
     * @param observable  网络请求的 Observable
     * @param success     成功回调
     * @param showLoading 是否显示加载框
     */
    public  <T> void launchRequest(Observable<HttpBaseBean<T>> observable,
                                   Consumer<T> success,
                                   boolean showLoading) {

        observable.subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .retry(2) // 默认重试 2 次
                .subscribe(new WrapperSubscriber<>(showLoading) {
                    @Override
                    protected void onSuccess(T data) {
                        super.onSuccess(data);
                        success.accept(data);
                    }
                });
    }


    /**
     * 通用的网络请求封装
     *
     * @param observable  网络请求的 Observable
     * @param success     成功回调
     */
    public  <T> void launchRequest(Observable<HttpBaseBean<T>> observable,
                                   Consumer<T> success) {
        launchRequest(observable,success,true);
    }

    /**
     * 通用的网络请求封装 - 灵活的错误处理
     *
     * @param observable  网络请求的 Observable
     * @param success     成功回调
     * @param flexibleErrorHandler 灵活的错误处理，返回true表示已处理（不执行默认Toast），false表示继续执行默认Toast
     * @param showLoading 是否显示加载框
     */
    public <T> void launchRequestWithFlexibleError(Observable<HttpBaseBean<T>> observable,
                                                   Consumer<T> success,
                                                   FlexibleErrorHandler<T> flexibleErrorHandler,
                                                   boolean showLoading) {

        observable.subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .retry(2) // 默认重试 2 次
                .subscribe(new WrapperSubscriber<>(showLoading) {
                    @Override
                    protected void onSuccess(T data) {
                        super.onSuccess(data);
                        success.accept(data);
                    }

                    @Override
                    protected void onFail(HttpBaseBean data) {
                        boolean handled = false;
                        if (flexibleErrorHandler != null) {
                            try {
                                handled = flexibleErrorHandler.handleError(data);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }

                        if (!handled) {
                            // 执行默认Toast
                            super.onFail(data);
                        }
                    }
                });
    }

    /**
     * 通用的网络请求封装 - 灵活的错误处理（默认显示加载框）
     *
     * @param observable  网络请求的 Observable
     * @param success     成功回调
     * @param flexibleErrorHandler 灵活的错误处理，返回true表示已处理（不执行默认Toast），false表示继续执行默认Toast
     */
    public <T> void launchRequestWithFlexibleError(Observable<HttpBaseBean<T>> observable,
                                                   Consumer<T> success,
                                                   FlexibleErrorHandler<T> flexibleErrorHandler) {
        launchRequestWithFlexibleError(observable, success, flexibleErrorHandler, true);
    }
}
