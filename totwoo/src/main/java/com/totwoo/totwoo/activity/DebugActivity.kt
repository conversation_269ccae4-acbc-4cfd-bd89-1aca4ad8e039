package com.totwoo.totwoo.activity

import android.os.Bundle
import android.widget.Toast
import com.totwoo.library.util.LogUtils
import com.totwoo.library.util.ext.click
import com.totwoo.totwoo.R
import com.totwoo.totwoo.ble.BluetoothManage
import com.totwoo.totwoo.databinding.ActivityDebugBinding
import com.totwoo.totwoo.utils.CommonUtils

/**
 * @des:
 * <AUTHOR>
 * @date 2024/12/20 11:42
 */
class DebugActivity : BaseActivity() {
    private var  binding: ActivityDebugBinding?= null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDebugBinding.inflate(layoutInflater)
        setContentView(binding?.root)

        binding?.btn02?.click {
            BluetoothManage.getInstance().sleep(0x02)
        }

        binding?.btn03?.click {
            BluetoothManage.getInstance().sleep(0x03)
        }

        binding?.btn04?.click {
            BluetoothManage.getInstance().sleep(0x04)
        }

        binding?.btn05?.click {
            CommonUtils.shareXLog(this);
        }


    }


    override fun initTopBar() {
        super.initTopBar()
        setTopBackIcon(R.drawable.back_icon_black)
        setTopTitle("调式")
        setTopLeftOnclik { finish() }
        setSpinState(false)
    }
}