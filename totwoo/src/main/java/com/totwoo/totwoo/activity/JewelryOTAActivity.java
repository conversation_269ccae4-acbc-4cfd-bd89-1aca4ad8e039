package com.totwoo.totwoo.activity;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;

import com.airbnb.lottie.LottieComposition;
import com.airbnb.lottie.LottieCompositionFactory;
import com.airbnb.lottie.LottieTask;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.data.JewelryOnlineDataManager;
import com.totwoo.totwoo.databinding.ActivityJewelryOtaBinding;
import com.totwoo.totwoo.service.DfuService;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CustomDialog;
import com.umeng.analytics.MobclickAgent;

import java.io.File;

import butterknife.ButterKnife;
import no.nordicsemi.android.dfu.DfuBaseService;
import no.nordicsemi.android.dfu.DfuProgressListener;
import no.nordicsemi.android.dfu.DfuProgressListenerAdapter;
import no.nordicsemi.android.dfu.DfuServiceInitiator;
import no.nordicsemi.android.dfu.DfuServiceListenerHelper;

/**
 * Created by totwoo on 2018/10/8.
 */

public class JewelryOTAActivity extends BaseActivity implements BluetoothManage.BluetoothOTAListener {
    public static final String BLE_OTA_FILE_PATH_TAG = "ble_ota_file_path";
    public static final int BLE_OTA_RETRY_TIMES = 2;
    private static final String TAG = "JewelryOTAActivity";

    private ActivityJewelryOtaBinding binding;

    private boolean isLowPower;
    private boolean otaOK;
    public static final String IS_LOW_POWER = "is_low_power";
    /**
     * 固件文件路径
     */
    private String path;
    private boolean isShowFailDialog;
    private LottieComposition lottieUpdatingComposition;

    /**
     * ota 失败后, 重试次数
     */
    private int otaRetryTimes;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityJewelryOtaBinding.inflate(LayoutInflater.from(this));
        setContentView(binding.getRoot());

        ButterKnife.bind(this);
        setSpinState(false);

        isLowPower = getIntent().getBooleanExtra(IS_LOW_POWER, false);

        if (isLowPower) {
            binding.otaLaterBtn.setVisibility(View.GONE);

            binding.otaKnowBtn.setText(R.string.i_know);
            binding.otaKnowBtn.setOnClickListener(v -> finish());
            binding.otaDetailTv.setText(R.string.low_batter_cant_ota_battery);
        } else {
            path = getIntent().getStringExtra(BLE_OTA_FILE_PATH_TAG);

//            binding.otaNoteInfoTv.setVisibility(View.VISIBLE);

            if (!TextUtils.isEmpty(S.updateString)) {
                binding.otaDetailTv.setText(S.updateString);
            } else {
                binding.otaDetailTv.setText(R.string.ota_title_info0);
            }

            BluetoothManage.getInstance().setOTAScanListener(JewelryOTAActivity.this);
            DfuServiceListenerHelper.registerLogListener(this, (deviceAddress, level, message) -> {
                if (level >= DfuBaseService.LOG_LEVEL_WARNING) {
                    LogUtils.e(TAG, message);
                } else {
                    LogUtils.d(TAG, message);
                }
            });
            DfuServiceListenerHelper.registerProgressListener(this,
                    mDfuProgressListener);

            binding.otaLaterBtn.setVisibility(View.GONE);
            binding.otaLaterBtn.setOnClickListener(v -> finish());

            binding.otaKnowBtn.setText(R.string.ota_update);
            binding.otaKnowBtn.setOnClickListener(v -> {
                BluetoothManage.getInstance().enterOtaMode();

                binding.otaKnowBtn.setVisibility(View.INVISIBLE);
                binding.animationView.setVisibility(View.VISIBLE);
                binding.otaMainIv.setVisibility(View.GONE);

//                binding.otaNoteInfoTv.setText(R.string.ota_note_info_updating);
                binding.otaDetailTv.setText(R.string.ota_detail_info_updating);

                mHandler.postDelayed(this::startScan, 1000);
            });
        }
    }

    /**
     * 设置蓝牙搜索状态， 开始搜索自动设置倒计时
     */
    public void startScan() {
        if (!PermissionUtil.hasBluetoothPermission(this)) {
            finish();
            return;
        }

        BluetoothManage.getInstance().disconnectCurrent();
        BluetoothManage.getInstance().scanOTA();
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (isFinishing()) {
            if (otaOK) {
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FIREWARE_UPDATE_SUCCESS);
                ToastUtils.showLong(this, R.string.ota_success);
            }
            BluetoothManage.getInstance().setOTAScanned(false);
            stopService(new Intent(JewelryOTAActivity.this, DfuService.class));
            DfuServiceListenerHelper.unregisterProgressListener(this,
                    mDfuProgressListener);
            BluetoothManage.getInstance().setOTA(false);
        }
    }

    private void showFailDialog(int errorType, String message) {
        final CustomDialog customDialog = new CustomDialog(JewelryOTAActivity.this);
        String msg = getString(R.string.ota_failed);
        if (ToTwooApplication.isDebug) {
            msg += "(" + errorType + ")";
        }
        customDialog.setMessage(msg);
        customDialog.setTitle(R.string.tips);
        customDialog.setCanceledOnTouchOutside(false);
        customDialog.setOnKeyListener((dialog, keyCode, event) -> true); // 屏蔽返回键
        customDialog.setPositiveButton(R.string.ota_failed_retry, v -> {
            startActivity(new Intent(JewelryOTAActivity.this, JewelryInfoActivity.class));
            customDialog.dismiss();
            finish();
        });
        customDialog.show();
    }

    /**
     * 开始 OTA 升级
     */
    private void startOTA(String  address) {
        com.tencent.mars.xlog.Log.e(TAG, "OTA，address: " + address);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            DfuServiceInitiator.createDfuNotificationChannel(JewelryOTAActivity.this);
        }
        DfuServiceInitiator starter = new DfuServiceInitiator(address)
                .setKeepBond(false)
                .setForceDfu(true)
                .setPacketsReceiptNotificationsEnabled(true)
                .setForeground(false);

        starter.setDisableNotification(true);

        starter.setZip(CommonUtils.getUriForFile(JewelryOTAActivity.this, new File(path)));

        if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.O) {
            //	starter.setPacketsReceiptNotificationsValue(DfuServiceInitiator.DEFAULT_PRN_VALUE);
            starter.setPacketsReceiptNotificationsValue(10);
        } else {
            starter.setPacketsReceiptNotificationsValue(4);
        }

        starter.start(JewelryOTAActivity.this, DfuService.class);
    }

    /**
     * 屏蔽返回键
     */
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (!isLowPower && keyCode == KeyEvent.KEYCODE_BACK) {
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @SuppressLint("SetTextI18n")
    private final DfuProgressListener mDfuProgressListener = new DfuProgressListenerAdapter() {
        @Override
        public void onDeviceConnecting(@NonNull final String deviceAddress) {
            Log.d(TAG, "onDeviceConnecting() called with: deviceAddress = [" + deviceAddress + "]");
        }

        @Override
        public void onDfuProcessStarting(@NonNull final String deviceAddress) {
            Log.d(TAG, "onDfuProcessStarting() called with: deviceAddress = [" + deviceAddress + "]");
        }

        @Override
        public void onEnablingDfuMode(@NonNull final String deviceAddress) {
            Log.d(TAG, "onEnablingDfuMode() called with: deviceAddress = [" + deviceAddress + "]");
        }

        @Override
        public void onFirmwareValidating(@NonNull final String deviceAddress) {
            Log.d(TAG, "onFirmwareValidating() called with: deviceAddress = [" + deviceAddress + "]");
        }

        @Override
        public void onDeviceDisconnecting(final String deviceAddress) {
            Log.d(TAG, "onDeviceDisconnecting() called with: deviceAddress = [" + deviceAddress + "]");
        }

        @Override
        public void onDfuCompleted(@NonNull final String deviceAddress) {
            Log.d(TAG, "onDfuCompleted() called with: deviceAddress = [" + deviceAddress + "]");
            // let's wait a bit until we cancel the notification. When canceled
            // immediately it will be recreated by service again.
            binding.otaProgressTv.setText(100 + "%");
            // 固件升级完成
            otaOK = true;
            BluetoothManage.getInstance().setOTA(false);
            JewelryOnlineDataManager.getInstance().resetConnectedJewInfo();
            otaRetryTimes = 0;
            finish();
        }

        @Override
        public void onDfuAborted(@NonNull final String deviceAddress) {
            Log.d(TAG, "onDfuAborted() called with: deviceAddress = [" + deviceAddress + "]");
            // let's wait a bit until we cancel the notification. When canceled
            // immediately it will be recreated by service again.
            otaError(-1, "message");
            otaRetryTimes = 0;
        }

        @Override
        public void onProgressChanged(@NonNull final String deviceAddress,
                                      final int percent, final float speed, final float avgSpeed,
                                      final int currentPart, final int partsTotal) {
            Log.d(TAG, "onProgressChanged() called with: deviceAddress = [" + deviceAddress + "], percent = [" + percent + "], speed = [" + speed + "], avgSpeed = [" + avgSpeed + "], currentPart = [" + currentPart + "], partsTotal = [" + partsTotal + "]");
            if (lottieUpdatingComposition == null) {
                LottieTask<LottieComposition> task = LottieCompositionFactory.fromAsset(JewelryOTAActivity.this, "updating_bluetooth.json");
                task.addListener(result -> {
                    lottieUpdatingComposition = result;
                    binding.animationView.setComposition(result);
                    binding.animationView.setProgress(0.0f);
                    binding.animationView.playAnimation();
                });
                binding.otaProgressTv.setVisibility(View.VISIBLE);
            }
            binding.otaProgressTv.setText(percent + "%");
        }

        @Override
        public void onError(@NonNull final String deviceAddress, final int error,
                            final int errorType, final String message) {
            Log.d(TAG, "onError() called with: deviceAddress = [" + deviceAddress + "], error = [" + error + "], errorType = [" + errorType + "], message = [" + message + "]");

            if (otaRetryTimes < BLE_OTA_RETRY_TIMES) {
                mHandler.postDelayed(()->{
                    otaRetryTimes ++;
                    startOTA(deviceAddress);
                }, 1000);
                return;
            }
            otaError(errorType, message);
            otaRetryTimes = 0;
        }
    };

    private void otaError(int errorType, String message) {
        if (!isShowFailDialog) {
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FIREWARE_UPDATE_FAIL);
            showFailDialog(errorType, message);
            isShowFailDialog = true;

            binding.animationView.setVisibility(View.GONE);
            binding.otaProgressTv.setVisibility(View.GONE);
            binding.otaMainIv.setVisibility(View.VISIBLE);
            binding.otaMainIv.setImageResource(R.drawable.ota_update_fail);
        }
    }

    @Override
    public void onOTADeviceScanned(BluetoothDevice device) {
        BluetoothManage.getInstance().getBondedDevices();

        mHandler.postDelayed(() -> startOTA(device.getAddress()), 1000);
    }

    @Override
    public void onOTATimeOut() {
        otaError(-2, "");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        BluetoothManage.getInstance().setOTA(false);
    }
}
