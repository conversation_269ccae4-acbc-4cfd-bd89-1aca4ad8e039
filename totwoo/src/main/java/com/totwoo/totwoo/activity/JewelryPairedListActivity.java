package com.totwoo.totwoo.activity;

import static com.totwoo.totwoo.activity.CallRemindSetActivity.ALL_CALL_REMIND_SWITCH_KEY;
import static com.totwoo.totwoo.activity.CallRemindSetActivity.IMPORTANT_CONTACT_REMIND_SWITCH_KEY;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.airbnb.lottie.LottieAnimationView;
import com.blankj.utilcode.util.Utils;
import com.bumptech.glide.Glide;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.library.exception.DbException;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.activity.homeActivities.HomeActivityControl;
import com.totwoo.totwoo.bean.JewelryOnlineDataBean;
import com.totwoo.totwoo.bean.LocalHttpJewelryInfo;
import com.totwoo.totwoo.bean.LocalJewelryInfo;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BleUtils;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.data.JewelryOnlineDataManager;
import com.totwoo.totwoo.utils.ACache;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.LocalJewelryDBHelper;
import com.totwoo.totwoo.utils.NetUtils;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.WrapperSubscriber;
import com.totwoo.totwoo.widget.CommonMiddleDialog;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

public class JewelryPairedListActivity extends BaseActivity implements SubscriberListener {
    public static String SELECTED_JEWELRY = "selected_jewelry";
    public static String SELECTED_ADDRESS = "selected_address";
    @BindView(R.id.paired_list_rv)
    RecyclerView mInfoRv;
    @BindView(R.id.jewelry_paired_list_lav)
    LottieAnimationView mJewelryListLav;
    @BindView(R.id.jewelry_paired_change_cl)
    ConstraintLayout mPairedCl;

    private LocalJewelryInfoAdapter localJewelryInfoAdapter;
    private ArrayList<LocalJewelryInfo> infos = new ArrayList<>();
    private View footView;
    /**
     * 要切换的首饰信息
     */
    private LocalJewelryInfo changeJewelryInfo;
    private LocalJewelryInfo currentJewelryInfo;
    private boolean changeSuccess;
    private ACache aCache;

    private boolean isNfcSelect;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_jewelry_paired_list);
        ButterKnife.bind(this);

        mPairedCl.setOnClickListener(v -> {
            //消费点击事件
        });
        mInfoRv.setLayoutManager(new LinearLayoutManager(JewelryPairedListActivity.this, RecyclerView.VERTICAL, false));
        footView = getLayoutInflater().inflate(R.layout.jewelry_paired_footer_view, null);
        footView.findViewById(R.id.jewelry_paired_footer_main_cl).setOnClickListener(v -> {
            showAddDialog();
        });
        aCache = ACache.get(JewelryPairedListActivity.this);
        footView.findViewById(R.id.jewelry_paired_footer_more_tv).setOnClickListener(v -> WebViewActivity.loadUrl(JewelryPairedListActivity.this, HttpHelper.getStaticWebUrl(HttpHelper.HOSTURL_WEB_STORE), false));

        localJewelryInfoAdapter = new LocalJewelryInfoAdapter(R.layout.jewelry_paired_item, infos);
        localJewelryInfoAdapter.addFooterView(footView);
        localJewelryInfoAdapter.setEnableLoadMore(false);
        mInfoRv.setAdapter(localJewelryInfoAdapter);

        HttpHelper.multiJewelryService.getJewelryList()
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new WrapperSubscriber<>(false) {
                    @Override
                    protected void onSuccess(List<LocalHttpJewelryInfo> listHttpBaseBean) {
                        LocalJewelryDBHelper.getInstance().deleteAllBean();
                        if (listHttpBaseBean != null && !listHttpBaseBean.isEmpty()) {
                            for (LocalHttpJewelryInfo info : listHttpBaseBean) {
                                LocalJewelryInfo localJewelryInfo = new LocalJewelryInfo(info.getMac_address(), info.getDevice_name(), info.getIs_select(), info.getCreate_time() * 1000);
                                LocalJewelryDBHelper.getInstance().addBean(localJewelryInfo);
                            }
                            setRvInfo();
                        }
                    }
                });

        setRvInfo();
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);
        setTopLeftOnclik(v -> goNext());
        setTopTitle(R.string.jewelry_info_totwoo);
        setSpinState(false);

        switchTopRight();
    }

    private void switchTopRight() {
        if (isNfcSelect) {
            getTopRightTv().setVisibility(View.GONE);
        } else {
//            setTopRightString(R.string.need_help);
//            setTopRightOnClick(v -> startActivity(new Intent(JewelryPairedListActivity.this, ConnectHelpActivity.class)));
        }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        goNext();
    }

    private void goNext() {
        if (changeSuccess) {
            LogUtils.e("HomeActivityControl");
            HomeActivityControl.getInstance().connectJew(JewelryPairedListActivity.this);
        } else {
            finish();
        }
    }

    private void showLoading() {
        mPairedCl.setVisibility(View.VISIBLE);
        mJewelryListLav.playAnimation();
    }

    private void goneLoading() {
        mPairedCl.setVisibility(View.GONE);
        mJewelryListLav.pauseAnimation();
    }

    public class LocalJewelryInfoAdapter extends BaseQuickAdapter<LocalJewelryInfo, BaseViewHolder> {

        public LocalJewelryInfoAdapter(int layoutResId, @Nullable List<LocalJewelryInfo> data) {
            super(layoutResId, data);
        }

        @Override
        protected void convert(BaseViewHolder helper, LocalJewelryInfo item) {
            String jewName = item.getName();
            JewelryOnlineDataBean jewData = JewelryOnlineDataManager.getInstance().getJewInfoByName(jewName);
            Glide.with(helper.itemView).load(jewData.getImgUrl()).placeholder(BleParams.getJewelryResourceId(jewName)).error(BleParams.getJewelryResourceId(jewName)).into((ImageView) helper.getView(R.id.jewelry_paired_iv));
            helper.setText(R.id.jewelry_paired_name_tv, TextUtils.isEmpty(jewData.getTitle()) ? BleParams.getTranName(jewName, JewelryPairedListActivity.this) : jewData.getTitle());

            if (Apputils.systemLanguageIsChinese(JewelryPairedListActivity.this)) {
                TextView switchTv = helper.getView(R.id.jewelry_paired_switch_tv);
                ConstraintLayout.LayoutParams layoutParams = new ConstraintLayout.LayoutParams(CommonUtils.dip2px(JewelryPairedListActivity.this, 60), CommonUtils.dip2px(JewelryPairedListActivity.this, 30));
                layoutParams.verticalBias = 0.5f;
                layoutParams.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID;
                layoutParams.topToTop = ConstraintLayout.LayoutParams.PARENT_ID;
                layoutParams.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
                layoutParams.setMargins(0, 0, CommonUtils.dip2px(JewelryPairedListActivity.this, 23), 0);
                switchTv.setLayoutParams(layoutParams);
            }
            if (item.getIs_select() == 1) {
                currentJewelryInfo = item;
                helper.setVisible(R.id.jewelry_paired_info_tv, true);
                helper.setTextColor(R.id.jewelry_paired_info_tv, getResources().getColor(R.color.text_color_gray_99));
                helper.setText(R.id.jewelry_paired_info_tv, setCurrentStatusText());
                helper.setVisible(R.id.jewelry_paired_switch_tv, false);
                helper.setVisible(R.id.jewelry_paired_delete_iv, false);
                helper.setVisible(R.id.jewelry_paired_cover_view, false);
                helper.setTextColor(R.id.jewelry_paired_name_tv, getResources().getColor(R.color.black));
                helper.getView(R.id.jewelry_paired_main_cl).setOnClickListener(v -> startActivity(new Intent(JewelryPairedListActivity.this, JewelryInfoActivity.class).putExtra(SELECTED_JEWELRY, item.getName()).putExtra(SELECTED_ADDRESS, item.getMac_address())));

                isNfcSelect = BleParams.isNfcJewelry(item.getName());
                switchTopRight();
            } else {
                helper.setVisible(R.id.jewelry_paired_info_tv, false);
                helper.setVisible(R.id.jewelry_paired_date_tv, false);
                helper.setVisible(R.id.jewelry_paired_switch_tv, true);
                helper.setVisible(R.id.jewelry_paired_delete_iv, true);
                helper.setVisible(R.id.jewelry_paired_cover_view, true);
                helper.setTextColor(R.id.jewelry_paired_name_tv, getResources().getColor(R.color.text_color_gray_99));
                helper.getView(R.id.jewelry_paired_main_cl).setOnClickListener(v -> changeJewelry(item));
                helper.getView(R.id.jewelry_paired_delete_iv).setOnClickListener(v -> showDeleteDialog(item));
            }
        }
    }

    @EventInject(eventType = S.E.E_UPDATE_JEWERLY_STATUS_CHANGE, runThread = TaskType.UI)
    public void notifyJewelryState(EventData data) {
        localJewelryInfoAdapter.notifyDataSetChanged();
    }

    private String setCurrentStatusText() {
        if (BleParams.isNfcJewelry(null)) {
            return getString(R.string.bounded);
        } else if (JewInfoSingleton.getInstance().getConnectState() == JewInfoSingleton.STATE_CONNECTED) {
            return getString(R.string.totwoo_connected);
        } else if (JewInfoSingleton.getInstance().getConnectState() == JewInfoSingleton.STATE_RECONNECTING) {
            return getString(R.string.totwoo_connecting);
        } else {
            return getString(R.string.totwoo_disconnected);
        }
    }

    /**
     * 展示请求蓝牙开启的对话框
     */
    public void showBluetoothDialog() {
        final CommonMiddleDialog dialog = new CommonMiddleDialog(this);
        dialog.setMessage(R.string.request_open_bluetooth);
        dialog.setSure(R.string.allow, v -> {
            BleUtils.enableBlueTooth(JewelryPairedListActivity.this);
            dialog.dismiss();
        });
        dialog.setCancel(R.string.cancel);

        dialog.show();
    }

    private void changeJewelry(LocalJewelryInfo info) {
        //判断蓝牙和网络
        if (!BleUtils.isBlEEnable(this) && BleParams.isBluetoothJewelry(null)) {
            showBluetoothDialog();
            return;
        }
        if (!NetUtils.isConnected(this)) {
            ToastUtils.showShort(this, R.string.error_net);
            return;
        }

        boolean fromNfc2Ble = currentJewelryInfo != null
                && BleParams.isNfcJewelry(currentJewelryInfo.getName())
                && BleParams.isBluetoothJewelry(info.getName());

        //解绑当前首饰
        showChangeCheckDialog(fromNfc2Ble, info);
    }

    private CommonMiddleDialog changeDialog;

    private void showChangeCheckDialog(boolean fromNfc2Ble, LocalJewelryInfo info) {
        changeDialog = new CommonMiddleDialog(this);
        changeDialog.setMessage(fromNfc2Ble ? R.string.jewelry_list_add_change_nfc : R.string.jewelry_list_add_change);
        changeDialog.setSure(fromNfc2Ble ? R.string.i_know : R.string.confirm, v -> {
            httpChangeJewelry(info);
            changeDialog.dismiss();
        });
        changeDialog.setCancel(R.string.cancel);
        changeDialog.show();
    }

    private void httpChangeJewelry(LocalJewelryInfo changeJewelryInfo) {

        this.changeJewelryInfo = changeJewelryInfo;
        showLoading();

        // 使用新的网络请求接口 - 调bind接口
        launchRequestWithFlexibleError(
                HttpHelper.commonService.bindState(changeJewelryInfo.getName(), BleParams.isNfcJewelry(changeJewelryInfo.getName()) ? changeJewelryInfo.getMac_address() : "", changeJewelryInfo.getMac_address(), "connect"),
                data -> {
                    // 嵌套请求 - 更新首饰信息
                    launchRequestWithFlexibleError(
                            HttpHelper.multiJewelryService.updateJewelry(BleParams.isNfcJewelry(changeJewelryInfo.getName()) ? changeJewelryInfo.getMac_address() : "", changeJewelryInfo.getMac_address(), changeJewelryInfo.getName(), "", "", 1, ""),
                            updateData -> {
                                BluetoothManage.getInstance().unPair();
                                changeSuccess = true;
                                //更改数据库
                                LocalJewelryDBHelper.getInstance().setSelected(changeJewelryInfo.getMac_address());
                                setRvInfo();

                                com.etone.framework.event.EventBus.onPostReceived(S.E.E_UPDATE_EMOTION, null);

                                //给一个标识，让连接之后不再存一遍name和address信息
                                PreferencesUtils.put(JewelryPairedListActivity.this, BleParams.PAIRED_CHANGE_CONNECT, true);
                                if (BleParams.isButtonBatteryJewelry()) {
                                    PreferencesUtils.put(Utils.getApp(), ALL_CALL_REMIND_SWITCH_KEY, false);
                                    PreferencesUtils.put(Utils.getApp(), IMPORTANT_CONTACT_REMIND_SWITCH_KEY, true);
                                }
                            },
                            fail -> {
                                goneLoading();
                                return false;
                            },
                            false // 不显示加载框
                    );
                },
                fail -> {
                    goneLoading();
                    return false;
                },
                false
        );
    }

    private CommonMiddleDialog addDialog;

    private void showAddDialog() {
        if (addDialog == null) {
            addDialog = new CommonMiddleDialog(this);
            addDialog.setMessage(R.string.jewelry_list_add_hint);
            addDialog.setSure(R.string.jewelry_list_add_sure, v -> {
                startActivity(new Intent(JewelryPairedListActivity.this, JewelrySelectActivity.class));
                addDialog.dismiss();
            });
            addDialog.setCancel(R.string.cancel);
        }
        addDialog.show();
    }

    private void setRvInfo() {
        try {
            infos = (ArrayList<LocalJewelryInfo>) LocalJewelryDBHelper.getInstance().getAllBeans();
        } catch (DbException e) {
            e.printStackTrace();
        }
        localJewelryInfoAdapter.setNewData(infos);
    }

    //切换，解绑成功
    @EventInject(eventType = S.E.E_JEWERLY_APART_FAIL, runThread = TaskType.UI)
    public void jewrlyApartFail(EventData data) {
        //把切换Sp的名称和address
        PreferencesUtils.put(JewelryPairedListActivity.this, BleParams.PAIRED_BLE_ADRESS_TAG, changeJewelryInfo.getMac_address());
        PreferencesUtils.put(JewelryPairedListActivity.this, BleParams.PAIRED_JEWELRY_NAME_TAG, changeJewelryInfo.getName());

        goneLoading();
        ToastUtils.showLong(Utils.getApp(), "unbind fail,try again");
    }

    @EventInject(eventType = S.E.E_UPDATE_JEWERLY_APART, runThread = TaskType.UI)
    public void jewrlyApartSuccess(EventData data) {
        //把切换Sp的名称和address
        PreferencesUtils.put(JewelryPairedListActivity.this, BleParams.PAIRED_BLE_ADRESS_TAG, changeJewelryInfo.getMac_address());
        PreferencesUtils.put(JewelryPairedListActivity.this, BleParams.PAIRED_JEWELRY_NAME_TAG, changeJewelryInfo.getName());

        //重连
        BluetoothManage.getInstance().reconnect(false);
        com.etone.framework.event.EventBus.onPostReceived(S.E.E_CUSTOM_ORDER_UPDATE, null);
        goneLoading();
    }

    @EventInject(eventType = S.E.E_UPDATE_PAIRED_STATE, runThread = TaskType.UI)
    public void removeJewelrySuccess(EventData data) {
        setRvInfo();
    }

    @Override
    protected void onResume() {
        super.onResume();
        InjectUtils.injectOnlyEvent(this);
    }

    @Override
    protected void onPause() {
        super.onPause();
        InjectUtils.injectUnregisterListenerAll(this);
    }


    private void httpDelete(LocalJewelryInfo item) {
        String address = item.getMac_address();

        showProgressDialog();
        // 使用新的网络请求接口 - 调bind接口解绑
        launchRequestWithFlexibleError(
                HttpHelper.commonService.bindState(item.getName(), BleParams.isNfcJewelry(item.getName()) ? item.getMac_address() : "", item.getMac_address(), "relieve"),
                data -> {
                    // 嵌套请求 - 删除首饰
                    launchRequestWithFlexibleError(
                            HttpHelper.multiJewelryService.deleteJewelry(address, "", item.getName()),
                            deleteData -> {
                                dismissProgressDialog();
                                LocalJewelryDBHelper.getInstance().deleteBean(address);
                                setRvInfo();
                            }, fail -> {
                                dismissProgressDialog();
                                return false;
                            }
                    );
                }, fail -> {
                    dismissProgressDialog();
                    return false;
                },
                false);
    }

    private void showDeleteDialog(LocalJewelryInfo info) {
        CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(this);
        commonMiddleDialog.setMessage(R.string.jewelry_list_delete_hint);
        commonMiddleDialog.setSure(R.string.jewelry_list_delete_confirm, v -> {
            httpDelete(info);
            commonMiddleDialog.dismiss();
        });
        commonMiddleDialog.setCancel(R.string.cancel);
        commonMiddleDialog.show();
    }

    @EventInject(eventType = S.E.E_IMEI_CHARGE_SUCCEED, runThread = TaskType.UI)
    private void imeiChargeSucceed() {
        localJewelryInfoAdapter.notifyDataSetChanged();
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }
}
