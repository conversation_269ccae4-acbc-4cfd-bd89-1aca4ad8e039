package com.totwoo.totwoo.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.library.exception.DbException;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.homeActivities.HomeActivityControl;
import com.totwoo.totwoo.activity.homeActivities.HomeBaseActivity;
import com.totwoo.totwoo.bean.LocalHttpJewelryInfo;
import com.totwoo.totwoo.bean.LocalJewelryInfo;
import com.totwoo.totwoo.bean.LoginInfoBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.receiver.JpushReceiver;
import com.totwoo.totwoo.tim.TimInitBusiness;
import com.totwoo.totwoo.utils.ApiException;
import com.totwoo.totwoo.utils.CaptchaController;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.LocalJewelryDBHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CustomProgressBarDialog;
import com.umeng.analytics.MobclickAgent;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.jpush.android.api.JPushInterface;
import cn.jpush.android.api.TagAliasCallback;
import rx.Subscriber;

@Deprecated
public class LoginAndPasswordActivity extends BaseActivity implements SubscriberListener {
    @BindView(R.id.login_country_code_tv)
    TextView mCountryCodeTv;
    @BindView(R.id.login_phone_et)
    EditText mPhoneEt;
    @BindView(R.id.login_code_et)
    EditText mCodeEt;
    @BindView(R.id.login_count_down_tv)
    TextView countDownTv;
    @BindView(R.id.login_get_voice_tv)
    TextView mVoiceCodeTv;
    @BindView(R.id.login_login_tv)
    TextView mLoginTv;
    @BindView(R.id.login_phone_clear_iv)
    ImageView login_phone_clear_iv;
    @BindView(R.id.login_password_login_tv)
    TextView mLoginPasswordTv;
    @BindView(R.id.parent_view)
    ConstraintLayout parent_view;

    /**
     * 标记验证码可重复获取的倒计时器，界面销毁是要停止计时
     */
    private CountDownTimer cuntTimer;

    private boolean videoPlayed = false;
    private String country_code_value;

    private CustomProgressBarDialog loadingDialog;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.activity_login_and_password);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);

        country_code_value = Apputils.getSystemLanguageCountryCode(this);

        mCountryCodeTv.setText("+" + country_code_value);

        mPhoneEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (!TextUtils.isEmpty(s.toString())) {
                    login_phone_clear_iv.setVisibility(View.VISIBLE);
                } else {
                    login_phone_clear_iv.setVisibility(View.INVISIBLE);
                }
            }
        });
        mCodeEt.setOnEditorActionListener((textView, id, keyEvent) -> {
            if (id == 1024 || id == EditorInfo.IME_NULL) {
                doLogin();
                return true;
            }
            return false;
        });

        setSpinState(false);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (cuntTimer != null) {
            cuntTimer.cancel();
        }
        InjectUtils.injectUnregisterListenerAll(this);
    }

    @OnClick({R.id.login_country_code_tv, R.id.login_phone_clear_iv, R.id.login_login_tv, R.id.login_password_login_tv,
            R.id.login_get_voice_tv, R.id.login_count_down_tv})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.login_country_code_tv:
                Intent intent = new Intent(this, CountryCodeListActivity.class);
                startActivityForResult(intent, 0);
                // 切换动画
                overridePendingTransition(R.anim.activity_fade_in,
                        R.anim.activity_fade_out);
                break;
            case R.id.login_phone_clear_iv:
                mPhoneEt.setText("");
                break;
            case R.id.login_login_tv:
                doLogin();
                break;
            case R.id.login_password_login_tv:
                String phone = mPhoneEt.getText().toString();
                Intent passwordIntent = new Intent(this, PasswordLoginActivity.class);
                if (!TextUtils.isEmpty(phone)) {
                    passwordIntent.putExtra(PasswordLoginActivity.PHONENUMBER, phone);
                }
                startActivity(passwordIntent);
                break;
            case R.id.login_get_voice_tv:
                getVoiceCode();
                break;
            case R.id.login_count_down_tv:
                getMsgCode();
                break;
        }
    }

    private boolean isMsgHaving;

    private void getMsgCode() {
        if (isVoiceHaving) {
            ToastUtils.showLong(LoginAndPasswordActivity.this, R.string.voice_is_having);
            return;
        }
        if (isMsgHaving) {
            ToastUtils.showLong(LoginAndPasswordActivity.this, R.string.verification_too_ofen);
            return;
        }
        String phone = mPhoneEt.getText().toString();

        if (TextUtils.isEmpty(phone)) {
            ToastUtils.showLong(this, R.string.error_invalid_phone);
        } else if (!isPhoneValid(phone)) {
            ToastUtils.showLong(this, R.string.error_incorrect_phone);
        } else {
            CommonUtils.phoneSmsPre0CheckAndDo(this, country_code_value, phone, () -> {
                isMsgHaving = true;
                CaptchaController.getInstance().checkAndShowCaptcha(this, "pwd",country_code_value + phone, country_code_value, (ret, ticket, randomStr) -> {
                    if (ret == 0) {
                        requestSms(country_code_value + phone, ticket, randomStr);
                    } else {
                        isMsgHaving = false;
                        if (ret != -10001){
                            ToastUtils.showLong(this, R.string.verification_failed);
                        }
                    }
                });
            });
        }
    }

    private void requestSms(String phone, String ticket, String randomStr) {
        int time = (int) (System.currentTimeMillis() / 1000);
        String sourceStr = time + "+" + phone + "+" + "totwoo_safe_202311";

        String firmwareType = null;
        if (ticket != null && ticket.startsWith(BleParams.COMMON_JEWELEY_PRE)) {
            firmwareType = ticket;
            ticket = "";
        }

        HttpHelper.loginV3Service.getSmsT(phone, time, "", CommonUtils.md5(sourceStr), firmwareType, ticket, randomStr)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Subscriber<HttpBaseBean<String>>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        if (loadingDialog == null) {
                            loadingDialog = new CustomProgressBarDialog(LoginAndPasswordActivity.this);
                        }
                        loadingDialog.show();
                    }

                    @Override
                    public void onCompleted() {
                        if (loadingDialog != null && loadingDialog.isShowing()) {
                            loadingDialog.dismiss();
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShort(LoginAndPasswordActivity.this, R.string.error_net);
                        isMsgHaving = false;
                    }

                    @Override
                    public void onNext(HttpBaseBean<String> stringHttpBaseBean) {
                        if (stringHttpBaseBean.getErrorCode() == 0) {
                            startCountdown();

                            etGetFoucs();
                            ToastUtils.showLong(LoginAndPasswordActivity.this,
                                    R.string.vercode_has_send);
                        } else if (stringHttpBaseBean.getErrorCode() == 800) {
                            // 临时验证码
                            String msg = stringHttpBaseBean.getErrorMsg();
                            ToastUtils.showLong(LoginAndPasswordActivity.this, msg);

                            String code = CommonUtils.extractVerCode(msg);

                            if (!TextUtils.isEmpty(code)) {
                                mCodeEt.setText(code);
                            }

                            etGetFoucs();
                            startCountdown();
                        } else {
                            String errMsg = ApiException.getHttpErrMessage(stringHttpBaseBean.getErrorCode(), stringHttpBaseBean.getErrorMsg());
                            ToastUtils.showLong(LoginAndPasswordActivity.this, errMsg);
                            isMsgHaving = false;
                        }
                    }
                });
    }

    /**
     * 开始获取验证码的倒计时显示， 及时期间禁止点击
     */
    protected void startCountdown() {
        cuntTimer = new CountDownTimer(60 * 1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                countDownTv.setText(String.valueOf(millisUntilFinished / 1000) + getString(R.string.the_second_can_repeat));
            }

            @Override
            public void onFinish() {
                countDownTv.setText(getString(R.string.get_verification_code));
                isMsgHaving = false;
            }
        };
        cuntTimer.start();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        switch (resultCode) {
            case 0:
                if (data != null) {
                    country_code_value = data.getStringExtra(CommonArgs.COUNTRY_CODE_KEY);
                    mCountryCodeTv.setText("+" + country_code_value);
                }
                break;

            default:
                break;
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    /**
     * 验证手机号是否有效
     *
     * @param phone
     * @return
     */
    private boolean isPhoneValid(String phone) {
        if (country_code_value.equals("86")) {
            Pattern p = Pattern.compile("^1\\d{10}$");
            Matcher m = p.matcher(phone);
            return m.matches();
        }
        return true;

    }

    private void etGetFoucs() {
        mCodeEt.setFocusable(true);
        mCodeEt.setFocusableInTouchMode(true);
        mCodeEt.requestFocus();
    }

    private boolean isVoiceHaving;

    private void getVoiceCode() {
        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOGIN_CLICKGETVOIVE);
        if (isVoiceHaving) {
            ToastUtils.show(LoginAndPasswordActivity.this, R.string.verification_too_ofen, Toast.LENGTH_SHORT);
            return;
        }
        if (isMsgHaving) {
            ToastUtils.show(LoginAndPasswordActivity.this, R.string.msg_is_having, Toast.LENGTH_SHORT);
            return;
        }
        String phone = mPhoneEt.getText().toString();

        if (TextUtils.isEmpty(phone)) {
            ToastUtils.showShort(this, R.string.error_invalid_phone);
        } else if (!isPhoneValid(phone)) {
            ToastUtils.showShort(this, R.string.error_incorrect_phone);
        } else {
            // 发送请求获取验证码
            phone = "86" + phone;
            String toMd5 = phone + "+totwoo_safe_sms";
            HttpHelper.loginService.getVoiceVercodeT(phone, CommonUtils.md5(toMd5))
                    .compose(HttpHelper.rxSchedulerHelper())
                    .subscribe(new Subscriber<HttpBaseBean<String>>() {
                        @Override
                        public void onCompleted() {

                        }

                        @Override
                        public void onError(Throwable e) {
                            ToastUtils.showShort(LoginAndPasswordActivity.this, R.string.error_net);
                            isVoiceHaving = false;
                        }

                        @Override
                        public void onNext(HttpBaseBean<String> stringHttpBaseBean) {
                            if (stringHttpBaseBean.getErrorCode() == 0) {
                                isVoiceHaving = true;
                                ToastUtils.showLong(LoginAndPasswordActivity.this, R.string.vercode_voice_send);
                                mHandler.postDelayed(() -> isVoiceHaving = false, 60 * 1000);
                            } else {
                                isVoiceHaving = false;
                                if (stringHttpBaseBean.getErrorMsg() == null || stringHttpBaseBean.getErrorMsg().length() == 0)
                                    ToastUtils.show(LoginAndPasswordActivity.this, getString(R.string.error_net), Toast.LENGTH_SHORT);
                                else
                                    ToastUtils.show(LoginAndPasswordActivity.this, stringHttpBaseBean.getErrorMsg(), Toast.LENGTH_SHORT);
                            }
                        }
                    });
        }
    }

    /**
     * 验证验证码是否有效
     *
     * @param vercode
     * @return
     */
    private boolean isVerCodeValid(String vercode) {
        Pattern p = Pattern.compile("^\\d{4}$");
        Matcher m = p.matcher(vercode);
        return m.matches();
    }

    private void doLogin() {
        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOGIN_CLICKCODELOGIN);
        String phone = mPhoneEt.getText().toString();
        String vercode = mCodeEt.getText().toString();
        if (TextUtils.isEmpty(phone)) {
            ToastUtils.showShort(this, R.string.error_invalid_phone);
        } else if (!isPhoneValid(phone)) {
            ToastUtils.showShort(this, R.string.error_incorrect_phone);
        } else if (TextUtils.isEmpty(vercode)) {
            ToastUtils.showShort(this, R.string.verification_code);
        } else if (!isVerCodeValid(vercode)) {
            ToastUtils.showShort(this, R.string.error_incorrect_vercode);
        } else {
            InputMethodManager inputMethodManager = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            inputMethodManager.hideSoftInputFromWindow(mLoginTv.getWindowToken(), 0);
            String registerId = PreferencesUtils.getString(this, JpushReceiver.REGISTER_ID, "");
            LogUtils.e("registerId = " + registerId);
            String toMd5 = vercode + "+" + country_code_value + phone + "+totwoo_safe";
            HttpHelper.loginService.loginT(country_code_value + phone, vercode, registerId, CommonUtils.md5(toMd5), "")
                    .compose(HttpHelper.rxSchedulerHelper())
                    .subscribe(new Subscriber<HttpBaseBean<LoginInfoBean>>() {
                        @Override
                        public void onStart() {
                            super.onStart();
                            mLoginTv.setClickable(false);

                            if (loadingDialog == null) {
                                loadingDialog = new CustomProgressBarDialog(LoginAndPasswordActivity.this);
                            }
                            loadingDialog.show();
                        }

                        @Override
                        public void onCompleted() {
                            mLoginTv.setClickable(true);
                            if (loadingDialog != null && loadingDialog.isShowing()) {
                                loadingDialog.dismiss();
                            }
                        }

                        @Override
                        public void onError(Throwable e) {
                            ToastUtils.showShort(LoginAndPasswordActivity.this, R.string.error_net);
                            mLoginTv.setClickable(true);
                            if (loadingDialog != null && loadingDialog.isShowing()) {
                                loadingDialog.dismiss();
                            }
                        }

                        @Override
                        public void onNext(HttpBaseBean<LoginInfoBean> loginInfoBeanHttpBaseBean) {
                            if (loginInfoBeanHttpBaseBean.getErrorCode() == 0) {
                                setInfo(loginInfoBeanHttpBaseBean.getData());
                                TimInitBusiness.login();
                                HttpHelper.multiJewelryService.getJewelryList()
                                        .compose(HttpHelper.rxSchedulerHelper())
                                        .subscribe(new Subscriber<HttpBaseBean<List<LocalHttpJewelryInfo>>>() {
                                            @Override
                                            public void onCompleted() {
                                                mLoginTv.setClickable(true);
                                            }

                                            @Override
                                            public void onError(Throwable e) {
                                                ToastUtils.showShort(LoginAndPasswordActivity.this, R.string.error_net);
                                                mLoginTv.setClickable(true);
                                            }

                                            @Override
                                            public void onNext(HttpBaseBean<List<LocalHttpJewelryInfo>> listHttpBaseBean) {
                                                if (listHttpBaseBean.getErrorCode() == 0 && listHttpBaseBean.getData() != null && listHttpBaseBean.getData().size() > 0) {
                                                    LogUtils.e("listHttpBaseBean.getData().size() = " + listHttpBaseBean.getData().size());
                                                    for (LocalHttpJewelryInfo info : listHttpBaseBean.getData()) {
                                                        LogUtils.e("info = " + info);
                                                        LocalJewelryDBHelper.getInstance().addBean(new LocalJewelryInfo(info.getMac_address(), info.getDevice_name(), info.getIs_select(), info.getCreate_time() * 1000));
                                                    }
                                                    try {
                                                        String jewleryInfo = LocalJewelryDBHelper.getInstance().getSelectedBean().toString();
                                                        LogUtils.e("jewleryInfo = " + jewleryInfo);
                                                    } catch (DbException e) {
                                                        LogUtils.e("e = " + e);
                                                        e.printStackTrace();
                                                    }
                                                }
                                                if (loginInfoBeanHttpBaseBean.getData().getPwd_frame() == 0) {
                                                    goNext();
                                                } else {
                                                    startActivity(new Intent(LoginAndPasswordActivity.this, SetPasswordActivity.class).putExtra(CommonArgs.FROM_TYPE, CommonArgs.LOGIN));
                                                    finish();
                                                }
                                            }
                                        });

                            } else if (loginInfoBeanHttpBaseBean.getErrorCode() == 108) {
                                ToastUtils.showShort(LoginAndPasswordActivity.this, R.string.error_incorrect_vercode);
                            } else {
                                ToastUtils.showShort(LoginAndPasswordActivity.this, R.string.error_net);
                            }
                        }
                    });
        }
    }

    private void setInfo(LoginInfoBean data) {
        CommonUtils.setLogin(true);
        ToTwooApplication.owner.setNew(data.getIs_new_user() == 1);
        ToTwooApplication.owner.setTotwooId(data.getTotwoo_id());
        ToTwooApplication.owner.setToken(data.getToken());
        ToTwooApplication.owner.setPhone(data.getMobilephone());
        PreferencesUtils.put(ToTwooApplication.baseContext, CommonArgs.USER_PHONE, data.getMobilephone());
        PreferencesUtils.put(ToTwooApplication.baseContext, CommonArgs.TOKEN, data.getToken());
        ToTwooApplication.owner.setNickName(data.getNick_name());
        ToTwooApplication.owner.setCity(data.getCity());

        ToTwooApplication.owner.setGender(Integer.valueOf(data.getSex()));
        switch (data.getLove_status()) {
            case "S":
                ToTwooApplication.owner.setLoveStatus(1);
                break;
            case "F":
                ToTwooApplication.owner.setLoveStatus(2);
                break;
            case "M":
                ToTwooApplication.owner.setLoveStatus(3);
                break;
            case "SEC":
                ToTwooApplication.owner.setLoveStatus(0);
                break;
        }
        ToTwooApplication.owner.setHeaderUrl(data.getHead_portrait());
        int height = TextUtils.isEmpty(data.getHeight()) ? 0 : Integer.valueOf(data.getHeight());
        ToTwooApplication.owner.setHeight(height);
        int weight = TextUtils.isEmpty(data.getWeight()) ? 0 : Integer.valueOf(data.getWeight());
        ToTwooApplication.owner.setWeight(weight);
        if (TextUtils.isEmpty(data.getBirthday()) || data.getBirthday().startsWith("0000")) {
            ToTwooApplication.owner.setBirthday("1985-06-15");
        } else {
            ToTwooApplication.owner.setBirthday(data.getBirthday());
        }
        int walk_goal;
        if (TextUtils.isEmpty(data.getWalk_goal()) || Integer.valueOf(data.getWalk_goal()) == 0) {
            walk_goal = 8000;
        } else {
            walk_goal = Integer.valueOf(data.getWalk_goal());
        }
        ToTwooApplication.owner.setWalkTarget(walk_goal);

        PreferencesUtils.put(this, StepTargetSettingActivity.STEP_TARGET, walk_goal);
        if (!TextUtils.isEmpty(data.getImei())) {
            PreferencesUtils.put(this, BleParams.SAFE_JEWLERY_IMEI, data.getImei());
        }

        HashSet<String> tags = new HashSet<>(2);
        tags.add(Apputils.systemLanguageIsChinese(this) ? "cn" : "en");

        // 2.1.0 版本之后, 更新推送方式, 清除原有的 Alias, 防止推送异常
        JPushInterface.setAliasAndTags(this, "", tags, new TagAliasCallback() {
                    @Override
                    public void gotResult(int arg0, String arg1, Set<String> arg2) {
                        LogUtils.w("Jpush alias " + arg1 + " set state: " + arg0);
                        if (arg0 == 0) {
//                            ToastUtils.showShortDebug(LoginActivity.this, "设置 标签成功: "
//                                    + arg1);
                            PreferencesUtils.put(LoginAndPasswordActivity.this,
                                    HomeBaseActivity.JPUSH_ALIAS_OK, true);
                        }
                    }
                }
        );
        // 保存当前国家代码
        PreferencesUtils.put(this, CommonArgs.COUNTRY_CODE_KEY, country_code_value);
        if (data.getIs_share() == 1) {
            PreferencesUtils.put(LoginAndPasswordActivity.this, CommonArgs.NEW_USER_BENFIT_SHARE, true);
        }
        //异常退出可能导致bug，临时解决方案
        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.PAIRED_FIRST_CONNECT);
        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.TOTWOO_DEVICE_INFO);
        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG);
        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.PAIRED_BLE_ADRESS_TAG);
    }

    private void goNext() {
        try {
            if (LocalJewelryDBHelper.getInstance().getAllBeans().size() > 0) {
                LocalJewelryInfo info = LocalJewelryDBHelper.getInstance().getSelectedBean();
                PreferencesUtils.put(LoginAndPasswordActivity.this, BleParams.PAIRED_BLE_ADRESS_TAG, info.getMac_address());
                PreferencesUtils.put(LoginAndPasswordActivity.this, BleParams.PAIRED_JEWELRY_NAME_TAG, info.getName());
//                JewInfoSingleton.getInstance().setConnectState(JewInfoSingleton.STATE_DISCONNECTED);
                BluetoothManage.getInstance().startBackgroundScan();
                LogUtils.e("HomeActivityControl");
                HomeActivityControl.getInstance().connectJew(LoginAndPasswordActivity.this);
            } else {
                startActivity(new Intent(this, JewelrySelectActivity.class).putExtra(CommonArgs.FROM_TYPE, CommonArgs.LOGIN));
            }
        } catch (DbException e) {
            e.printStackTrace();
        }
        finish();
    }

    /**
     * 忘记密码验证成功
     * ForgetPasswordCodeActivity
     */
    @EventInject(eventType = S.E.E_LOGIN_SUCCESS, runThread = TaskType.UI)
    public void verifySuccess(EventData data) {
        finish();
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }
}
