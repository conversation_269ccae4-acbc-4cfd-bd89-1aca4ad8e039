package com.totwoo.totwoo.activity;

import android.app.Activity;
import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.URLUtil;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.constraintlayout.widget.Group;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.ClickUtils;
import com.totwoo.library.net.HttpRequest;
import com.totwoo.library.net.RequestParams;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.activity.homeActivities.HomeActivityControl;
import com.totwoo.totwoo.bean.NotifyMessage;
import com.totwoo.totwoo.utils.ApkDownloader;
import com.totwoo.totwoo.utils.DesUtil;
import com.totwoo.totwoo.utils.FileUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.RequestCallBack;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.CustomProgressBarDialog;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;

/**
 * 消息系统, 消息列表界面
 * <p/>
 * Created by lixingmao on 16/3/28.
 */
public class MessageActivity extends BaseActivity implements AdapterView.OnItemLongClickListener {
    /**
     * 每次加载消息条数Ø
     */
    private static final int PRE_LOADING_COUNR = 10;

    private ListView mListView;
    private ArrayList<NotifyMessage> messagelist;
    /**
     * 清除全部消息按钮
     */
    private SwipeRefreshLayout refreshLayout;
    private View footView;
    private MessageAdapter mAdapter;
    private CustomProgressBarDialog progressDialog;

    //  当前数据是缓存数据还是刷新的最新数据
    private boolean isCacheData = true;

    // 无数据时展示的文案
    private RelativeLayout noDataRl;
    /**
     * 下一页对应的页码, 如果为0 ,表示是最后一页
     */
    private int nextPage;

    /**
     * 消息总数
     */
    private int messageCount;
    /**
     * 是否正在加载更多
     */
    private boolean isLoading;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_message);

        mListView = (ListView) findViewById(R.id.message_list);
        refreshLayout = (SwipeRefreshLayout) findViewById(R.id.message_refresh_layout);
        noDataRl = (RelativeLayout) findViewById(R.id.message_no_message_rl);

        mListView.setOnItemLongClickListener(this);

        mListView.addFooterView(getFootView());
        mAdapter = new MessageAdapter(this, getCacheData());
        mListView.setAdapter(mAdapter);


        refreshLayout.setColorSchemeResources(R.color.refresh_ring_color1,
                R.color.refresh_ring_color2,
                R.color.refresh_ring_color3,
                R.color.refresh_ring_color4,
                R.color.refresh_ring_color5);

        refreshLayout.setOnRefreshListener(() -> loadData(1));

        initLoadMoreListener();


        // 进入界面, 开始加载最新数据
        mHandler.postDelayed(() -> {
            loadData(1);
            refreshLayout.setRefreshing(true);
        }, 400);

//        Aria.download(this).register();
//        AriaConfig.getInstance().getDConfig().setThreadNum(1);
    }

    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.message_center_zh);
        setTopRightIcon(R.drawable.delete);
        setTopRightOnClick(v -> showCustomDialog(-1));
    }

    /**
     * 初始化下拉加载更多的
     */
    private void initLoadMoreListener() {
        if (mListView == null) {
            return;
        }

        mListView.setOnScrollListener(new AbsListView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(AbsListView view, int scrollState) {

            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
                if (nextPage > 0 && firstVisibleItem + visibleItemCount > totalItemCount - 3 && !isLoading) {
//                    ToastUtils.showLong(MessageActivity.this, "开始加载更多...");
                    loadData(nextPage);
                    isLoading = true;
                }
            }
        });
    }


    /**
     * 加载缓存数据
     *
     * @return
     */
    private ArrayList<NotifyMessage> getCacheData() {
        return parseData(HttpHelper.getDataCache(HttpHelper.URL_GET_MESSAGE_LIST));
    }

    /**
     * 解析数据
     *
     * @param data
     * @return
     */
    private ArrayList<NotifyMessage> parseData(JSONObject data) {
        ArrayList<NotifyMessage> messageList = new ArrayList<>();
        JSONArray messList = null;
        JSONObject obj = null;
        NotifyMessage message = null;

        // {"message_count":4,"timestamp":1459941599,"next_page":0,"message_list":
        // [{"push_id":2,"user_id":"71","title":"totwoo智能珠宝","read_status":1,"content":"totwoo智能珠宝123",
        // "read_time":0,"create_time":1459355400,"jump_url":"http://www.totwoo.com","custom_data":"{}",
        // "totwoo_id":"8615330058983","message_id":262},]}

        if (data != null) {
            nextPage = data.optInt("next_page");
            messageCount = data.optInt("message_count");
            // 解析对应的数据条目
            if ((messList = data.optJSONArray("message_list")) != null) {
                for (int i = 0; i < messList.length(); i++) {
                    obj = messList.optJSONObject(i);
                    message = new NotifyMessage();
                    message.setId(obj.optInt("message_id"));
                    message.setContent(obj.optString("content"));
                    message.setTitle(obj.optString("title"));
                    message.setTime(obj.optLong("create_time") * 1000);// 服务端以秒为单位
                    message.setUrl(obj.optString("jump_url"));
                    message.setIs_share(obj.optInt("is_share"));
                    message.setStar_name(obj.optString("star_name"));
                    message.setStar_head_portrait(obj.optString("star_head_portrait"));
                    message.setShare_content(obj.optString("share_content"));
                    message.setShare_title(obj.optString("share_title"));
                    message.setIs_full_show(obj.optString("is_full_show"));
                    message.setIsNew(obj.optInt("read_time") == 0);
                    String jumpType = obj.optString("jump_type");
                    if (!com.etone.framework.utils.StringUtils.isEmpty(jumpType) && jumpType.equals("map")) {
                        message.setJumpType(jumpType);
                        message.setCustomData(obj.optString("custom_data"));
                    }
                    messageList.add(message);
                }
            }
        }
        return messageList;
    }

    /**
     * 加载数据, 如果页码为1, 即表示刷新操作, 清空原有数据
     *
     * @param pageNo
     */
    private void loadData(final int pageNo) {
        RequestParams param = HttpHelper.getBaseParams(true);
        param.addFormDataPart("curpage", String.valueOf(pageNo));
        param.addFormDataPart("perpage", String.valueOf(PRE_LOADING_COUNR));
        param.addFormDataPart("read_status", "-1");// 全部数据, 包括已读未读

        HttpRequest.post(HttpHelper.URL_GET_MESSAGE_LIST,
                param, new RequestCallBack<String>() {
                    @Override
                    public void onLogicSuccess(String s) {
                        super.onLogicSuccess(s);
                        JSONObject data = HttpHelper.parserStringResponse(s);
                        LogUtils.e("okhttp " + data.toString());
                        if (data != null) {
                            isCacheData = false;
                            ArrayList<NotifyMessage> list = parseData(data);
                            if (list != null && mAdapter != null) {
                                if (pageNo == 1) {
                                    mAdapter.clearMessage();
                                }
                                mAdapter.addMessage(list);

                                // 缓存第一页的数据
                                if (pageNo == 1) {
                                    HttpHelper.saveDataCache(HttpHelper.URL_GET_MESSAGE_LIST, s);
                                }
                            }
                        }
                        refreshLayout.setRefreshing(false);
                        isLoading = false;
                    }

                    @Override
                    public void onFailure(int error, String msg) {
                        ToastUtils.showLong(MessageActivity.this, R.string.error_net);
                        refreshLayout.setRefreshing(false);
                        isLoading = false;
                    }
                });
    }


    /**
     * 获取加载更多的 FootView
     *
     * @return
     */
    private View getFootView() {
        footView = View.inflate(this, R.layout.message_list_item, null);
        footView.findViewById(R.id.message_item_content_tv).setVisibility(View.GONE);
        footView.findViewById(R.id.message_item_title_tv).setVisibility(View.GONE);
        footView.findViewById(R.id.message_item_new_icon).setVisibility(View.GONE);

        TextView moreTv = (TextView) footView.findViewById(R.id.message_item_date_tv);
        moreTv.setTextSize(11);
        moreTv.setText(getString(R.string.is_loadding_more));
        moreTv.setTextColor(getResources().getColor(R.color.text_color_black_nomal));

        return footView;
    }


    private ArrayList<NotifyMessage> getTestData() {
        ArrayList<NotifyMessage> list = new ArrayList<>(32);
        for (int i = 0; i < 20; i++) {
            NotifyMessage message = new NotifyMessage();
            if (i % 2 == 0) {
                message.setTitle("天气不错");
                message.setContent("今天的天气真好啊, PM2.5 都达到800多了. ");

            } else {
                message.setTitle("大家好, 都过来看看天气怎么样");
                message.setContent("今天的天气真好啊, PM2.5 都达到800多了, 你们那里的呢, 什么情况啊? 查看详情>>>");
            }
            message.setIsNew(i % 3 == 0);
            message.setTime(System.currentTimeMillis() - (i * 3600 * 1000 * 4));
            message.setUrl("http://www.totwoo.com");
            list.add(message);
        }
        return list;
    }


    /**
     * 处理长按删除单条消息内容的事件
     *
     * @param parent
     * @param view
     * @param position
     * @param id
     * @return
     */
    @Override
    public boolean onItemLongClick(AdapterView<?> parent, View view, int position, long id) {
        // 因为 header 的缘故, position 减一对应数据
//        position -= 1;
//        if (position < 0 || position > mAdapter.getCount() - 1) {
//            return false;
//        }
        // 删除单条内容
        showCustomDialog(position);
        return true;
    }

    /**
     * 展示删除消息的提示框
     *
     * @param position 消息位置, -1 表示全部删除
     */
    private void showCustomDialog(final int position) {
        final CommonMiddleDialog dialog = new CommonMiddleDialog(this);

        final String url;
        final RequestParams params = HttpHelper.getBaseParams(true);
        if (position >= 0) {
            dialog.setMessage(R.string.delete_message_prompt);
            url = HttpHelper.URL_GET_MESSAGE_DELETE;
            params.addFormDataPart("message_id", String.valueOf(mAdapter.getItemId(position)));
        } else {
            dialog.setMessage(R.string.delete_all_message_prompt);
            url = HttpHelper.URL_GET_MESSAGE_DELETE_ALL;
        }

        dialog.setCancel(R.string.cancel);

        dialog.setSure(R.string.confirm, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                HttpRequest.post(url, params, new RequestCallBack<String>() {

                    @Override
                    public void onStart() {
                        super.onStart();
                        if (progressDialog == null) {
                            progressDialog = new CustomProgressBarDialog(MessageActivity.this);
                            progressDialog.setMessage(R.string.delete_ing);
                        }
                        progressDialog.show();
                    }

                    @Override
                    public void onLogicSuccess(String s) {
                        super.onLogicSuccess(s);

                        JSONObject data = HttpHelper.parserStringResponse(s);
                        if (data != null && data.optInt("delete_result") == 1) {
                            if (mAdapter != null) {
                                if (position < 0) {
                                    mAdapter.clearMessage();
//                                    ToastUtils.showLong(MessageActivity.this, R.string.delete_all_message_success);
                                    HttpHelper.saveDataCache(HttpHelper.URL_GET_MESSAGE_LIST, "");
                                } else {
                                    mAdapter.removeMessage(position);
//                                    ToastUtils.showLong(MessageActivity.this, R.string.delete_message_success);
                                }
                            }
                        }

                        if (progressDialog != null && progressDialog.isShowing()) {
                            progressDialog.dismiss();
                        }
                    }

                    @Override
                    public void onFailure(int error, String msg) {
                        if (progressDialog != null && progressDialog.isShowing()) {
                            progressDialog.dismiss();
                        }
                        ToastUtils.showLong(MessageActivity.this, R.string.error_net);
                    }
                });
                dialog.dismiss();
            }
        });

        dialog.show();
    }

    // 退出界面时需要判断, 当前首页是否存活, 如果没有, 跳转首页
    @Override
    public void finish() {
        Class<? extends Activity> tagertClass = HomeActivityControl.getInstance().getTagertClass();
        if (ActivityUtils.isActivityExistsInStack(tagertClass)) {
            super.finish();
        } else {
            HomeActivityControl.getInstance().openHomeActivity(this);
        }
    }

    /**
     * 消息列表适配器
     */
    class MessageAdapter extends BaseAdapter {
        private Context mContext;

        public MessageAdapter(Context context, ArrayList<NotifyMessage> datalist) {
            mContext = context;
            if (datalist == null) {
                messagelist = new ArrayList<>();
            } else {
                messagelist = datalist;
            }

            // 更新是否可更新的状态
            if (nextPage > 0) {
                footView.setVisibility(View.VISIBLE);
            } else {
                footView.setVisibility(View.GONE);
            }
        }

        public void addMessage(NotifyMessage message) {
            messagelist.add(message);
            notifyDataSetChanged();
        }

        public void addMessage(ArrayList<NotifyMessage> list) {
            messagelist.addAll(list);
            notifyDataSetChanged();
        }

        public void removeMessage(int position) {
            messagelist.remove(position);
            notifyDataSetChanged();
        }

        @Override
        public void notifyDataSetChanged() {
            super.notifyDataSetChanged();
            // 更新是否可更新的状态
            if (nextPage > 0) {
                footView.setVisibility(View.VISIBLE);
            } else {
                footView.setVisibility(View.GONE);
            }
        }

        public void clearMessage() {
            messagelist.clear();
            notifyDataSetChanged();
        }

        @Override
        public int getCount() {
            if (messagelist.size() == 0) {
                noDataRl.setVisibility(View.VISIBLE);
            } else {
                noDataRl.setVisibility(View.GONE);
            }
            return messagelist.size();
        }

        @Override
        public NotifyMessage getItem(int position) {
            return messagelist.get(position);
        }

        @Override
        public long getItemId(int position) {
            return messagelist.get(position).getId();
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            ViewHolder holder = null;
            if (convertView == null) {
                convertView = LayoutInflater.from(mContext).inflate(R.layout.message_list_item_new, null);

                holder = new ViewHolder();
                holder.dateTv = convertView.findViewById(R.id.message_item_date_tv);
                holder.seeTv = convertView.findViewById(R.id.see_tv);
                holder.titleTv = convertView.findViewById(R.id.message_title_tv);
                holder.contentTV = convertView.findViewById(R.id.message_content_tv);
                holder.newIcon = convertView.findViewById(R.id.message_item_new_icon);
                holder.group = convertView.findViewById(R.id.group);
                convertView.setTag(holder);
            } else {
                holder = (ViewHolder) convertView.getTag();
            }


            NotifyMessage mess = messagelist.get(position);

            holder.group.setVisibility(
                    TextUtils.isEmpty(mess.getUrl()) ? View.GONE : View.VISIBLE);

            if ((!isCacheData && mess.isNew())) {
                holder.newIcon.setImageResource(R.drawable.icon_message_unread);
            } else {
                holder.newIcon.setImageResource(R.drawable.icon_message);
            }

            holder.titleTv.setText(mess.getTitle());
            if (TextUtils.isEmpty(mess.getContent())) {
                holder.contentTV.setVisibility(View.GONE);
            } else {
                holder.contentTV.setVisibility(View.VISIBLE);
                holder.contentTV.setText(mess.getContent());
            }

            SimpleDateFormat sf = new SimpleDateFormat("yyyy/MM/dd HH:mm");
            holder.dateTv.setText(sf.format(new Date(mess.getTime())));

            ClickUtils.expandClickArea(holder.seeTv, 30);
            holder.seeTv.setOnClickListener(v -> {
                NotifyMessage nm = mess;
                if (TextUtils.equals(nm.getJumpType(), "map")) {
                    try {
                        NotifyMessage.CustomData cd = nm.getCustomData();
                        Uri mUri = Uri.parse("geo:" + cd.latitude + "," + cd.longitude);
                        Intent mIntent = new Intent(Intent.ACTION_VIEW, mUri);
                        startActivity(mIntent);
                    } catch (ActivityNotFoundException e) {
                        e.printStackTrace();
                    }
                } else {
                    String url = nm.getUrl();
                    if (!TextUtils.isEmpty(url) && URLUtil.isValidUrl(url)) {
                        if (url.endsWith("apk") || url.endsWith("APK")) {
                            File target = new File(FileUtils.getDownloadDir() + File.separator + "apk" + File.separator + "totwoo_message_list.apk");
                            new ApkDownloader(false, true, true, true)
                                    .downloads(mContext, url, target);
                        } else {
//                            nm.setIs_share(0);
//                            nm.setIs_full_show("0");
//                            if (nm.getIs_share() == 0) {
//                                WebViewActivity.loadUrl(mContext, addParams(url), false);
//                            } else {
                                WebViewActivity.loadUrl(mContext, addParams(url), false, nm.getIs_share() ==1, nm.getShare_title(), nm.getShare_content() + " "+ addParams(url), nm.getIs_full_show());
//                            }
                        }
                    }
                    if (messagelist != null && messagelist.size() > 0) {
                        NotifyMessage notifyMessage = messagelist.get(position);
                        notifyMessage.setIsNew(false);
                        messagelist.set(position, notifyMessage);
                        mAdapter.notifyDataSetChanged();
                    }
                }
            });
            return convertView;
        }
    }

    private String addParams(String url) {
        if (TextUtils.isEmpty(url)) {
            return "";
        }
        if (url.contains("?")) {
            return url + "&sign=" + DesUtil.getH5Sign();
        } else {
            return url + "?sign=" + DesUtil.getH5Sign();
        }
    }

    class ViewHolder {
        public TextView dateTv;
        public TextView titleTv;
        public TextView contentTV;
        public ImageView newIcon;
        public TextView seeTv;
        public Group group;
    }

}
