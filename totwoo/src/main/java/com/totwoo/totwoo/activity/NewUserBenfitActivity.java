package com.totwoo.totwoo.activity;

import android.graphics.BitmapFactory;
import android.graphics.Typeface;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.homeActivities.HomeActivityControl;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.FileUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ShareUtilsSingleton;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CustomDialog;
import com.totwoo.totwoo.widget.CustomTypefaceSpan;
import com.totwoo.totwoo.widget.IMHintDialog;
import com.totwoo.totwoo.widget.NewUserGiftDialog;
import com.umeng.analytics.MobclickAgent;

import butterknife.BindView;
import butterknife.ButterKnife;
import rx.Subscriber;

public class NewUserBenfitActivity extends BaseActivity implements SubscriberListener {
    private IMHintDialog imHintDialog;
    private NewUserGiftDialog newUserGiftDialog;
    private CustomDialog customDialog;
    private CustomDialog customShareDialog;
    private CustomTypefaceSpan tfspan;
    private Typeface typefaceGithic;

    @BindView(R.id.benfit_share_text)
    TextView shareText;
    @BindView(R.id.new_benfit_info_tv)
    TextView shareInfoTv;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_new_benfit);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);

        imHintDialog = new IMHintDialog(NewUserBenfitActivity.this, v -> imHintDialog.dismiss(),"说明","1.新注册的用户，每人仅有1次新人特权分享机会，请一定动动手指分享给您的好友","2.分享后获得150元，可在有赞商城购买兔兔智能珠宝时抵扣使用，不能直接兑换成现金",
                "3.此次活动所有奖品与苹果公司无关，totwoo保留最终解释权");

        newUserGiftDialog = new NewUserGiftDialog(NewUserBenfitActivity.this, v -> {
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.NEWUSER_SHARE_LUCKY_CLICK);
            WebViewActivity.loadUrl(NewUserBenfitActivity.this, HttpHelper.URL_GIFT_NEW, false);
            newUserGiftDialog.dismiss();
        }, v -> newUserGiftDialog.dismiss(),CommonUtils.setNumberGoldenSpan("感谢您的分享\n获得 150 元TOTWOO礼包",150,20),"立即领取");

        customDialog = new CustomDialog(NewUserBenfitActivity.this);
        customDialog.setMessage("新人特权仅有一次，关闭之前请确认您已经分享给了好友。");
        customDialog.setTitle(R.string.tips);
        customDialog.setNegativeButton("确认关闭", v -> {
            HomeActivityControl.getInstance().openHomeActivity(this);
            HttpHelper.commonService.changeShareStatus(2001)
                    .compose(HttpHelper.rxSchedulerHelper())
                    .subscribe(new Subscriber<HttpBaseBean<Object>>() {
                        @Override
                        public void onCompleted() {
                            PreferencesUtils.put(NewUserBenfitActivity.this,CommonArgs.NEW_USER_BENFIT_SHARE,false);
                        }

                        @Override
                        public void onError(Throwable e) {

                        }

                        @Override
                        public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {

                        }
                    });
            finish();
        });
        customDialog.setPositiveButton("继续分享", v -> customDialog.dismiss());
        shareText.setOnClickListener(v -> {
            shared = true;
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.NEWUSER_SHARE_CLICK);
            customShareDialog.show();
        });
        shareInfoTv.setText(CommonUtils.setNumberGoldenSpan("把兔兔的爱和幸运分享给您的好友\n分享成功后双方立得 150 元",150,18));

        customShareDialog = new CustomDialog(NewUserBenfitActivity.this);
        customShareDialog.setTitle("分享");
        customShareDialog.setInfo(CommonUtils.setNumberGoldenSpan("分享给好友，双方各得 150 元",150,16));

        final String title = "手慢无！我帮你节省了150元，让你抢鲜体验兔兔智能珠宝，赶紧领取吧。";
        final String content = "兔兔智能珠宝，每一次兔兔闪光都蕴含一个秘密。";
        final String url = "http://m.totwoo.com/share/index_new.html";
        View view = getLayoutInflater().inflate(R.layout.common_share_layout_new,null);
        view.findViewById(R.id.common_share_friend_iv).setOnClickListener(v -> {
            ShareUtilsSingleton.getInstance().shareUrlToWechatMoment(title,content,getImagePath(),url);
            customShareDialog.dismiss();
        });
        view.findViewById(R.id.common_share_wechat_iv).setOnClickListener(v -> {
            ShareUtilsSingleton.getInstance().shareUrlToWechat(title,content,getImagePath(),url);
            customShareDialog.dismiss();
        });
        view.findViewById(R.id.common_share_weibo_iv).setOnClickListener(v -> {
            ShareUtilsSingleton.getInstance().shareUrlToWeibo(NewUserBenfitActivity.this,title,getImagePath(),url);
            customShareDialog.dismiss();
        });
        view.findViewById(R.id.common_share_qq_iv).setOnClickListener(v -> {
            ShareUtilsSingleton.getInstance().shareUrlToQQ(title,content,getImagePath(),url);
            customShareDialog.dismiss();
        });
        view.findViewById(R.id.common_share_qzone_iv).setOnClickListener(v -> {
            ShareUtilsSingleton.getInstance().shareUrlToQzone(title,content,getImagePath(),url);
            customShareDialog.dismiss();
        });
        customShareDialog.setMainLayoutView(view);
        customShareDialog.setPositiveButton(R.string.cancel, v -> customShareDialog.dismiss());
        setSpinState(false);
        PermissionUtil.hasStoragePermission(NewUserBenfitActivity.this);
    }

    private String getImagePath(){
        String path = FileUtils.saveBitmapFromSDCard(BitmapFactory.decodeResource(ToTwooApplication.baseContext.getResources(), R.drawable.new_user_benfit_share_icon),
                "totwoo_cache_img_" + System.currentTimeMillis());
        return path;
    }

    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopTitle("新人特权");
        setTopTitleColor(getResources().getColor(R.color.white));
        setTopBackIcon(R.drawable.close_icon_white);
        setTopLeftOnclik(v -> showNotSaveDialog());
        setTopRightIcon(R.drawable.icon_help_white);
        setTopRightOnClick(v -> imHintDialog.show());
    }

    private boolean shared;

    private void showNotSaveDialog() {
        if(newUserGiftDialog.isShowing()){
            newUserGiftDialog.dismiss();
            return;
        }
        if(customShareDialog.isShowing()){
            customShareDialog.dismiss();
            return;
        }
        if(imHintDialog.isShowing()){
            imHintDialog.dismiss();
            return;
        }
        //m
        if(!shared){
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.NEWUSER_CLOSE_CLICK);
        }
        customDialog.show();
    }

    @Override
    public void onBackPressed() {

        showNotSaveDialog();
    }

    /**
     */
    @EventInject(eventType = S.E.E_SHARE_SUCCESSED, runThread = TaskType.UI)
    public void successCallback(EventData data) {
        newUserGiftDialog.show();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        InjectUtils.injectUnregisterListenerAll(this);
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults,this);
    }
}
