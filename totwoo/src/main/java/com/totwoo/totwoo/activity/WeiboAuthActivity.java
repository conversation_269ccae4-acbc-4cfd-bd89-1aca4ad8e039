package com.totwoo.totwoo.activity;

import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.sina.weibo.sdk.auth.Oauth2AccessToken;
import com.sina.weibo.sdk.auth.WeiboAuth;
import com.sina.weibo.sdk.auth.WeiboAuthListener;
import com.sina.weibo.sdk.auth.sso.SsoHandler;
import com.sina.weibo.sdk.exception.WeiboException;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.SafeTypeBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.RoundImageView;
import com.umeng.analytics.MobclickAgent;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Observer;

public class WeiboAuthActivity extends BaseActivity {
    @BindView(R.id.weibo_default_cl)
    ConstraintLayout mDefaultCl;
    @BindView(R.id.weibo_success_ll)
    LinearLayout mSuccessCl;
    @BindView(R.id.weibo_share_checkbox)
    CheckBox mShareCb;
    @BindView(R.id.weibo_preview_iv)
    RoundImageView mPreviewIv;
    @BindView(R.id.weibo_preview_tv)
    TextView mPreviewTv;
    @BindView(R.id.weibo_preview_des)
    TextView mPreviewDes;

    public static final String WEIBO_TOKEN = "weibo_token";
    public static final String WEIBO_SHARE = "weibo_share";
    public static final String WEIBO_EXPIRE_TIME = "weibo_expire_time";

    public static final String APP_KEY = "3522343557";

    public static final String REDIRECT_URL = "http://www.totwoo.com";

    public static final String SCOPE =
            "email,direct_messages_read,direct_messages_write,"
                    + "friendships_groups_read,friendships_groups_write,statuses_to_me_read,"
                    + "follow_app_official_microblog," + "invitation_write";

    /**
     * 微博 Web 授权类，提供登陆等功能
     */
    private WeiboAuth mWeiboAuth;

    private boolean shareState;

    /**
     * 封装了 "access_token"，"expires_in"，"refresh_token"，并提供了他们的管理功能
     */
    private Oauth2AccessToken mAccessToken;

    /**
     * 注意：SsoHandler 仅当 SDK 支持 SSO 时有效
     */
    private SsoHandler mSsoHandler;

    private TextView tvRight;

    private CommonMiddleDialog expireDialog;

    private long expireTime;
    private String token;

    private long sevenDays = 7 * 24 * 3600;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_weibo);
        ButterKnife.bind(this);

        mWeiboAuth = new WeiboAuth(this, APP_KEY, REDIRECT_URL, SCOPE);
        mSsoHandler = new SsoHandler(WeiboAuthActivity.this, mWeiboAuth);

        shareState = PreferencesUtils.getBoolean(WeiboAuthActivity.this, WEIBO_SHARE, true);
        mShareCb.setChecked(shareState);

        token = PreferencesUtils.getString(WeiboAuthActivity.this, WEIBO_TOKEN, "");
        setViewState();
        expireTime = PreferencesUtils.getLong(WeiboAuthActivity.this, WEIBO_EXPIRE_TIME, 0);
        if (!TextUtils.isEmpty(token) && expireTime > 0 && (expireTime - System.currentTimeMillis() / 1000) < sevenDays) {
            expireDialog = new CommonMiddleDialog(this);
            expireDialog.setMessage(R.string.safe_weibo_expire);
            expireDialog.setSure(R.string.safe_weibo_expire_sure, v -> {
                authorize();
                expireDialog.dismiss();
            });
            expireDialog.setCancel(R.string.cancel);
            expireDialog.show();
        }
    }

    private void setViewState() {
        if (TextUtils.isEmpty(token)) {
            mDefaultCl.setVisibility(View.VISIBLE);
            mSuccessCl.setVisibility(View.GONE);
        } else {
            mDefaultCl.setVisibility(View.GONE);
            mSuccessCl.setVisibility(View.VISIBLE);
            getSafeState();
        }
    }

    private void getSafeState() {
        HttpHelper.safeService.getSafeState(2001)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<SafeTypeBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        LogUtils.e("aab e = " + e);
                        ToastUtils.showShort(WeiboAuthActivity.this, getString(R.string.error_net));
                    }

                    @Override
                    public void onNext(HttpBaseBean<SafeTypeBean> safeTypeBeanHttpBaseBean) {
                        if (safeTypeBeanHttpBaseBean.getErrorCode() == 0) {
                            mPreviewDes.setText(setStyle(safeTypeBeanHttpBaseBean.getData().getWeibo_content()));
                            mPreviewTv.setText(safeTypeBeanHttpBaseBean.getData().getWeibo_nickname());
                            RequestOptions options;
                            if (ToTwooApplication.owner.getGender() == 0) {
                                options = new RequestOptions()
                                        .error(R.drawable.default_head_yellow);
                            } else {
                                options = new RequestOptions()
                                        .error(R.drawable.default_head_yellow);
                            }
                            Glide.with(WeiboAuthActivity.this).load(BitmapHelper.checkRealPath(safeTypeBeanHttpBaseBean.getData().getWeibo_profile())).apply(options).into(mPreviewIv);
                        }
                    }
                });
    }

    private void setTopRightState() {
        if (TextUtils.isEmpty(token)) {
            tvRight.setVisibility(View.GONE);
        } else {
            tvRight.setVisibility(View.VISIBLE);
        }
    }

    private void updateToken(String token, boolean isClear, long tokenTime) {
        HttpHelper.safeService.setWeiboToken("token", token, tokenTime,1)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<Object>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        if (isClear) {
                            ToastUtils.showShort(WeiboAuthActivity.this,R.string.safe_weibo_unbind_fail);
                        }
                    }

                    @Override
                    public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {
                        if (objectHttpBaseBean.getErrorCode() == 0) {
                            if (isClear) {
                                PreferencesUtils.remove(WeiboAuthActivity.this, WEIBO_TOKEN);
                            }
                            setViewState();
                            setTopRightState();
                            mShareCb.setChecked(shareState);
                            if (isClear) {
                                ToastUtils.showShort(WeiboAuthActivity.this,R.string.safe_weibo_unbind_success);
                            }

                        }
                    }
                });
    }

    private void updateState(boolean isShare) {
        HttpHelper.safeService.setWeiboState("set", isShare ? 1 : 0)
                .compose(HttpHelper.rxSchedulerHelper())
                .subscribe(new Observer<HttpBaseBean<Object>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {
                        PreferencesUtils.put(WeiboAuthActivity.this, WEIBO_SHARE, isShare);
                        mShareCb.setChecked(shareState);
                    }
                });
    }

    /**
     * 当 SSO 授权 Activity 退出时，该函数被调用。
     */
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        // SSO 授权回调
        // 重要：发起 SSO 登陆的 Activity 必须重写 onActivityResult
        if (mSsoHandler != null) {
            mSsoHandler.authorizeCallBack(requestCode, resultCode, data);
        }
    }

    @OnClick({R.id.weibo_auth_tv, R.id.weibo_share_checkbox})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.weibo_auth_tv:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.GUARD_WEIBO_SYNC_ON);
                authorize();
                break;
            case R.id.weibo_share_checkbox:
                shareState = !shareState;
                updateState(shareState);
                break;
        }
    }

    private void authorize(){
        if(CommonUtils.isInstallPackage(WeiboAuthActivity.this,"com.sina.weibo")){
            mSsoHandler.authorize(new AuthListener());
        }else{
            mWeiboAuth.anthorize(new AuthListener());
        }
    }

    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopBackIcon(R.drawable.back_icon_black);
        setTopTitle(R.string.safe_emergency_set_weibo);
        tvRight = getTopRightTv();
        tvRight.setText(R.string.safe_weibo_unbind);
        tvRight.setOnClickListener(v -> cancelBind());
        setTopRightState();
    }
    
    private CommonMiddleDialog cancelBindDialog;
    private void cancelBind() {
        if (cancelBindDialog == null) {
            cancelBindDialog = new CommonMiddleDialog(WeiboAuthActivity.this);
            cancelBindDialog.setMessage(R.string.safe_weibo_cancel_bind);
            cancelBindDialog.setSure(v -> {
                token = "";
                updateToken("", true, 0);
                cancelBindDialog.dismiss();
            });
            cancelBindDialog.setCancel(R.string.cancel);
        }
        cancelBindDialog.show();
    }

    /**
     * 微博认证授权回调类。
     * 1. SSO 授权时，需要在 {@link #onActivityResult} 中调用 {@link SsoHandler#authorizeCallBack} 后，
     * 该回调才会被执行。
     * 2. 非 SSO 授权时，当授权结束后，该回调就会被执行。
     * 当授权成功后，请保存该 access_token、expires_in、uid 等信息到 SharedPreferences 中。
     */
    class AuthListener implements WeiboAuthListener {

        @Override
        public void onComplete(Bundle values) {
            // 从 Bundle 中解析 Token
            mAccessToken = Oauth2AccessToken.parseAccessToken(values);
            if (mAccessToken.isSessionValid()) {

                // 保存 Token 到 SharedPreferences
                PreferencesUtils.put(WeiboAuthActivity.this, WEIBO_TOKEN, mAccessToken.getToken());
                PreferencesUtils.put(WeiboAuthActivity.this, WEIBO_EXPIRE_TIME, mAccessToken.getExpiresTime() / 1000);
                PreferencesUtils.put(WeiboAuthActivity.this, WEIBO_SHARE, true);
                shareState = true;
                token = mAccessToken.getToken();
                updateToken(mAccessToken.getToken(), false, mAccessToken.getExpiresTime() / 1000);
            } else {
                // 当您注册的应用程序签名不正确时，就会收到 Code，请确保签名正确
                String code = values.getString("code");
                String message = getString(R.string.connect_failed);
                if (!TextUtils.isEmpty(code)) {
                    message = message + "\nObtained the code: " + code;
                }
                Toast.makeText(WeiboAuthActivity.this, message, Toast.LENGTH_LONG).show();
            }
        }

        @Override
        public void onCancel() {
            Toast.makeText(WeiboAuthActivity.this,
                    R.string.cancel, Toast.LENGTH_LONG).show();
        }

        @Override
        public void onWeiboException(WeiboException e) {
            Toast.makeText(WeiboAuthActivity.this,
                    "Auth exception : " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private SpannableString setStyle(String describe) {
        SpannableString spannableString = new SpannableString(describe);
        String sub1 = "@Totwoo女孩安全饰品";
        String sub2 = "网页链接";
        int index = describe.indexOf(sub1);
        int endIndex = index + sub1.length();
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#ff507daf")), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        index = describe.indexOf(sub2);
        endIndex = index + sub2.length();
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#ff507daf")), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

}
