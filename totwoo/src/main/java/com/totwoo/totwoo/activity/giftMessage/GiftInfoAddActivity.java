package com.totwoo.totwoo.activity.giftMessage;

import android.content.Intent;
import android.graphics.Bitmap;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.airbnb.lottie.LottieAnimationView;
import com.airbnb.lottie.LottieDrawable;
import com.blankj.utilcode.util.SizeUtils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.canhub.cropper.CropImageView;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventBus;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.record.PreviewConfig;
import com.totwoo.totwoo.utils.CenterCropRoundCornerTransform;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.CommonMiddleDialog;

import java.io.File;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

public class GiftInfoAddActivity extends BaseActivity implements SubscriberListener {
    public static final String GIFT_SENDER_NAME = "gift_sender_name";
    public static final String GIFT_SENDER_INFO = "gift_sender_info";
    @BindView(R.id.top_bar_back_btn)
    ImageView mBackIv;
    @BindView(R.id.top_bar_right_tv)
    TextView mRightTv;
    @BindView(R.id.gift_add_image_iv)
    ImageView mMainIv;
    @BindView(R.id.gift_add_name_et)
    EditText mNameEt;
    @BindView(R.id.gift_add_info_et)
    EditText mInfoEt;
    @BindView(R.id.gift_add_count_tv)
    TextView mCountTv;
    @BindView(R.id.gift_add_info_rl)
    View mAddInfoRl;
    @BindView(R.id.gift_voice_play_iv)
    ImageView mVoicePlayIv;
    @BindView(R.id.gift_add_lav)
    LottieAnimationView mLottieView;

    @BindView(R.id.linearLayout)
    LinearLayout linearLayout;



    private int fromType;
    private String coverPath;
    private MediaPlayer mPlayer;
    private boolean isVoicePlaying = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_gift_add_info);
        ButterKnife.bind(this);
        InjectUtils.injectOnlyEvent(this);
        // 已在BaseActivity中启用EdgeToEdge.enable()，无需额外设置
        fromType = getIntent().getIntExtra(CommonArgs.FROM_TYPE, CommonArgs.COMMON_SEND_TYPE_IMAGE);
        coverPath = getIntent().getStringExtra(CommonArgs.COVER_PATH);

        mNameEt.setFilters(new InputFilter[]{
                new InputFilter.LengthFilter(20)
        });


        mInfoEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s != null && s.length() > 0) {
                    int len = s.length();
                    if (len > 300) {
                        s = s.subSequence(0, 300);
                        mInfoEt.setText(s);
                        ToastUtils.showShort(GiftInfoAddActivity.this, getString(R.string.wish_out_of_limit));
                        mInfoEt.setSelection(300);
                    }
                }
            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s != null && !TextUtils.isEmpty(s.toString())) {
                    int length = s.toString().length();
                    if (length >= 300) {
                        length = 300;
                    }
                    setEditCount(length);
                } else {
                    setEditCount(0);
                }
            }
        });
        switch (fromType) {
            case CommonArgs.COMMON_SEND_TYPE_IMAGE:
                setImageUrl(CommonArgs.CACHE_GIFT_IMAGE);
                break;
            case CommonArgs.COMMON_SEND_TYPE_SOUND:
                mMainIv.setVisibility(View.INVISIBLE);
                mVoicePlayIv.setVisibility(View.VISIBLE);
                if (mPlayer == null)
                    mPlayer = new MediaPlayer();
                try {
                    mPlayer.setDataSource(CommonArgs.CACHE_GIFT_AUDIO_PATH);
                    mPlayer.prepare();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                mLottieView.setImageAssetsFolder("lottie_gift_voice/");
                mLottieView.setAnimation("gift_voice_play.json");
                mLottieView.setRepeatCount(LottieDrawable.INFINITE);
                mLottieView.setVisibility(View.VISIBLE);
                mHandler.postDelayed(() -> autoPlaySound(), 500);

                mVoicePlayIv.setTranslationY(SizeUtils.dp2px(-25));
                linearLayout.setTranslationY(SizeUtils.dp2px(-17));
                break;
            case CommonArgs.COMMON_SEND_TYPE_VIDEO:
                setImageUrl(coverPath);
                mVoicePlayIv.setVisibility(View.VISIBLE);
                linearLayout.setTranslationY(SizeUtils.dp2px(-17));

                break;
        }
    }


    @Override
    protected void initTopBar() {
        super.initTopBar();
        setTopBackIcon(R.drawable.back_icon_black);

        mRightTv.setText(R.string.preview);
        mRightTv.setTextColor(getResources().getColor(R.color.color_main));
        mRightTv.setOnClickListener(v -> toPreview());
    }


    private void autoPlaySound() {
        mVoicePlayIv.setImageResource(R.drawable.gift_voice_pause);
        mPlayer.start();
        mLottieView.playAnimation();
        isVoicePlaying = true;
        mPlayer.setOnCompletionListener(mp -> {
            mVoicePlayIv.setImageResource(R.drawable.gift_voice_play);
            isVoicePlaying = false;
            mLottieView.pauseAnimation();
        });
    }

    private void setEditCount(int number) {
        mCountTv.setText(number + "/300");
    }

    @OnClick({R.id.gift_add_delete_iv, R.id.gift_voice_play_iv, R.id.gift_add_image_iv, R.id.gift_add_lav})
    protected void onClick(View view) {
        switch (view.getId()) {
            case R.id.gift_add_delete_iv:
                showDeleteDialog();
                break;
            case R.id.gift_add_image_iv:
            case R.id.gift_add_lav:
            case R.id.gift_voice_play_iv:
                if (fromType == CommonArgs.COMMON_SEND_TYPE_SOUND) {
                    if (isVoicePlaying) {
                        mVoicePlayIv.setImageResource(R.drawable.gift_voice_play);
                        mPlayer.pause();
                        mLottieView.pauseAnimation();
                        isVoicePlaying = false;
                    } else {
                        mVoicePlayIv.setImageResource(R.drawable.gift_voice_pause);
                        mPlayer.start();
                        mLottieView.playAnimation();
                        isVoicePlaying = true;
                        //((AnimationDrawable) audioGif.getDrawable()).start();
                        mPlayer.setOnCompletionListener(mp -> {
                            mVoicePlayIv.setImageResource(R.drawable.gift_voice_play);
                            isVoicePlaying = false;
                            mLottieView.pauseAnimation();
                        });
                    }
                } else if (fromType == CommonArgs.COMMON_SEND_TYPE_VIDEO) {
                    new PreviewConfig(getIntent().getStringExtra(CommonArgs.VIDEO_PATH), coverPath, null).goPreview(this);
                }
                break;
        }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        handlerFinish();
    }

    private void handlerFinish() {
        showNotSaveDialog();
    }

    private void toPreview() {
        String name = mNameEt.getText().toString().trim();
        if (TextUtils.isEmpty(name)) {
            ToastUtils.showShort(GiftInfoAddActivity.this, R.string.gift_add_info_empty_name);
            return;
        }
        String info = mInfoEt.getText().toString().trim();
        Intent intent = new Intent(GiftInfoAddActivity.this, GiftDataActivity.class);
        intent.putExtra(CommonArgs.FROM_TYPE, GiftDataActivity.PREVIEW);
        intent.putExtra(GiftDataActivity.TYPE, fromType);
        intent.putExtra(CommonArgs.VIDEO_PATH, getIntent().getStringExtra(CommonArgs.VIDEO_PATH));
        intent.putExtra(CommonArgs.COVER_PATH, coverPath);
        intent.putExtra(GIFT_SENDER_NAME, name);
        intent.putExtra(GIFT_SENDER_INFO, info);
        startActivity(intent);
    }

    private void showNotSaveDialog() {
        CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(GiftInfoAddActivity.this);
        commonMiddleDialog.setMessage(R.string.gift_add_cancel);
        commonMiddleDialog.setCancel(R.string.cancel);
        commonMiddleDialog.setSure(v -> {
            commonMiddleDialog.dismiss();
            finish();
        });
        commonMiddleDialog.show();
    }

    private void showDeleteDialog() {
        CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(GiftInfoAddActivity.this);
        commonMiddleDialog.setMessage(R.string.gift_add_info_delete);
        commonMiddleDialog.setCancel(R.string.cancel);
        commonMiddleDialog.setSure(v -> {
            commonMiddleDialog.dismiss();
            EventBus.onPostReceived(S.E.E_GIFT_DELETE_INFO, null);
            finish();
        });
        commonMiddleDialog.show();
    }

    private void setImageUrl(String path) {
        RequestOptions options = new RequestOptions().placeholder(R.drawable.gift_placeholder).skipMemoryCache(true).diskCacheStrategy(DiskCacheStrategy.NONE);
        Glide.with(this).load(path).apply(RequestOptions.bitmapTransform(new CenterCropRoundCornerTransform(40))).apply(options).into(mMainIv);
    }

    @Override
    protected void onPause() {
        super.onPause();

        try {
            if (mPlayer != null && mPlayer.isPlaying()) {
                mVoicePlayIv.setImageResource(R.drawable.gift_voice_play);
                mLottieView.pauseAnimation();
                mPlayer.pause();
                //((AnimationDrawable) audioGif.getDrawable()).stop();
            }
        } catch (Exception e) {
            LogUtils.e("aab e = " + e);
        }
    }

    /**
     * 寄语发送成功
     * ContactsSelectActivity
     */
    @EventInject(eventType = S.E.E_GIFT_SEND_SUCCEED, runThread = TaskType.UI)
    public void sendSucceed(EventData data) {
        finish();
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        InjectUtils.injectUnregisterListenerAll(this);
    }
}
