package com.totwoo.totwoo.activity.giftMessage;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.PermissionUtils;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.bean.GiftTypeSelectBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.record.RecorderConfig;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.PictureSelectUtil;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CustomDialog;
import com.totwoo.totwoo.widget.discretescrollview.DSVOrientation;
import com.totwoo.totwoo.widget.discretescrollview.DiscreteScrollView;
import com.totwoo.totwoo.widget.discretescrollview.InfiniteScrollAdapter;
import com.umeng.analytics.MobclickAgent;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * 智能情书制作页面, 包含照片, 语音, 视频
 */
public class SendGiftGalleryActivity extends BaseActivity {
    @BindView(R.id.item_picker)
    DiscreteScrollView itemPicker;
    @BindView(R.id.gift_gallery_index0_iv)
    ImageView mIndexIv0;
    @BindView(R.id.gift_gallery_index1_iv)
    ImageView mIndexIv1;
    @BindView(R.id.gift_gallery_index2_iv)
    ImageView mIndexIv2;
    @BindView(R.id.do_tv)
    TextView doTv;


    private InfiniteScrollAdapter infiniteAdapter;
    private CustomDialog changeBgDialog;
    private Uri uri;
    private int  position = 0;

    private List<GiftTypeSelectBean> mList = new ArrayList<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_gift_gallery);
        ButterKnife.bind(this);


        mList.add(new GiftTypeSelectBean(R.drawable.gift_type_photo, getString(R.string.gift_type_image_hint), getString(R.string.gift_type_image), CommonArgs.COMMON_SEND_TYPE_IMAGE, getString(R.string.gift_type_image_title)));
        mList.add(new GiftTypeSelectBean(R.drawable.gift_type_voice, getString(R.string.gift_type_voice_hint), getString(R.string.gift_type_voice), CommonArgs.COMMON_SEND_TYPE_SOUND, getString(R.string.gift_type_sound_title)));
        mList.add(new GiftTypeSelectBean(R.drawable.gift_type_video, getString(R.string.gift_type_video_hint), getString(R.string.gift_type_video), CommonArgs.COMMON_SEND_TYPE_VIDEO,getString(R.string.gift_type_video_title)));

        itemPicker = findViewById(R.id.item_picker);
        itemPicker.setOrientation(DSVOrientation.HORIZONTAL);
        infiniteAdapter = InfiniteScrollAdapter.wrap(new CardAdapter());
        itemPicker.setAdapter(infiniteAdapter);
//        itemPicker.setItemTransitionTimeMillis(150);
//        itemPicker.setItemTransformer(new ScaleTransformer.Builder()
//                .setMinScale(0.98f)
//                .build());
        LinearLayout.LayoutParams selectLayoutParams = new LinearLayout.LayoutParams(CommonUtils.dip2px(SendGiftGalleryActivity.this, 25), CommonUtils.dip2px(SendGiftGalleryActivity.this, 8));
        LinearLayout.LayoutParams unSelectLayoutParams = new LinearLayout.LayoutParams(CommonUtils.dip2px(SendGiftGalleryActivity.this, 10), CommonUtils.dip2px(SendGiftGalleryActivity.this, 8));
        int margin4dp = CommonUtils.dip2px(SendGiftGalleryActivity.this, 4);
        selectLayoutParams.setMargins(margin4dp, margin4dp, margin4dp, margin4dp);
        unSelectLayoutParams.setMargins(margin4dp, margin4dp, margin4dp, margin4dp);
        itemPicker.addOnItemChangedListener(new DiscreteScrollView.OnItemChangedListener<RecyclerView.ViewHolder>() {
            @Override
            public void onCurrentItemChanged(@Nullable RecyclerView.ViewHolder viewHolder, int adapterPosition) {
                position = adapterPosition;
                doTv.setText(mList.get(adapterPosition).getDescribe());
                setTopTitle(mList.get(adapterPosition).getName());
                if (adapterPosition == 0) {
                    mIndexIv0.setImageResource(R.drawable.gift_gallery_selected);
                    mIndexIv0.setLayoutParams(selectLayoutParams);
                    mIndexIv1.setImageResource(R.drawable.gift_gallery_unselected);
                    mIndexIv1.setLayoutParams(unSelectLayoutParams);
                    mIndexIv2.setImageResource(R.drawable.gift_gallery_unselected);
                    mIndexIv2.setLayoutParams(unSelectLayoutParams);
                } else if (adapterPosition == 1) {
                    mIndexIv0.setImageResource(R.drawable.gift_gallery_unselected);
                    mIndexIv0.setLayoutParams(unSelectLayoutParams);
                    mIndexIv1.setImageResource(R.drawable.gift_gallery_selected);
                    mIndexIv1.setLayoutParams(selectLayoutParams);
                    mIndexIv2.setImageResource(R.drawable.gift_gallery_unselected);
                    mIndexIv2.setLayoutParams(unSelectLayoutParams);
                } else if (adapterPosition == 2) {
                    mIndexIv0.setImageResource(R.drawable.gift_gallery_unselected);
                    mIndexIv0.setLayoutParams(unSelectLayoutParams);
                    mIndexIv1.setImageResource(R.drawable.gift_gallery_unselected);
                    mIndexIv1.setLayoutParams(unSelectLayoutParams);
                    mIndexIv2.setImageResource(R.drawable.gift_gallery_selected);
                    mIndexIv2.setLayoutParams(selectLayoutParams);
                }
            }
        });
        itemPicker.addScrollListener((scrollPosition, currentPosition, newPosition, currentHolder, newCurrent) -> {
            LogUtils.e("aab scrollPosition = " + scrollPosition);
            LogUtils.e("aab currentPosition = " + currentPosition);
            LogUtils.e("aab newPosition = " + newPosition);
        });

        if (BleParams.isTouchJewelry()) {
//            mTitleInfoTv.setText(R.string.gift_type_subtitle_touch);
        }

        doTv.setOnClickListener(v -> {
            doAction();
        });

    }

    private void doAction() {
        switch (mList.get(position).getType()) {
            case CommonArgs.COMMON_SEND_TYPE_IMAGE:
                if (PermissionUtil.hasCameraPermission(SendGiftGalleryActivity.this) && PermissionUtil.hasStoragePermission(SendGiftGalleryActivity.this)) {
                    getPhotoDialog(SendGiftGalleryActivity.this);
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_SERCET_MAKE_PHOTO);
                }
                break;
            case CommonArgs.COMMON_SEND_TYPE_SOUND:
                if (PermissionUtil.hasAudioPermission(SendGiftGalleryActivity.this) && PermissionUtil.hasStoragePermission(SendGiftGalleryActivity.this)) {
                    startActivity(new Intent(SendGiftGalleryActivity.this, GiftVoiceAddActivity.class));
                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_SERCET_MAKE_VOICE);

                }
                break;
            case CommonArgs.COMMON_SEND_TYPE_VIDEO:
                if (PermissionUtil.hasCameraPermission(SendGiftGalleryActivity.this) && PermissionUtil.hasAudioPermission(SendGiftGalleryActivity.this)) {
                    RecorderConfig config = new RecorderConfig("GIFT");
                    config.setSuggestWidth(720);
                    config.setSuggestHeight(1280);
                    config.setTarget((videoPath, coverPath) ->
                            startActivity(
                                    new Intent(SendGiftGalleryActivity.this, GiftInfoAddActivity.class)
                                            .putExtra(CommonArgs.FROM_TYPE, CommonArgs.COMMON_SEND_TYPE_VIDEO)
                                            .putExtra(CommonArgs.VIDEO_PATH, videoPath)
                                            .putExtra(CommonArgs.COVER_PATH, coverPath)
                            ));
                    config.goRecorder(SendGiftGalleryActivity.this);

                    MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_SERCET_MAKE_VIDEO);

                }
                break;
        }
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);
        setTopLeftOnclik(v -> finish());

        //修改view 大小

//        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) topRightIcon.getLayoutParams();
//        layoutParams.width = SizeUtils.dp2px(38);
//        layoutParams.height = SizeUtils.dp2px(38);
//        topRightIcon.setLayoutParams(layoutParams);
//        topRightIcon.requestLayout();


        setTopRightIcon(R.drawable.gift_list);
//        ImageView topRightIcon = getTopRightIcon();
//        topRightIcon.setScaleX(0.8f);
//        topRightIcon.setScaleY(0.8f);

        setTopRightOnClick(v -> startActivity(new Intent(SendGiftGalleryActivity.this, GiftMessageListActivity.class).putExtra(CommonArgs.FROM_TYPE, GiftMessageListActivity.LIST_SEND)));
    }

    private class CardAdapter extends RecyclerView.Adapter<CardAdapter.ViewHolder> {

        @Override
        public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
            View itemView = LayoutInflater.from(parent.getContext()).inflate(R.layout.gift_type_item, parent, false);
//            ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(CommonUtils.dip2px(SendGiftGalleryActivity.this, 270), CommonUtils.getScreenHeight() / 2);
//            itemView.setLayoutParams(layoutParams);
            return new CardAdapter.ViewHolder(itemView);
        }

        @Override
        public void onBindViewHolder(final CardAdapter.ViewHolder holder, final int position) {
//            holder.mImageView.setOnClickListener(v -> {
//                switch (mList.get(position).getType()) {
//                    case CommonArgs.COMMON_SEND_TYPE_IMAGE:
//                        if (PermissionUtil.hasCameraPermission(SendGiftGalleryActivity.this) && PermissionUtil.hasStoragePermission(SendGiftGalleryActivity.this)) {
//                            getPhotoDialog(SendGiftGalleryActivity.this);
//                        }
//                        break;
//                    case CommonArgs.COMMON_SEND_TYPE_SOUND:
//                        if (PermissionUtil.hasAudioPermission(SendGiftGalleryActivity.this) && PermissionUtil.hasStoragePermission(SendGiftGalleryActivity.this)) {
//                            startActivity(new Intent(SendGiftGalleryActivity.this, GiftVoiceAddActivity.class));
//                        }
//                        break;
//                    case CommonArgs.COMMON_SEND_TYPE_VIDEO:
//                        if (PermissionUtil.hasCameraPermission(SendGiftGalleryActivity.this) && PermissionUtil.hasAudioPermission(SendGiftGalleryActivity.this)) {
//                            RecorderConfig config = new RecorderConfig("GIFT");
//                            config.setSuggestWidth(720);
//                            config.setSuggestHeight(1280);
//                            config.setTarget((videoPath, coverPath) ->
//                                    startActivity(
//                                            new Intent(SendGiftGalleryActivity.this, GiftInfoAddActivity.class)
//                                                    .putExtra(CommonArgs.FROM_TYPE, CommonArgs.COMMON_SEND_TYPE_VIDEO)
//                                                    .putExtra(CommonArgs.VIDEO_PATH, videoPath)
//                                                    .putExtra(CommonArgs.COVER_PATH, coverPath)
//                                    ));
//                            config.goRecorder(SendGiftGalleryActivity.this);
//                        }
//                        break;
//                }
//            });
            holder.mImageView.setImageResource(mList.get(position).getImageResource());
            holder.gifTitle.setText(mList.get(position).getTitle());
            holder.mTextViewName.setText(BleParams.isTouchJewelry()?getString(R.string.gift_type_subtitle_touch):getString(R.string.gift_type_subtitle));
        }

        @Override
        public int getItemCount() {
            return mList.size();
        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            ImageView mImageView;
            TextView gifTitle;
            TextView mTextViewName;

            public ViewHolder(final View itemView) {
                super(itemView);
                mImageView = (ImageView) itemView.findViewById(R.id.gift_type_bg_iv);
                gifTitle = (TextView) itemView.findViewById(R.id.gift_type_title);
                mTextViewName = (TextView) itemView.findViewById(R.id.gift_type_text);
            }
        }
    }

    public void getPhotoDialog(Context context) {
        changeBgDialog = new CustomDialog(context);
        changeBgDialog.setTitle(R.string.wish_add_img);
        LinearLayout modify_head_dialog_ll = new LinearLayout(context);
        modify_head_dialog_ll
                .setLayoutParams(new LinearLayout.LayoutParams(
                        LinearLayout.LayoutParams.MATCH_PARENT,
                        LinearLayout.LayoutParams.WRAP_CONTENT));
        modify_head_dialog_ll.setOrientation(LinearLayout.VERTICAL);
        TextView album_tv = new TextView(context);
        TextView camera_tv = new TextView(context);
        modify_head_dialog_ll.addView(album_tv);
        modify_head_dialog_ll.addView(camera_tv);

        changeBgDialog.setMainLayoutView(modify_head_dialog_ll);
        album_tv.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
        camera_tv.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
        album_tv.setPadding(Apputils.dp2px(context, 20), Apputils.dp2px(context, 15), Apputils.dp2px(context, 20), Apputils.dp2px(context, 15));
        camera_tv.setPadding(Apputils.dp2px(context, 20), Apputils.dp2px(context, 15), Apputils.dp2px(context, 20), Apputils.dp2px(context, 15));
        album_tv.setBackgroundResource(R.drawable.item_bg);
        camera_tv.setBackgroundResource(R.drawable.item_bg);
        album_tv.setText(R.string.select_photo);
        camera_tv.setText(R.string.use_camera);
        album_tv.setTextColor(getResources().getColor(R.color.text_color_black_important));
        camera_tv.setTextColor(getResources().getColor(R.color.text_color_black_important));
        album_tv.setTextSize(16);
        camera_tv.setTextSize(16);
        changeBgDialog.setNegativeButtonText(R.string.cancel);
        // 相册tv监听点击开启选择图片app
        album_tv.setOnClickListener(v -> {
            PictureSelectUtil.with(this).gallery().crop(4, 3).setCallback(uri -> {
                receivePhoto(uri);
                changeBgDialog.dismiss();
            }).select();
        });
        // 相机tv监听点击开启拍照app
        camera_tv.setOnClickListener(v -> {
            PictureSelectUtil.with(this).camera().crop(4, 3).setCallback(uri -> {
                receivePhoto(uri);
                changeBgDialog.dismiss();
            }).select();
        });
        changeBgDialog.show();
    }

    private void receivePhoto(Uri uri) {
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = false;
        options.inSampleSize = 2;

        Bitmap bitmap = null;
        File f = null;
        try {
            bitmap = BitmapFactory.decodeStream(getContentResolver().openInputStream(uri));
            f = new File(CommonArgs.CACHE_GIFT_IMAGE);
            if (f.exists())
                f.delete();
            bitmap.compress(Bitmap.CompressFormat.JPEG, 90, new FileOutputStream(CommonArgs.CACHE_GIFT_IMAGE));
            startActivity(new Intent(SendGiftGalleryActivity.this, GiftInfoAddActivity.class).putExtra(CommonArgs.FROM_TYPE, CommonArgs.COMMON_SEND_TYPE_IMAGE));
        } catch (Exception e) {
            e.printStackTrace();
            if (f != null) {
                f.deleteOnExit();
            }
            ToastUtils.showLong(this, R.string.data_error);
        }
    }


    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (PermissionUtils.isGranted( permissions)) {
            doAction();
        }
        PermissionUtil.handlePermissionResult(requestCode, permissions, grantResults, this);
    }
}
