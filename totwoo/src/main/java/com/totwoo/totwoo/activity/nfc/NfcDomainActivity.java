package com.totwoo.totwoo.activity.nfc;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import com.blankj.utilcode.util.NetworkUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.data.nfc.SecretInfoManager;
import com.totwoo.totwoo.utils.BlackAlertDialogUtil;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.ToastUtils;

import java.util.regex.Pattern;

/**
 * NFC 创建个性域名页面
 */
public class NfcDomainActivity extends BaseActivity {
    private EditText domainEt;
    private TextView domainUrlTv;
    private Pattern doMainPattern = Pattern.compile("^[A-Za-z0-9]{4,20}$");
    private View saveTv;

    @SuppressLint("SetTextI18n")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_nfc_domain);

        CommonUtils.setStateBarTransparentForBlackUI(this);

        domainEt = findViewById(R.id.nfc_domain_et);
        domainUrlTv = findViewById(R.id.nfc_domain_url_tv);
        saveTv = findViewById(R.id.nfc_domain_save);
        findViewById(R.id.nfc_sort_back).setOnClickListener(v -> finish());

        String domain = SecretInfoManager.getInstance().getDomain();
        if (TextUtils.isEmpty(domain)) {
            domainUrlTv.setText(CommonArgs.NFC_HARDWARE_URL_PREFIX);
            saveTv.setOnClickListener(v -> checkAndSave());
        } else {
            domainEt.setVisibility(View.GONE);
            saveTv.setVisibility(View.GONE);
            domainUrlTv.setText(CommonArgs.NFC_HARDWARE_URL_PREFIX + domain);
        }
    }

    @SuppressLint("SetTextI18n")
    private void checkAndSave() {
        Editable text = domainEt.getText();
        if (TextUtils.isEmpty(text)) {
            ToastUtils.showLong(this, R.string.nfc_domain_error_blank);
            return;
        } else if (!doMainPattern.matcher(text).matches()) {
           ToastUtils.showLong(this, R.string.nfc_domain_error_format);
            return;
        }

        BlackAlertDialogUtil.showCommonDialog(this, R.string.confirm_text_for_domain_set, () -> {
            if (!NetworkUtils.isConnected()) {
                ToastUtils.showLong( this,R.string.error_net);
                return;
            }
            NfcLoading.show(this);
            SecretInfoManager.getInstance().updateDomain(text.toString(), code -> {
                NfcLoading.dismiss();
                if (code == 0) {
                    domainEt.setVisibility(View.GONE);
                    saveTv.setVisibility(View.GONE);
                    domainUrlTv.setText(CommonArgs.NFC_HARDWARE_URL_PREFIX + text);
                    ToastUtils.showLong( this,R.string.save_success);
                } else if (code == 1091) {
                    ToastUtils.showLong( this,R.string.nfc_domain_error_repeat);
                } else if (code == 1093) {
                    ToastUtils.showLong(this, R.string.nfc_domain_error_format);
                } else {
                    ToastUtils.showLong( this,HttpHelper.getUnknownErrorMessage(this, String.valueOf(code)));
                }
            });
        });
    }
}
