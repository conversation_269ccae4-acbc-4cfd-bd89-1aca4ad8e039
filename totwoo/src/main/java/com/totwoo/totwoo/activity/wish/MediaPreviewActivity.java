package com.totwoo.totwoo.activity.wish;

import android.graphics.Bitmap;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.Target;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.databinding.ActivityMediaPrewBinding;
import com.totwoo.totwoo.record.PreviewConfig;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.widget.scaleVideoView.ScalableType;

import java.io.IOException;

/**
 * 多媒体预览: 支持图片, 视频(封面图)
 */
public class MediaPreviewActivity extends BaseActivity {
    private static final String TAG = "MediaPreviewActivity";

    private PreviewConfig config;

    private boolean videoReady;

    private boolean videoPlayed = false;
    private ActivityMediaPrewBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityMediaPrewBinding.inflate(LayoutInflater.from(this));
        setContentView(binding.getRoot());
        CommonUtils.setStateBar(this, true);

        config = (PreviewConfig) getIntent().getSerializableExtra(PreviewConfig.EXTRA_PREVIEW_CONFIG_TAG);

        if (config == null) {
            ToastUtils.showLong(this, R.string.data_error);
            finish();
            return;
        }

        try {
            initData();
        } catch (Exception e) {
            e.printStackTrace();
            ToastUtils.showLong(this, R.string.data_error);
        }
    }

    private void initData() throws IOException {
        Log.d(TAG, "initData with: " + config);

        String imageUrl = config.getCoverPath() == null ? config.getVideoPath() : config.getCoverPath();
        RequestOptions requestOptions = new RequestOptions();
        if (imageUrl != null && imageUrl.startsWith("/")) {
            requestOptions = requestOptions.skipMemoryCache(true).diskCacheStrategy(DiskCacheStrategy.NONE);
        }

        Glide.with(MediaPreviewActivity.this)
                .asBitmap()
                .load(HttpHelper.getRealImageUrl(imageUrl))
                .apply(requestOptions)
                .addListener(new RequestListener<Bitmap>() {
                    @Override
                    public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Bitmap> target, boolean isFirstResource) {
                        // 等预览图加载完毕后, 在加载视频, 否则 cpu 会被占用, 无法显示预览图
                        if (!TextUtils.isEmpty(config.getVideoPath())) {
                            initVideoData();
                        }
                        return false;
                    }

                    @Override
                    public boolean onResourceReady(Bitmap resource, Object model, Target<Bitmap> target, DataSource dataSource, boolean isFirstResource) {
                        binding.mediaCoverView.setImageBitmap(resource);
                        if (!TextUtils.isEmpty(config.getVideoPath())) {
                            binding.mediaProgressbar.setVisibility(View.VISIBLE);
                            binding.mediaCoverView.post(MediaPreviewActivity.this::initVideoData);
                        }
                        return true;
                    }
                })
                .submit();

        if (!TextUtils.isEmpty(config.getInfo())) {
            binding.mediaInfoTv.setVisibility(View.VISIBLE);
            binding.mediaInfoTv.setText(config.getInfo());
            binding.mediaInfoTv.setOnClickListener(v -> {
                binding.mediaInfoTv.toggle(true);
            });
        }

        if (!TextUtils.isEmpty(config.getVideoPath())) {
            binding.mediaVideoView.setDataSource(HttpHelper.getRealImageUrl(config.getVideoPath()));
            binding.mediaVideoView.setLooping(config.isLoopPlay());
            binding.mediaVideoView.setScalableType(ScalableType.FIT_CENTER);

            binding.mediaVideoView.setOnClickListener(v -> {
                if (!videoReady) {
                    return;
                }

                if (binding.mediaVideoView.isPlaying()) {
                    binding.mediaVideoView.pause();
                    videoPlayed = false;
                } else {
                    binding.mediaVideoView.start();
                    videoPlayed = true;
                }

            });
        } else {
            binding.mediaProgressbar.setVisibility(View.GONE);
        }
    }

    private void initVideoData() {
        try {
            binding.mediaVideoView.prepareAsync(mp -> {
                mp.start();
                videoReady = true;
                videoPlayed = true;
                binding.mediaCoverView.setVisibility(View.GONE);
                binding.mediaVideoView.setVisibility(View.VISIBLE);
                binding.mediaProgressbar.setVisibility(View.GONE);
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    protected void onResume() {
        super.onResume();
        if (videoReady && videoPlayed) {
            binding.mediaVideoView.start();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (videoReady) {
            binding.mediaVideoView.pause();
        }
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_white);
        setTopLeftOnclik(v -> finish());
        setTopbarBackground(R.color.black);

        if (config.getTitleId() != 0) {
            setTopTitle(config.getTitleId());
            setTopTitleColor(0xffffffff);
        }

        if (config.getMenuListener() != null) {
            if (config.getMenuIcon() != 0) {
                setTopRightIcon(config.getMenuIcon());
            } else {
                setTopRightString(config.getMenuTextId());
            }
            setTopRightOnClick(v -> config.getMenuListener().onClick(this));
        }
    }
}
