package com.totwoo.totwoo.fragment;

import static com.totwoo.totwoo.ToTwooApplication.baseContext;
import static com.totwoo.totwoo.ToTwooApplication.owner;
import static com.totwoo.totwoo.utils.CommonArgs.COLOR_VALUE;
import static com.totwoo.totwoo.utils.CommonArgs.MUSIC_PART_VALUE;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.totwoo.library.exception.DbException;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.BrightModeActivity;
import com.totwoo.totwoo.activity.WaterTimeSettingActivity;
import com.totwoo.totwoo.activity.homeActivities.HomeBaseActivity;
import com.totwoo.totwoo.adapter.CustomAngleRecyclerViewAdapter;
import com.totwoo.totwoo.bean.ConstellationIndexBean;
import com.totwoo.totwoo.bean.CustomOrderBean;
import com.totwoo.totwoo.bean.GreetingCardInfo;
import com.totwoo.totwoo.bean.HomePageIndexInfo;
import com.totwoo.totwoo.bean.PeriodBean;
import com.totwoo.totwoo.bean.PeriodStateBean;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.service.BrightMusicPlayService;
import com.totwoo.totwoo.utils.ACache;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.CustomOrderDbHelper;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PopupMenuUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.PullZoomRecyclerView;
import com.totwoo.totwoo.widget.pickerview.TimePickerDialog;
import com.totwoo.totwoo.widget.pickerview.data.Type;
import com.totwoo.totwoo.widget.pickerview.listener.OnDateSetListener;
import com.umeng.analytics.MobclickAgent;

import java.text.SimpleDateFormat;
import java.util.ArrayList;

import butterknife.BindView;
import butterknife.ButterKnife;
import rx.Observer;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * Created by totwoo on 2018/9/18.
 */

public class CustomAngelFragment extends BaseFragment implements
        SubscriberListener, HomeBaseActivity.JewelryStateChangeListener, BluetoothManage.WriteSuccessListener, CustomAngleRecyclerViewAdapter.ItemTypeProvider{
    @BindView(R.id.custom_angel_fragment_recyclerview)
    PullZoomRecyclerView mRecyclerView;

//    @BindView(R.id.angel_top_layout)
//    AngleTopLayerLayout mHeartTopLayout;

    final SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

    private CustomAngleRecyclerViewAdapter mRecyclerViewAdapter;
    private static final String ANGEL_HINT_SHOW = "angel_hint_show";
    private Context mContext;

    private int finalIndex;
    private ArrayList<CustomOrderBean> customOrderBeans;

    private boolean isInit = false;
    private int imageTopToTopHeight = PopupMenuUtil.dip2px(baseContext, 224);
    private int imageBottomToTopHeight = PopupMenuUtil.dip2px(baseContext, 330);
    public static final String CACHE_ANGEL_IMG = "cache_angel_img";
    public static final String CACHE_ANGEL_JUMP_URL = "cache_angel_jump_url";

    private View view;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        view = inflater.inflate(R.layout.fragment_custom_angel, container, false);
        ButterKnife.bind(this, view);
        mContext = ToTwooApplication.baseContext;
        InjectUtils.injectOnlyEvent(this);
        try {
            initContentView();
        } catch (DbException e) {
            e.printStackTrace();
        }
        mContext.startService(new Intent(mContext, BrightMusicPlayService.class));
        getInfo();
        isInit = true;
        setTopLayout();
        BluetoothManage.getInstance().setWriteSuccessListener(this);
        if (!PreferencesUtils.getBoolean(mContext, ANGEL_HINT_SHOW, false)) {
            PreferencesUtils.put(mContext, ANGEL_HINT_SHOW, true);
            com.etone.framework.event.EventBus.onPostReceived(S.E.E_ANGEL_HINT, null);
        }
        CommonUtils.setStateBar(getActivity(), false);

        return view;
    }

    private void initContentView() throws DbException {
        mRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        customOrderBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getSelect(1);
        mRecyclerViewAdapter = new CustomAngleRecyclerViewAdapter(getActivity(), mRecyclerView, customOrderBeans, CustomAngleRecyclerViewAdapter.FROM_ANGEL);
        mRecyclerView.setAdapter(mRecyclerViewAdapter);


        // 监听 RecyclerView 的滑动事件
        mRecyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                // 获取 RecyclerView 滑动的距离
                int scrollY = recyclerView.computeVerticalScrollOffset();

                // 根据滑动距离计算透明度
                int alpha = calculateAlpha(scrollY);


                // 设置顶部状态栏和标题栏的背景色
//                mHeartTopLayout.setAlpha(alpha);
            }
        });


    }


    private void setTopLayout() {


//        mHeartTopLayout.setBuringLayerView(mRecyclerView);
//        mHeartTopLayout.setJewState();
//        binding.magicTopLayout.getmRightIcon().setVisibility(View.INVISIBLE);
//        binding.magicTopLayout.getmRight2Icon().setVisibility(View.INVISIBLE);
//        binding.magicTopLayout.getmRightIcon().setAlpha(0f);
//        binding.magicTopLayout.getmRight2Icon().setAlpha(0f);
//        binding.magicTopLayout.setmRightSetListener(v -> {
//            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FUNC_SORT);
//            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.MAGIC_ORDER_CLICK);
//            if (BleParams.isButtonBatteryJewelry()) {
//                startActivity(new Intent(getActivity(), CustomOrderActivity.class).putExtra(CommonArgs.FROM_TYPE, 6).putExtra(CommonArgs.NEED_HIDE_GIFT, true));
//            } else if (BleParams.isSM2()) {
//                startActivity(new Intent(getActivity(), CustomOrderActivity.class).putExtra(CommonArgs.FROM_TYPE, 9).putExtra(CommonArgs.NEED_HIDE_GIFT, true));
//            } else {
//                startActivity(new Intent(getActivity(), CustomOrderActivity.class).putExtra(CommonArgs.FROM_TYPE, 2));
//            }
//        });
//        // 对于 Banner 大图已经修改为情书制作入口的地方, 隐藏排序入口
//        if (!TextUtils.isEmpty(PreferencesUtils.getString(mContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "")) && !BleParams.isNoFlashJewelry()) {
//            binding.magicTopLayout.setmRightSetVisible(View.VISIBLE);
//        } else {
//            binding.magicTopLayout.setmRightSetVisible(View.GONE);
//        }
//        binding.magicTopLayout.setmRightIconListener(v -> {
//            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FLASH_SET);
//            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_FLASH_SET);
//            if (BleParams.isNoneMusicJewelry()) {
//                getContext().startActivity(new Intent(getContext(), BrightModeActivity.class));
//            } else {
//                getContext().startActivity(new Intent(getContext(), BrightPartMusicActivity.class));
//            }
//        });
//        binding.magicTopLayout.setmRight2IconListener(view -> flashChange());
    }



    // 计算透明度的方法
    private int calculateAlpha(int scrollY) {
        int minHeight = 0; // 顶部状态栏和标题栏透明的最小高度
        int maxHeight = 300; // 顶部状态栏和标题栏透明的最大高度

        // 根据滑动距离计算透明度
        int alpha = (int) (255 * ((float) (1 - scrollY / maxHeight)));
        // 控制透明度范围在 0 到 255 之间
        return Math.min(Math.max(alpha, 0), 255);
    }


    private void flashChange() {
        LogUtils.e("aab JewInfoSingleton.getInstance().getConnectState() = " + JewInfoSingleton.getInstance().getConnectState());
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            ToastUtils.showLong(ToTwooApplication.baseContext, CommonUtils.compantGetString(R.string.error_jewelry_connect));
            return;
        }
        // 表示当前灯光模式的状态, 0 表示从未开启, 1~9 表示9中模式正在开启中,
        // -1 ~ -9 表示当前关闭, 上次开启的为某个模式
        int index = PreferencesUtils.getInt(mContext, COLOR_VALUE, 1);
        int musicIndex = PreferencesUtils.getInt(mContext, MUSIC_PART_VALUE, 0);
        if (index < 0) {
            index = -index;
        }
        finalIndex = index;
        if (!CommonUtils.jewelryFlashOpen(requireContext())) {
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.OPEN_FLASH);
            if (BleParams.isNoneMusicJewelry() || finalIndex == 0 || musicIndex == 0) {
                BluetoothManage.getInstance().changeBirghtMode(NotifyUtil.getColorValue(BrightModeActivity.BrightModeListDataBean.dataBeans.get(index - 1).getFlashColorValue()), true);
            } else {
                BluetoothManage.getInstance().changeMusicBrightMode(7, NotifyUtil.getColorValue(getFlashColorValue(finalIndex - 1)), true);
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_MUSIC_PLAY_PLAY_PART, null);
            }
        } else {
            if (BleParams.isNoneMusicJewelry() || finalIndex == 0 || musicIndex == 0) {
                BluetoothManage.getInstance().changeBirghtMode(-1, true);
            } else {
                BluetoothManage.getInstance().changeMusicBrightMode(musicIndex, -1, true);
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_MUSIC_PLAY_STOP, null);
            }
        }
    }

    /**
     * 顶部标签页点击闪光
     * CustomAngelPullHolder
     */
    @EventInject(eventType = S.E.E_HOLDER_FLASH_CHANGE, runThread = TaskType.UI)
    public void onLightClickReceiver(EventData data) {
        flashChange();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        InjectUtils.injectUnregisterListenerAll(this);
        if (mRecyclerViewAdapter != null) {
            mRecyclerViewAdapter.recovery();
        }
    }

    @Override
    public void onWriteSuccessed() {
        if (getContext() == null) {
            return;
        }
        boolean open = CommonUtils.jewelryFlashOpen(requireContext());
        if (open) {
//            imageChangeHandler.sendEmptyMessage(0);
            PreferencesUtils.put(mContext, COLOR_VALUE, -finalIndex);
        } else {
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.ANGEL_FLASH_TURN_ON);
//            imageChangeHandler.sendEmptyMessage(1);
            PreferencesUtils.put(mContext, COLOR_VALUE, finalIndex);
        }
        com.etone.framework.event.EventBus.onPostReceived(S.E.E_FLASH_CHANGED, null);
    }


    /**
     * 标签页
     * CustomOrderActivity
     */
    @EventInject(eventType = S.E.E_CUSTOM_ORDER_UPDATE, runThread = TaskType.UI)
    public void onOrderUpdateReceiver(EventData data) {
        try {
            LogUtils.e("aab E_CUSTOM_ORDER_UPDATE receiver");
            customOrderBeans = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getSelect(1);
            mRecyclerViewAdapter = new CustomAngleRecyclerViewAdapter(getActivity(), mRecyclerView, customOrderBeans, CustomAngleRecyclerViewAdapter.FROM_ANGEL);
            mRecyclerView.setAdapter(mRecyclerViewAdapter);
        } catch (DbException e) {
            e.printStackTrace();
        }
    }

//    private ImageChangeHandler imageChangeHandler = new ImageChangeHandler();

    @Override
    public void onShow() {
        // 已在BaseActivity中启用EdgeToEdge.enable()，无需额外设置

        MobclickAgent.onPageStart(TrackEvent.ANGEL_PAGE);
        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.HOMEPAGE_BOTTOM_ANGEL);
//        setJewState();

        if (isInit) {
            getInfo();
        }
    }

    @Override
    public void onChange() {
//        setJewState();
    }

    @EventInject(eventType = S.E.E_HOMEACTIVITY_ONSHOW, runThread = TaskType.UI)
    public void onEventShow(EventData data) {
        setJewState();
    }

    private void setJewState() {
        if (view == null) {
            return;
        }
//        if (mHeartTopLayout == null) {
//            mHeartTopLayout = view.findViewById(R.id.angel_top_layout);
//        }
//        mHeartTopLayout.setJewState();
    }

    private void getInfo() {
        getPeriodState();
        getConstellationIndex();
        getGreetingCardInfo();
        String cache_img = ACache.get(mContext).getAsString(CACHE_ANGEL_IMG);
        if (TextUtils.isEmpty(cache_img)) {
            getTopImage();
        }
    }

    public static int define_num;

    private void getConstellationIndex() {
        HttpHelper.commonService.getConstellationIndex()
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<ConstellationIndexBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<ConstellationIndexBean> constellationIndexBeanHttpBaseBean) {
                        if (constellationIndexBeanHttpBaseBean.getErrorCode() == 0) {
                            CustomAngleRecyclerViewAdapter.constellationIndexBeanHttpBaseBean = constellationIndexBeanHttpBaseBean.getData();
                            com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_CHANGE_CUSTOM_NOTIFY, constellationIndexBeanHttpBaseBean.getData());
                            define_num = constellationIndexBeanHttpBaseBean.getData().getDefine_num();
                        }
                    }
                });
    }

    private void getPeriodState() {
        HttpHelper.periodSave.getPeriodState()
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<PeriodStateBean>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<PeriodStateBean> periodStateBeanHttpBaseBean) {
                        if (periodStateBeanHttpBaseBean.getErrorCode() == 0) {
                            CustomAngleRecyclerViewAdapter.periodStateBean = periodStateBeanHttpBaseBean.getData();
                            com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_PERIOD, periodStateBeanHttpBaseBean.getData());
                        } else if (periodStateBeanHttpBaseBean.getErrorCode() == 4) {
                            com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_PERIOD_OFF, null);
                        }
                    }
                });
    }

    private void getTopImage() {
        HttpHelper.commonService.homePageIndex("angel")
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<HomePageIndexInfo>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<HomePageIndexInfo> homePageIndexInfoHttpBaseBean) {
                        if (homePageIndexInfoHttpBaseBean.getErrorCode() == 0) {
                            CustomAngleRecyclerViewAdapter.homePageIndexInfo = homePageIndexInfoHttpBaseBean.getData();
                            com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_TOP_INDEX, homePageIndexInfoHttpBaseBean.getData());
                            ACache.get(mContext).put(CACHE_ANGEL_IMG, homePageIndexInfoHttpBaseBean.getData().getImg_url(), 6 * 3600);
                            ACache.get(mContext).put(CACHE_ANGEL_JUMP_URL, homePageIndexInfoHttpBaseBean.getData().getJump_url(), 6 * 3600);
                        }
                    }
                });
    }

    private void getGreetingCardInfo() {
        HttpHelper.commonService.getGreetingCardInfo()
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<HttpBaseBean<GreetingCardInfo>>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(HttpBaseBean<GreetingCardInfo> greetingCardInfoBeanHttpBaseBean) {
                        if (greetingCardInfoBeanHttpBaseBean.getErrorCode() == 0) {
                            if (greetingCardInfoBeanHttpBaseBean.getData().getIs_greetingcard() == 1) {
                                CustomAngleRecyclerViewAdapter.hasGreetingCardList = true;
                                com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_CHANGE_CARD_YES, null);
                            } else {
                                CustomAngleRecyclerViewAdapter.hasGreetingCardList = false;
                                com.etone.framework.event.EventBus.onPostReceived(S.E.E_HOLDER_CHANGE_CARD_NO, null);
                            }
                        }
                    }
                });
    }

    /**
     * 大姨妈标签页点击时候的弹窗
     * HomePeroidHolder
     */
    @EventInject(eventType = S.E.E_HOLDER_PERIOD_CHANGE, runThread = TaskType.UI)
    public void onPeriodInfoReceiver(EventData data) {
        showBirthDialog();
    }

    private void showBirthDialog() {
        String periodDay = owner.getPeriodDay();
        if (TextUtils.isEmpty(periodDay))
            periodDay = format.format(System.currentTimeMillis());

        long tenYears = 1L * 365 * 1000 * 60 * 60 * 24L;
        TimePickerDialog mDialogAll = new TimePickerDialog.Builder()
                .setCallBack(new OnDateSetListener() {
                    @Override
                    public void onDateSet(TimePickerDialog timePickerView, long millseconds) {
                        String tempBirthday = format.format(millseconds);
                        owner.setPeriodDay(tempBirthday);
                        HttpHelper.periodSave.updatePeriod(tempBirthday)
                                .subscribeOn(Schedulers.newThread())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribe(new Observer<HttpBaseBean<PeriodBean>>() {
                                    @Override
                                    public void onCompleted() {
                                    }

                                    @Override
                                    public void onError(Throwable e) {

                                    }

                                    @Override
                                    public void onNext(HttpBaseBean<PeriodBean> periodBeanHttpBaseBean) {
                                        if (periodBeanHttpBaseBean.getErrorCode() == 0) {
                                            getPeriodState();
                                            showWaterSuggestDialog();
                                        }
                                    }
                                });
                    }
                })
//                .setCancelStringId("Cancel")
//                .setSureStringId("Sure")
                .setTitleStringId(getString(R.string.period_setting_start_dialog))
                .setYearText(getString(R.string.period_setting_select_year))
                .setMonthText(getString(R.string.period_setting_select_month))
                .setDayText(getString(R.string.period_setting_select_day))
                .setHourText("")
                .setMinuteText("")
                .setCyclic(false)
                .setMinMillseconds(System.currentTimeMillis() - tenYears)
                .setMaxMillseconds(System.currentTimeMillis())
                .setCurrentMillseconds(System.currentTimeMillis())
                .setThemeColor(mContext.getResources().getColor(R.color.timepicker_dialog_bg))
                .setType(Type.YEAR_MONTH_DAY)
                .setWheelItemTextNormalColor(mContext.getResources().getColor(R.color.timetimepicker_default_text_color))
                .setWheelItemTextSelectorColor(mContext.getResources().getColor(R.color.timepicker_toolbar_bg))
                .setWheelItemTextSize(14)
                .build();

        mDialogAll.show(getChildFragmentManager(), "year_month_day");
    }

    private void showWaterSuggestDialog() {
        final CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(getActivity());
        commonMiddleDialog.setInfo(R.string.drink_water_reminder);
        commonMiddleDialog.setTitle(R.string.warm_tips);
        commonMiddleDialog.setSure(R.string.set, v -> {
            try {
                getActivity().startActivity(new Intent(getActivity(), WaterTimeSettingActivity.class));
            } catch (Exception e) {
                e.printStackTrace();
            }
            commonMiddleDialog.dismiss();
        });
        commonMiddleDialog.setCancel(R.string.cancel);
        commonMiddleDialog.show();
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }

    private String getFlashColorValue(int position) {
        String str = null;
        switch (position) {
            case 0:
                str = "COLORFUL";
                break;
            case 1:
                str = "PINK";
                break;
            case 2:
                str = "RED";
                break;
            case 3:
                str = "ORANGE";
                break;
            case 4:
                str = "YELLOW";
                break;
            case 5:
                str = "GREEN";
                break;
            case 6:
                str = "CYAN";
                break;
            case 7:
                str = "BLUE";
                break;
            case 8:
                str = "PURPLE";
                break;
            case 9:
                str = "WHITE";
                break;
        }
        return str;
    }

    public boolean hasItemByType(int itemType) {
        if (mRecyclerViewAdapter != null) {
            return mRecyclerViewAdapter.hasItemByType(itemType);
        }
        return false;
    }
}
