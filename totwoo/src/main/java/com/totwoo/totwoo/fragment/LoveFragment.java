package com.totwoo.totwoo.fragment;

import static com.totwoo.totwoo.ToTwooApplication.owner;
import static com.totwoo.totwoo.fragment.LovePairedFragment.IS_TOTWOO_HINT_SHOULD_SHOW;

import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.tencent.mars.xlog.Log;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.LoveTotwooInfo;
import com.totwoo.totwoo.bean.eventbusObject.TotwooMessage;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.data.CoupleLogic;
import com.totwoo.totwoo.utils.ACache;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.umeng.analytics.MobclickAgent;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;

import rx.Observer;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * Created by totwoo on 2018/11/6.
 */
public class LoveFragment extends BaseFragment implements SubscriberListener {
    private static final int PAIR_STATE_UNPAIRED = 0;
    private static final int PAIR_STATE_PAIRED = 1;
    @Deprecated //单身模式已经删除, 相关逻辑可删除了
    private static final int PAIR_STATE_SINGLE = 2;

    /**
     * 是否已经展示过 配对成功的 dialog
     */
    public static final String PREF_HAS_SHOW_PAIRED_DIALOG = "pref_has_show_paired_dialog";

    private int currentState = -1;

    private boolean isInit = false;
    private ACache aCache;
    private LoveUnpairedFragment unpairedFragment;
    private LovePairedFragment pairedFragment;
    /**
     * 是否已经从服务端更新过首页的背景图
     */
    private boolean hasLoadBgFromNet;
    /**
     * 是否已经做过一次进入画面时的 Totwoo 信息补充
     */
    private boolean hasRestoreTotwooMessage = false;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        aCache = ACache.get(ToTwooApplication.baseContext);
        InjectUtils.injectOnlyEvent(this);
        EventBus.getDefault().register(this);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        FrameLayout layout = new FrameLayout(getContext());
        layout.setId(R.id.id_love_fragment_container);
        checkAndSwitchUI(false);
        getTotwooIndex();
        isInit = true;
        return layout;
    }

    @Override
    public void onShow() {
        super.onShow();
        MobclickAgent.onPageStart(TrackEvent.LOVE_PAGE);
        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.HOMEPAGE_BOTTOM_LOVE);

//        checkAndSwitchUI();

        if (isInit) {
            getTotwooIndex();
        }
    }

    private void checkAndSwitchUI(boolean requestNet) {
        if (getHost() == null) {
            return;
        }
        if (owner != null && !TextUtils.isEmpty(ToTwooApplication.owner.getPairedId())) {
            if (currentState != PAIR_STATE_PAIRED) {
                if (pairedFragment == null) {
                    pairedFragment = LovePairedFragment.newInstance();
                }
                getChildFragmentManager().beginTransaction().replace(R.id.id_love_fragment_container, pairedFragment).commit();
                currentState = PAIR_STATE_PAIRED;
                if (requestNet) {
                    new Handler().postDelayed(this::getTotwooIndex, 600);
                }
            }
        } else {
            if (currentState != PAIR_STATE_UNPAIRED) {
                if (unpairedFragment == null) {
                    unpairedFragment = LoveUnpairedFragment.newInstance();
                }
                getChildFragmentManager().beginTransaction().replace(R.id.id_love_fragment_container, unpairedFragment).commit();
                currentState = PAIR_STATE_UNPAIRED;
            }
        }
    }


    @Override
    public void onHide() {
        super.onHide();

        MobclickAgent.onPageEnd(TrackEvent.LOVE_PAGE);
    }

    @EventInject(eventType = S.E.E_RECEIVED_IM_MESSAGE, runThread = TaskType.UI)
    public void onImMessageReceived(EventData data) {
        if (pairedFragment != null && pairedFragment.isVisible()) {
            pairedFragment.receiveImMessage();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onReceiveState(final TotwooMessage message) {
        if (getContext() == null || !isAdded() || message == null) {
            Log.e("onReceiveMsg","context |msg  is null, skip!");
            return;
        }
        switch (message.getTotwooState()) {
            case CommonArgs.ACTION_TOTWOO_DATA_CHANGEED://收到totwoo
                if (pairedFragment != null && pairedFragment.isVisible()) {
                    pairedFragment.receiveTotwooMessageV2(message);
                }
                break;
            case CoupleLogic.COUPLE_STATE_APART + ""://解除配对
                checkAndSwitchUI(true);
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_CLOSE_HOMEPAGE, null);
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_LOVE_HINT_TOTWOO_DISMISS, null);
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_UPDATE_PAIRED_STATE, null);
                PreferencesUtils.remove(ToTwooApplication.baseContext, PREF_HAS_SHOW_PAIRED_DIALOG);
                break;
            case TotwooMessage.TOTWOO_SEND_SUCCESS://totwoo发送成功
                if (pairedFragment != null && pairedFragment.isVisible()) {
                    pairedFragment.totwooSendSuccess(message);
                }
                break;
            case CoupleLogic.COUPLE_STATE_REPLY + "": //配对成功
                checkAndSwitchUI(true);
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_CUSTOM_ORDER_UPDATE, null);
                com.etone.framework.event.EventBus.onPostReceived(S.E.E_UPDATE_PAIRED_STATE, null);

                onShow();
                break;
            case CoupleLogic.COUPLE_STATE_REQUEST + "":
                if (unpairedFragment != null && unpairedFragment.isVisible()) {
                    unpairedFragment.receiveCoupleRequest(message);
                }
                break;
        }
    }


    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }


    @EventInject(eventType = S.E.E_LOVE_PAIR_BACKGROUND, runThread = TaskType.UI)
    public void onBackgroundCropSuccess(EventData data) {
        if (pairedFragment != null && pairedFragment.isVisible()) {
            pairedFragment.changeBackgroundImage(Uri.fromFile(new File(CommonArgs.LOVE_PAIR_BACKGROUND_IMAGE)));
        }
    }

    @EventInject(eventType = S.E.E_HOMEACTIVITY_ONSHOW, runThread = TaskType.UI)
    public void onEventShow(EventData data) {
        if (pairedFragment != null && pairedFragment.isVisible()) {
            pairedFragment.refreshJewState();
        } else if (unpairedFragment != null && unpairedFragment.isVisible()) {
            unpairedFragment.refreshJewState();
        }
    }

    /**
     * 进入页面时, 如果后台有收到 Totwoo 或者请求信息, 需要做一下动态展示
     */
    private void checkNotification() {
        if (!hasRestoreTotwooMessage) {
            hasRestoreTotwooMessage = true;

            if (aCache.getAsObject(CommonArgs.NOTIFICATION_MESSAGE) != null) {
                TotwooMessage totwooMessage = (TotwooMessage) aCache.getAsObject(CommonArgs.NOTIFICATION_MESSAGE);
                onReceiveState(totwooMessage);
            }
        }
    }

    private void getTotwooIndex() {
        launchRequest(
                HttpHelper.commonService.getTotwooIndex(), data -> {
                    checkNotification();
                    //华为审核隐藏求签用
                    ToTwooApplication.cacheData.setItem_status(data.getItem_status());
                    ToTwooApplication.otherPhone = data.getTarget_phone();

                    if (data.getType() == 3) {
                        ToTwooApplication.owner.setPairedId(data.getTalk_id());
                        // 如果已经配对过, 则不需要再进行显示引导了就
                        PreferencesUtils.put(getContext(), IS_TOTWOO_HINT_SHOULD_SHOW, true);
//                                showTotwooHint();
                        //每次启动app 调用过一次，保证数据最新
                        com.etone.framework.event.EventBus.onPostReceived(S.E.E_UPDATE_EMOTION, null);
                    } else {
                        CoupleLogic.clearCouplePairedData(getContext());
                    }
                    checkAndSwitchUI(false);
                    try {
                        PreferencesUtils.put(ToTwooApplication.baseContext, CoupleLogic.PAIRED_PERSON_NICK_NAME, data.getNick_name());
                        PreferencesUtils.put(ToTwooApplication.baseContext, CoupleLogic.PAIRED_PERSON_ID, data.getTarget_phone());
                        PreferencesUtils.put(ToTwooApplication.baseContext, CoupleLogic.PAIRED_PERSON_HEAD_URL_TAG, data.getHead());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    com.etone.framework.event.EventBus.onPostReceived(S.E.E_UPDATE_PAIRED_STATE, null);

                    if (pairedFragment != null && pairedFragment.isVisible()) {
                        pairedFragment.initLoveTotwooInfo(data);
                        if (!hasLoadBgFromNet && !TextUtils.isEmpty(data.getTotwoo_bg())) {
                            pairedFragment.changeBackgroundImage(Uri.parse(BitmapHelper.checkRealPath(data.getTotwoo_bg())));
                            hasLoadBgFromNet = true;
                        }
                    }
                },
                err -> {
                    dismissProgressDialog();
                    ToastUtils.showShort(ToTwooApplication.baseContext, R.string.error_net);
                    if (pairedFragment != null && pairedFragment.isVisible() && !hasLoadBgFromNet) {
                        hasLoadBgFromNet = true;
                    }
                }, false
        );
    }

    /**
     * 检查是否显示引导蒙层
     */
    private void showTotwooHint() {
//        boolean hasShow = PreferencesUtils.getBoolean(requireContext(), IS_TOTWOO_HINT_SHOWED, false);
//        boolean shouldShow = PreferencesUtils.getBoolean(requireContext(), IS_TOTWOO_HINT_SHOULD_SHOW, true);
//        LogUtils.e("showTotwooHint  hasShow: " + hasShow + ",  shouldShow: " + shouldShow);
//        //显示过是最高级别
//        if (hasShow) {
//            return;
//        }
//        if (shouldShow) {
//            com.etone.framework.event.EventBus.onPostReceived(S.E.E_LOVE_HINT_TOTWOO, null);
//        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();

        EventBus.getDefault().unregister(this);
        InjectUtils.injectUnregisterListenerAll(this);
    }
}
