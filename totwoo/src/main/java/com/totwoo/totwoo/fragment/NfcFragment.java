package com.totwoo.totwoo.fragment;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.provider.Settings;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.blankj.utilcode.util.NetworkUtils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.target.Target;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.gson.JsonElement;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.activity.nfc.NfcCardEditActivity;
import com.totwoo.totwoo.activity.nfc.NfcDomainActivity;
import com.totwoo.totwoo.activity.nfc.NfcLoading;
import com.totwoo.totwoo.activity.nfc.NfcMediaListActivity;
import com.totwoo.totwoo.activity.nfc.NfcSecretPasswdActivity;
import com.totwoo.totwoo.activity.nfc.NfcSecretSelectActivity;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.data.nfc.SecretInfoBean;
import com.totwoo.totwoo.data.nfc.SecretInfoManager;
import com.totwoo.totwoo.data.nfc.SecretType;
import com.totwoo.totwoo.utils.BlackAlertDialogUtil;
import com.totwoo.totwoo.utils.BlackBottomSheetDialog;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.RealPathFromUriUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.widget.SecretGridView;
import com.umeng.analytics.MobclickAgent;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import rx.Observable;
import rx.Observer;
import rx.functions.Func1;

/**
 * NFC 首页相关逻辑: 用户信息+记忆
 */
public class NfcFragment extends BaseFragment {
    /**
     * 添加名片的 activity code
     */
    private static final int REQUEST_CODE_ACTIVITY_ADD_CARD = 345;
    private static final int REQUEST_CODE_ACTIVITY_PICK_IMAGE = 346;
    private static final int REQUEST_CODE_ACTIVITY_SET_SELECT = 347;
    private static final int REQUEST_CODE_ACTIVITY_MEDIA_LIST = 348;

    private TextView infoTitleEditCompleteTv;
    private ImageView editIcon;
    private ImageView menuIcon;
    private SecretGridView secretGridView;
    private SecretInfoManager secretInfoManager;
    private ImagePickedCallback imagePickedCallback;
    private View netErrorLayout;

//    private boolean isFirstResume = true;


    public NfcFragment() {
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        secretInfoManager = SecretInfoManager.getInstance();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {


        View root = inflater.inflate(R.layout.fragment_nfc, container, false);
        initUI(root);

        refreshData(true);

        return root;
    }

    private void initUI(View root) {
        // 已在BaseActivity中启用EdgeToEdge.enable()，无需额外设置

        infoTitleEditCompleteTv = root.findViewById(R.id.nfc_info_title_edit_bt);
        secretGridView = root.findViewById(R.id.nfc_info_list_view);
        netErrorLayout = root.findViewById(R.id.nfc_info_net_error_layout);

        netErrorLayout.setOnClickListener(v -> refreshData(true));

        menuIcon = root.findViewById(R.id.nfc_top_menu);
        editIcon = root.findViewById(R.id.nfc_top_edit);
        menuIcon.setOnClickListener(v -> showBottomMenuDialog());

        List<SecretInfoBean> savedInfos = secretInfoManager.getSavedInfos();

        if (savedInfos != null && !savedInfos.isEmpty()) {
            secretGridView.setVisibility(View.VISIBLE);
            menuIcon.setVisibility(View.VISIBLE);
            menuIcon.setVisibility(View.VISIBLE);
        }
        secretGridView.setSecretInfoList(savedInfos);
        secretGridView.setAdditionalType(SecretGridView.ADDITIONAL_ACTION_TYPE_ADD);
        secretGridView.setSecretInfoClickListener(new SecretGridView.SecretInfoClickListener() {
            @Override
            public void onClick(SecretInfoBean bean) {
                if (secretGridView.isEditMode()) {
                    LogUtils.d("NfcFragment info delete: " + bean.getName());

                    int deleteInfoId;
                    if (SecretType.TYPE_MEDIA.equals(bean.getType().type)) {
                        deleteInfoId = R.string.custom_notify_list_delete_hint_video;
                    } else {
                        deleteInfoId = R.string.custom_notify_list_delete_hint;
                    }
                    BlackAlertDialogUtil.showCommonDialog(getContext(), deleteInfoId, () -> {
                        NfcLoading.show(getContext());
                        MobclickAgent.onEvent(getContext(), TrackEvent.NFC.NFC_DELETE_INFO_PREFIX + bean.getType().type);
                        secretInfoManager.deleteSecretData(bean, code -> {
                            NfcLoading.dismiss();
                            if (code == 0) {
                                secretGridView.notifyItemRemove(bean);

                                if (secretInfoManager.getSavedInfos().isEmpty()) {
                                    menuIcon.setVisibility(View.GONE);
                                    editIcon.setVisibility(View.GONE);
                                }
                            } else {
                                if (getContext() != null) {
                                    ToastUtils.showLong(getContext(), HttpHelper.getUnknownErrorMessage(getContext(), String.valueOf(code)));
                                }
                            }
                        });
                    });
                } else {
                    detailSecretInfo(bean, false);
                }
            }

            @Override
            public void onAdditionalClick(int additionalType) {
                showSecretAddGalleryDialog();
            }
        });

        View.OnClickListener editListener = v -> {
            boolean edit = secretGridView.toggleEditMode();

            if (edit) {
                MobclickAgent.onEvent(getContext(), TrackEvent.NFC.NFC_INFO_EDIT);
            }

            infoTitleEditCompleteTv.setVisibility(edit ? View.VISIBLE : View.GONE);
            menuIcon.setVisibility(edit ? View.GONE : View.VISIBLE);
            editIcon.setVisibility(edit ? View.GONE : View.VISIBLE);

            secretGridView.setAdditionalType(edit ? SecretGridView.ADDITIONAL_ACTION_TYPE_NONE
                    : SecretGridView.ADDITIONAL_ACTION_TYPE_ADD);
        };
        editIcon.setOnClickListener(editListener);
        infoTitleEditCompleteTv.setOnClickListener(editListener);

        CommonUtils.setTestMultiClick(root.findViewById(R.id.nfc_home_top), v -> refreshData(true), 3);
    }


    /**
     * 刷新网络数据
     */
    private void refreshData(boolean showProgress) {
        if (getContext() == null) {
            return;
        }
        if (secretGridView == null) {
            return;
        }

        if (showProgress) {
            NfcLoading.show(getContext());
        }
        secretInfoManager.fetchSecretData((code) -> {
            if (showProgress) {
                NfcLoading.dismiss();
            }
            if (code == 0) {
                netErrorLayout.setVisibility(View.GONE);
                secretGridView.setVisibility(View.VISIBLE);
                secretGridView.setSecretInfoList(secretInfoManager.getSavedInfos());
            } else {
                netErrorLayout.setVisibility(View.VISIBLE);
                secretGridView.setVisibility(View.GONE);
                ToastUtils.showLong(getContext(), HttpHelper.getUnknownErrorMessage(getContext(), String.valueOf(code)));
            }

            boolean noData = secretInfoManager.getSavedInfos() == null
                    || secretInfoManager.getSavedInfos().isEmpty();

            menuIcon.setVisibility(noData ? View.GONE : View.VISIBLE);
            editIcon.setVisibility(noData ? View.GONE : View.VISIBLE);
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        refreshData(false);
//        isFirstResume = false;
    }

    /**
     * 菜单弹窗
     */
    private void showBottomMenuDialog() {
        Context context = getContext();
        if (context == null) {
            return;
        }

        BlackBottomSheetDialog dialog = new BlackBottomSheetDialog(context);
        dialog.setContentView(R.layout.bottom_dialog_nfc_menu);

        dialog.getWindow().findViewById(R.id.dialog_close).setOnClickListener(v -> dialog.dismiss());
        dialog.getWindow().findViewById(R.id.bottom_dialog_nfc_menu_select).setOnClickListener(v -> {
            MobclickAgent.onEvent(getContext(), TrackEvent.NFC.NFC_SET_DEFAULT);
            if (secretInfoManager.getSavedInfos() == null || secretInfoManager.getSavedInfos().size() == 0) {
                ToastUtils.showLong(getContext(), R.string.no_secret_info_error);
            } else {
                startActivityForResult(new Intent(context, NfcSecretSelectActivity.class), REQUEST_CODE_ACTIVITY_SET_SELECT);
                requireActivity().overridePendingTransition(R.anim.activity_fade_in, R.anim.activity_fade_out);
            }
            dialog.dismiss();
        });
        dialog.getWindow().findViewById(R.id.bottom_dialog_nfc_menu_passwd).setOnClickListener(v -> {
            MobclickAgent.onEvent(getContext(), TrackEvent.NFC.NFC_SET_PASS_CODE);
            startActivity(new Intent(context, NfcSecretPasswdActivity.class));
            requireActivity().overridePendingTransition(R.anim.activity_fade_in, R.anim.activity_fade_out);
            dialog.dismiss();
        });
        dialog.getWindow().findViewById(R.id.bottom_dialog_nfc_menu_domain).setOnClickListener(v -> {
            MobclickAgent.onEvent(getContext(), TrackEvent.NFC.NFC_SET_DOMAIN);
            startActivity(new Intent(context, NfcDomainActivity.class));
            requireActivity().overridePendingTransition(R.anim.activity_fade_in, R.anim.activity_fade_out);
            dialog.dismiss();
        });

        dialog.show();
    }

    /**
     * 展示 信息添加面板
     */
    private void showSecretAddGalleryDialog() {
        Context context = getContext();
        if (context == null) {
            return;
        }

        ConstraintLayout layout = new ConstraintLayout(context);
        BlackBottomSheetDialog dialog = new BlackBottomSheetDialog(context, layout);

        // title
        TextView title = new TextView(context);
        title.setTextColor(getResources().getColor(R.color.b_title_text_color));
        title.setTextSize(17);
        title.setText(R.string.select_the_info_to_add);
        title.setId(R.id.title_container);

        ConstraintLayout.LayoutParams lp = new ConstraintLayout.LayoutParams(-2, -2);
        lp.leftMargin = Apputils.dp2px(context, 20);
        lp.topMargin = Apputils.dp2px(context, 20);
        lp.leftToLeft = ConstraintLayout.LayoutParams.PARENT_ID;
        lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID;

        layout.addView(title, lp);

        // 关闭按钮
        ImageView close = new ImageView(context);
        close.setImageResource(R.drawable.icon_close_grey);
        close.setOnClickListener(v -> dialog.dismiss());

        lp = new ConstraintLayout.LayoutParams(-2, -2);
        lp.rightMargin = Apputils.dp2px(context, 14);
        lp.topMargin = Apputils.dp2px(context, 14);
        lp.rightToRight = ConstraintLayout.LayoutParams.PARENT_ID;
        lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID;

        layout.addView(close, lp);


        // 主题表格
        SecretGridView gridView = new SecretGridView(context);
        gridView.setIconBackground(R.drawable.shape_secret_icon_bg_1);
        gridView.setSecretInfoList(secretInfoManager.getOtherInfos());
        gridView.setSecretInfoClickListener(new SecretGridView.SecretInfoClickListener() {
            @Override
            public void onClick(SecretInfoBean bean) {
                detailSecretInfo(bean, true);
                dialog.dismiss();
            }

            @Override
            public void onAdditionalClick(int additionalType) {
            }
        });
        gridView.setPadding(0, 0, 0, Apputils.dp2px(context, 30));

        lp = new ConstraintLayout.LayoutParams(-1, -2);
        lp.topToBottom = R.id.title_container;
        lp.topMargin = Apputils.dp2px(context, 24);
        layout.addView(gridView, lp);

//      设置是否默认折叠
        BottomSheetBehavior<View> behavior = BottomSheetBehavior.
                from((View) layout.getParent());
        behavior.setSkipCollapsed(true);
        behavior.setHideable(true);

        dialog.show();
    }

    /**
     * 如果给定的原始有匹配的 URL, 则提取对应的 URL, 否则返回 null;
     * <p>
     * 特殊情况, 如果整个 text 就是 URL, 则按照无提取处理, 返回 null;
     *
     * @return
     */
    private String extractUrlAndNotAll(String text) {
        if (!TextUtils.isEmpty(text)) {
            Pattern pattern = Pattern.compile("([hH][tT]{2}[pP]://|[hH][tT]{2}[pP][sS]://|[wW]{3}.|[wW][aA][pP].|[fF][tT][pP].|[fF][iI][lL][eE].)[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]");
            Matcher matcher = pattern.matcher(text);
            if (!matcher.matches() && matcher.find()) {
                return matcher.group();
            }
        }
        return null;
    }


    /**
     * 添加指定类型的秘密信息
     *
     * @param bean
     * @param isAdd true 表示添加条目, false 表示编辑
     */
    private void detailSecretInfo(SecretInfoBean bean, boolean isAdd) {
        if (bean == null || bean.getType() == null || getContext() == null) {
            return;
        }

        if (isAdd) {
            MobclickAgent.onEvent(getContext(), TrackEvent.NFC.NFC_ADD_INFO_PREFIX + bean.getType().type);
        }

        switch (bean.getType().type) {
            case SecretType.TYPE_WECHAT:
                // 微信二维码上传逻辑
                showWechatSecretAddDialog(bean, isAdd);
                break;
            case SecretType.TYPE_BUSINESS:
            case SecretType.TYPE_SOCIAL:
                startActivityForResult(
                        new Intent(getContext(), NfcCardEditActivity.class)
                                .putExtra(NfcCardEditActivity.EXTRA_NFC_SECRET_CARD_DATA, bean),
                        REQUEST_CODE_ACTIVITY_ADD_CARD);
                requireActivity().overridePendingTransition(R.anim.activity_fade_in, R.anim.activity_fade_out);
                break;
            case SecretType.TYPE_MEDIA:
                startActivityForResult(
                        new Intent(getContext(), NfcMediaListActivity.class)
                                .putExtra(NfcMediaListActivity.EXTRA_NFC_SECRET_MEDIA_DATA, bean),
                        REQUEST_CODE_ACTIVITY_MEDIA_LIST);
                requireActivity().overridePendingTransition(R.anim.activity_fade_in, R.anim.activity_fade_out);
                break;
            case SecretType.TYPE_CUSTOM:
                showCommonSecretAddDialog(bean, true, isAdd);
                break;
            default:
                showCommonSecretAddDialog(bean, false, isAdd);
                break;
        }
    }

    /**
     * 展示微信的添加底部提示框
     */
    private void showWechatSecretAddDialog(SecretInfoBean bean, boolean isAdd) {
        Context context = getContext();
        if (context == null || bean == null || bean.getType() == null) {
            return;
        }
        AtomicReference<Bitmap> wechatPic = new AtomicReference<>();

        BlackBottomSheetDialog dialog = new BlackBottomSheetDialog(context);
        dialog.setContentView(R.layout.bottom_dialog_wechat_upload);

        ImageView wechatImage = dialog.getWindow().findViewById(R.id.bottom_dialog_wechat_image);
        View reSelectTv = dialog.getWindow().findViewById(R.id.bottom_dialog_wechat_reselect);


        dialog.getWindow().findViewById(R.id.dialog_close).setOnClickListener(v -> dialog.dismiss());
        View.OnClickListener pickImageListener = v -> {
            // 选择图片文件
            try {
                imagePickedCallback = image -> {
                    wechatImage.setImageDrawable(new BitmapDrawable(getResources(), image));

                    reSelectTv.setVisibility(View.VISIBLE);
                    wechatPic.set(image);
                };
                Intent intent = new Intent(Intent.ACTION_PICK, null);
                intent.setDataAndType(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, "image/*");
                startActivityForResult(intent, REQUEST_CODE_ACTIVITY_PICK_IMAGE);
                requireActivity().overridePendingTransition(R.anim.activity_fade_in, R.anim.activity_fade_out);
            } catch (Exception e) {
                e.printStackTrace();
                BlackAlertDialogUtil.showCommonDialog(getContext(), R.string.open_camera_error1, R.string.immediately_receive,
                        () -> startActivity(new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, Uri.parse("package:" + context.getPackageName()))));
            }

        };
        dialog.getWindow().findViewById(R.id.bottom_dialog_wechat_upload).setOnClickListener(pickImageListener);
        reSelectTv.setOnClickListener(pickImageListener);

        if (!TextUtils.isEmpty(bean.getValue())) {
            try {
                Glide.with(this).load(HttpHelper.getRealImageUrl(bean.getValue())).addListener(new RequestListener<Drawable>() {
                    @Override
                    public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Drawable> target, boolean isFirstResource) {
                        return false;
                    }

                    @Override
                    public boolean onResourceReady(Drawable resource, Object model, Target<Drawable> target, DataSource dataSource, boolean isFirstResource) {
                        reSelectTv.setVisibility(View.VISIBLE);
                        return false;
                    }
                }).into(wechatImage);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // 点击保存, 做上传图片的处理
        dialog.getWindow().findViewById(R.id.bottom_dialog_add_save).setOnClickListener(v -> {
            if (wechatPic.get() == null) {
                ToastUtils.showLong(getContext(), R.string.no_qr_code);
                LogUtils.w("Get wechat pic error!");
                return;
            }
            final String[] wechatUrl = {null};
            // 上传图片, 并更新信息
            NfcLoading.show(context);

            ByteArrayOutputStream pics = new ByteArrayOutputStream();
            wechatPic.get().compress(Bitmap.CompressFormat.JPEG, 90, pics);
            HttpHelper.uploadFile(9, "wechatPic", pics.toByteArray())
                    .flatMap((Func1<String, Observable<HttpBaseBean<JsonElement>>>) url -> {
                        wechatUrl[0] = HttpHelper.getRealImageUrl(url);
                        if (bean.getId() == null) {
                            return HttpHelper.nfcService.save(SecretType.TYPE_WECHAT, wechatUrl[0], "", "");
                        } else {
                            return HttpHelper.nfcService.updateNfc(bean.getId(), SecretType.TYPE_WECHAT, wechatUrl[0], "", "");
                        }
                    }).compose(HttpHelper.rxSchedulerHelper())
                    .subscribe(new Observer<HttpBaseBean<JsonElement>>() {
                        @Override
                        public void onCompleted() {
                            NfcLoading.dismiss();
                        }

                        @Override
                        public void onError(Throwable e) {
                            NfcLoading.dismiss();
                            if (getContext() == null) {
                                return;
                            }
                            if (!NetworkUtils.isConnected()) {
                                ToastUtils.showLong(getContext(), R.string.error_net);
                            } else  {
                                ToastUtils.showLong(getContext(), HttpHelper.getUnknownErrorMessage(getContext(), e.getMessage()));
                            }
                        }

                        @Override
                        public void onNext(HttpBaseBean<JsonElement> baseBean) {
                            if (baseBean.getErrorCode() == 0) {
                                if (getContext() != null) {
                                    ToastUtils.showLong(getContext(), R.string.saved_success);
                                }
                                bean.setValue(wechatUrl[0]);
                                if (isAdd) {
                                    secretInfoManager.getSavedInfos().add(bean);
                                    secretGridView.notifyItemAddOrNotify(bean);

                                    menuIcon.setVisibility(View.VISIBLE);
                                    editIcon.setVisibility(View.VISIBLE);
                                }
                                dialog.dismiss();
                            } else {
                                if (getContext() != null) {
                                    ToastUtils.showLong(getContext(), HttpHelper.getUnknownErrorMessage(getContext(), String.valueOf(baseBean.getErrorCode())));
                                }
                            }
                        }
                    });
        });
        dialog.setOnDismissListener(dialog1 -> {
            imagePickedCallback = null;
        });
        dialog.show();
    }

    /**
     * 展示微信的添加底部提示框
     */
    private void showCommonSecretAddDialog(SecretInfoBean bean, boolean isCustom, boolean isAdd) {
        Context context = getContext();
        if (context == null || bean == null || bean.getType() == null) {
            return;
        }
        SecretType secretType = bean.getType();
        BlackBottomSheetDialog dialog = new BlackBottomSheetDialog(context);
        dialog.setContentView(isCustom ? R.layout.bottom_dialog_secret_add_url : R.layout.bottom_dialog_secret_add_common);

        TextView save = dialog.getWindow().findViewById(R.id.bottom_dialog_add_save);
        EditText urlEt = dialog.getWindow().findViewById(R.id.bottom_dialog_add_url_et);
        if (bean.getValue() != null) {
            urlEt.setText(bean.getValue());
        }
        EditText urlTitleEt = dialog.getWindow().findViewById(R.id.bottom_dialog_add_url_title_et);
        dialog.getWindow().findViewById(R.id.dialog_close).setOnClickListener(v -> dialog.dismiss());

        // 抖音, 快手平台, 需要自动截取 URL; 如果后续其他平台也需要, 在这里增加判断即可
        if (bean.getType().type.equals(SecretType.TYPE_DOUYIN) || bean.getType().type.equals(SecretType.TYPE_KUAISHOU)) {
            urlEt.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {
                }

                @Override
                public void afterTextChanged(Editable s) {
                    String url = extractUrlAndNotAll(s.toString());
                    if (url != null) {
                        urlEt.setText(url);
                        urlEt.setSelection(url.length());
                    }
                }
            });
        }

        if (!isCustom) {
            // 常规信息
            TextView helpTitle = dialog.getWindow().findViewById(R.id.bottom_dialog_add_help_title);
            TextView helpContent = dialog.getWindow().findViewById(R.id.bottom_dialog_add_help_content);

            if (secretType.addHelpTitleRes != 0) {
                helpTitle.setText(secretType.addHelpTitleRes);
            } else {
                helpTitle.setVisibility(View.GONE);
            }

            if (secretType.addHelpContentRes != 0) {
                helpContent.setText(secretType.addHelpContentRes);
            } else {
                helpContent.setVisibility(View.GONE);
            }

            if (secretType.addHintTes != 0) {
                urlEt.setHint(secretType.addHintTes);
            }
        } else {
            // 自定义链接
            if (bean.getName() != null) {
                urlTitleEt.setText(bean.getName());
            }
        }

        save.setOnClickListener(v -> {
            // 检查输入
            if (urlEt.getText().length() == 0) {
                ToastUtils.showLong(context, R.string.nfc_secret_no_url_error);
                return;
            } else if (isCustom && urlTitleEt.getText().length() == 0) {
                ToastUtils.showLong(context, R.string.nfc_secret_no_url_error);
                return;
            }

            bean.setValue(urlEt.getText().toString());
            if (isCustom) {
                bean.setName(urlTitleEt.getText().toString());

                // 如果首次设置, 设置随机颜色值
                if (bean.getExtraData() == null) {
                    bean.setExtraData(new HashMap<>());
                }
                if (!bean.getExtraData().containsKey("color") || TextUtils.isEmpty(bean.getExtraData().get("color"))) {
                    bean.getExtraData().put("color", bean.getType().getRandomBgColor());
                }
            }

            NfcLoading.show(context);
            secretInfoManager.saveSecretData(bean, code -> {
                NfcLoading.dismiss();
                if (code == 0) {
                    if (getContext() != null) {
                        ToastUtils.showLong(getContext(), R.string.saved_success);
//                        BlackAlertDialogUtil.showSingleButtonDialog(getContext(), R.string.nfc_secret_add_success_info, R.string.i_know, null);
                    }
                    if (isAdd) {
                        secretInfoManager.getSavedInfos().add(bean);

                        menuIcon.setVisibility(View.VISIBLE);
                        editIcon.setVisibility(View.VISIBLE);
                    }
                    secretGridView.notifyItemAddOrNotify(bean);
                    dialog.dismiss();
                } else {
                    ToastUtils.showLong(getContext(), HttpHelper.getUnknownErrorMessage(getContext(), String.valueOf(code)));
                }
            });
        });
        dialog.show();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        LogUtils.d("requestCode: " + requestCode + ", resultCode: " + requestCode + ", data: " + (data == null ? null : data.getDataString()));

        if (requestCode == REQUEST_CODE_ACTIVITY_ADD_CARD && resultCode == Activity.RESULT_OK && data != null) {
            // 添加名片成功
            if (secretGridView != null) {
                secretGridView.notifyItemAddOrNotify((SecretInfoBean) data.getSerializableExtra(NfcCardEditActivity.EXTRA_NFC_SECRET_CARD_DATA));
            }

            menuIcon.setVisibility(View.VISIBLE);
            editIcon.setVisibility(View.VISIBLE);
        } else if (requestCode == REQUEST_CODE_ACTIVITY_MEDIA_LIST && data != null) {
            SecretInfoBean bean = (SecretInfoBean) data.getSerializableExtra(NfcMediaListActivity.EXTRA_NFC_SECRET_MEDIA_DATA);

            if (secretGridView != null) {
                if (bean == null || bean.getListData() == null || bean.getListData().isEmpty()) {
                    secretGridView.notifyItemRemove(bean);
                } else {
                    secretGridView.notifyItemAddOrNotify(bean);
                }
            }

            boolean noData = secretInfoManager.getSavedInfos() == null
                    || secretInfoManager.getSavedInfos().isEmpty();

            menuIcon.setVisibility(noData ? View.GONE : View.VISIBLE);
            editIcon.setVisibility(noData ? View.GONE : View.VISIBLE);

            // 情况较复杂, 做一次刷新
            refreshData(false);
        } else if (requestCode == REQUEST_CODE_ACTIVITY_PICK_IMAGE && resultCode == Activity.RESULT_OK && data != null && data.getData() != null) {
            Uri uri = data.getData();

            try {
                if (imagePickedCallback != null) {
                    Bitmap bitmap = CommonUtils.rotateBitmapByDegree(BitmapFactory.decodeStream(getContext().getContentResolver().openInputStream(uri)), CommonUtils.getBitmapDegree(RealPathFromUriUtils.getRealPathFromUri(getContext(), uri)));
                    imagePickedCallback.onPick(bitmap);
                }
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            }
        } else if (requestCode == REQUEST_CODE_ACTIVITY_SET_SELECT && secretGridView != null) {
            secretGridView.notifyAllItem();
        }
    }

    public boolean onBackPress() {
        if (secretGridView != null && secretGridView.isEditMode()) {
            secretGridView.toggleEditMode();
            return true;
        }
        return false;
    }


    interface ImagePickedCallback {
        void onPick(Bitmap image);
    }


}