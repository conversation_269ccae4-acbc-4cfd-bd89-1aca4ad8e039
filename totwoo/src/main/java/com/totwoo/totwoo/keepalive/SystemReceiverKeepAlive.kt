package com.totwoo.totwoo.keepalive

import android.bluetooth.BluetoothAdapter
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.net.ConnectivityManager
import android.os.Build
import android.os.Handler
import android.os.Looper
import com.totwoo.library.util.LogUtils
import com.totwoo.totwoo.ble.BleParams
import com.totwoo.totwoo.service.KeepAliveService

/**
 * 保活系统事件广播接收器 - Kotlin简化版
 * 
 * 功能：
 * 1. 监听屏幕开关事件
 * 2. 监听蓝牙状态变化
 * 3. 监听网络状态变化
 * 4. 监听设备重启事件
 * 5. 触发保活机制恢复
 */
class SystemReceiverKeepAlive : BroadcastReceiver() {

    companion object {
        private const val TAG = "KeepAliveSystemReceiver"
        
        // 防抖机制 - 避免频繁触发
        private var lastScreenEventTime = 0L
        private var lastBluetoothEventTime = 0L
        private const val EVENT_DEBOUNCE_INTERVAL = 3000L // 3秒防抖
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        if (context == null || intent?.action == null) return

        LogUtils.d(TAG, "SystemReceiver已启用")

        val action = intent.action!!
        val currentTime = System.currentTimeMillis()
        LogUtils.d(TAG, "📡 收到系统广播: $action")

        // 防抖检查
        if (isEventDebounced(action, currentTime)) {
            LogUtils.d(TAG, "事件防抖，跳过处理: $action")
            return
        }

        when (action) {
            Intent.ACTION_SCREEN_ON -> {
                // 屏幕点亮时检查服务状态
                checkAndRestartServices(context, "SCREEN_ON")
            }
            
            Intent.ACTION_SCREEN_OFF -> {
                // 屏幕关闭时检查服务状态
                checkAndRestartServices(context, "SCREEN_OFF")
            }
            
            ConnectivityManager.CONNECTIVITY_ACTION -> {
                checkAndRestartServices(context, "NETWORK_CHANGED")
            }
            
            Intent.ACTION_BOOT_COMPLETED,
            Intent.ACTION_MY_PACKAGE_REPLACED,
            Intent.ACTION_PACKAGE_REPLACED -> {
                handleBootCompleted(context)
            }
            
            Intent.ACTION_POWER_CONNECTED -> {
                checkAndRestartServices(context, "POWER_CONNECTED")
            }
            
            Intent.ACTION_POWER_DISCONNECTED -> {
                checkAndRestartServices(context, "POWER_DISCONNECTED")
            }
            
            BluetoothAdapter.ACTION_STATE_CHANGED -> {
                checkAndRestartServices(context, "BLUETOOTH_STATE_CHANGED")
            }
        }
    }

    /**
     * 处理设备重启完成
     */
    private fun handleBootCompleted(context: Context) {
        // 延迟启动，等待系统完全启动
        Handler(Looper.getMainLooper()).postDelayed({
            checkAndStartServices(context, "BOOT_COMPLETED")
        }, 10000) // 延迟10秒
    }

    /**
     * 检查并重启服务 - 直接启动
     */
    private fun checkAndRestartServices(context: Context, trigger: String) {
        try {
            // 直接启动KeepAliveService
            val serviceIntent = Intent(context, KeepAliveService::class.java).apply {
                action = BleParams.ACTION_KEEP_ALIVE
                putExtra("trigger", trigger)
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent)
            } else {
                context.startService(serviceIntent)
            }

            LogUtils.d(TAG, "服务启动成功: $trigger")
        } catch (e: Exception) {
            LogUtils.e(TAG, "服务启动失败: ${e.message}")
        }
    }

    /**
     * 检查并启动服务 - 用于开机启动
     */
    private fun checkAndStartServices(context: Context, trigger: String) {
        try {
            // 直接启动KeepAliveService
            val serviceIntent = Intent(context, KeepAliveService::class.java).apply {
                action = BleParams.ACTION_START
                putExtra("trigger", trigger)
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent)
            } else {
                context.startService(serviceIntent)
            }

            LogUtils.d(TAG, "服务启动成功: $trigger")
        } catch (e: Exception) {
            LogUtils.e(TAG, "服务启动失败: ${e.message}")
        }
    }

    /**
     * 检查事件是否需要防抖
     */
    private fun isEventDebounced(action: String, currentTime: Long): Boolean {
        return when (action) {
            Intent.ACTION_SCREEN_ON,
            Intent.ACTION_SCREEN_OFF -> {
                if (currentTime - lastScreenEventTime < EVENT_DEBOUNCE_INTERVAL) {
                    true
                } else {
                    lastScreenEventTime = currentTime
                    false
                }
            }
            
            BluetoothAdapter.ACTION_STATE_CHANGED -> {
                if (currentTime - lastBluetoothEventTime < EVENT_DEBOUNCE_INTERVAL) {
                    true
                } else {
                    lastBluetoothEventTime = currentTime
                    false
                }
            }
            
            else -> false
        }
    }
}
