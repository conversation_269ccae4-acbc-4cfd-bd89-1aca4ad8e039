package com.totwoo.totwoo.keepalive

import android.content.Context
import android.content.Intent
import android.os.Build
import android.text.TextUtils
import androidx.core.app.NotificationCompat
import androidx.work.*
import com.tencent.mars.xlog.Log
import com.totwoo.totwoo.R
import com.totwoo.totwoo.ble.BleParams
import com.totwoo.totwoo.ble.BluetoothManage
import com.totwoo.totwoo.service.KeepAliveService
import com.totwoo.totwoo.utils.PreferencesUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.concurrent.TimeUnit

/**
 * 蓝牙保活CoroutineWorker - 根据Android官方文档优化
 * 
 * 参考文档：
 * https://developer.android.com/develop/background-work/background-tasks/persistent/how-to/long-running
 * 
 * 功能：
 * 1. 20分钟周期检查服务状态
 * 2. 检查蓝牙连接状态并尝试重连
 * 3. 复用KeepAliveService的前台服务和通知
 * 4. 异常自动恢复和重试机制
 * 5. 使用协程提供更好的异步处理
 * 6. 声明为connectedDevice前台服务类型
 */
class WorkerKeepAlive(
    context: Context,
    workerParams: WorkerParameters
) : CoroutineWorker(context, workerParams) {

    companion object {
        private const val TAG = "BluetoothKeepAliveWorker"
        private const val WORK_NAME = "bluetooth_keep_alive_work"
        private const val WORK_TAG = "bluetooth_keep_alive"

        /**
         * 调度WorkManager任务 - 根据官方文档优化
         */
        @JvmStatic
        fun scheduleWork(context: Context) {
            Log.d(TAG, "开始调度CoroutineWorker保活任务")
            
            // 根据官方文档建议的约束条件
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.NOT_REQUIRED)  // 不需要网络
                .setRequiresBatteryNotLow(false)  // 不要求电池充足，保活更重要
                .setRequiresCharging(false)       // 不要求充电状态
                .setRequiresDeviceIdle(false)     // 不要求设备空闲
                .setRequiresStorageNotLow(true)   // 要求存储空间充足
                .build()

            // 20分钟周期任务
            val workRequest = PeriodicWorkRequestBuilder<WorkerKeepAlive>(20, TimeUnit.MINUTES)
                .setConstraints(constraints)
                .setBackoffCriteria(BackoffPolicy.EXPONENTIAL, 1, TimeUnit.MINUTES)  // 失败时指数退避
                .addTag(WORK_TAG)
                .setInitialDelay(1, TimeUnit.MINUTES)  // 1分钟后开始第一次执行
                .build()

            // 使用REPLACE策略确保只有一个实例运行
            WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                WORK_NAME,
                ExistingPeriodicWorkPolicy.REPLACE,  // 替换现有任务
                workRequest
            )

            Log.d(TAG, "✅ CoroutineWorker保活任务调度成功 - 20分钟周期")
        }

        /**
         * 取消WorkManager任务
         */
        @JvmStatic
        fun cancelWork(context: Context) {
            try {
                val workManager = WorkManager.getInstance(context)
                
                // 取消特定的工作
                workManager.cancelUniqueWork(WORK_NAME)
                
                // 也可以通过标签取消
                workManager.cancelAllWorkByTag(WORK_TAG)
                
                Log.d(TAG, "✅ CoroutineWorker保活任务已取消")
            } catch (e: Exception) {
                Log.e(TAG, "取消WorkManager任务失败: ${e.message}")
            }
        }

    }

    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "🔍 CoroutineWorker保活检查开始 - 运行次数: $runAttemptCount")

        try {
            // 1. 检查是否有配对的蓝牙设备
            if (!hasBluetoothJewelry()) {
                Log.d(TAG, "没有配对的蓝牙首饰，跳过保活检查")
                return@withContext Result.success()
            }

            // 2. 不需要设置前台服务，因为KeepAliveService已经是前台服务了

            // 3. 确保KeepAliveService运行
            if (!ensureServiceRunning()) {
                Log.w(TAG, "KeepAliveService启动失败，将重试")
                return@withContext Result.retry()
            }

            // 4. 检查蓝牙连接状态
            val isConnected = BluetoothManage.getInstance().isConnected
            Log.d(TAG, "蓝牙连接状态: ${if (isConnected) "已连接" else "未连接"}")

            // 5. 如果未连接，尝试重连
            if (!isConnected) {
                BluetoothManage.getInstance().reconnect(false)
            } else {
                // 6. 发送心跳包（保持连接活跃）
                sendHeartbeatPacket()
            }

            // 7. 更新保活统计
            updateKeepAliveStats(isConnected)

            // 生成检查报告
            val duration = System.currentTimeMillis() - startTime
            val actualInterval = System.currentTimeMillis() - PreferencesUtils.getLong(applicationContext, "last_worker_execution", 0L)

            Log.d(TAG, "✅ CoroutineWorker保活检查完成，耗时: ${duration}ms")
            Log.d(TAG, "📊 实际执行间隔: ${actualInterval / 1000 / 60}分钟 (预期20分钟)")

            // 记录本次执行时间，用于计算下次间隔
            PreferencesUtils.put(applicationContext, "last_worker_execution", System.currentTimeMillis())

            Result.success()
            
        } catch (e: Exception) {
            Log.e(TAG, "CoroutineWorker保活检查失败: ${e.message}")
            e.printStackTrace()
            
            // 根据重试次数决定策略
            if (runAttemptCount < 3) {
                Log.d(TAG, "将在1分钟后重试，当前重试次数: $runAttemptCount")
                Result.retry()
            } else {
                Log.e(TAG, "重试次数过多，标记为失败")
                Result.failure()
            }
        }
    }



    /**
     * 检查是否有蓝牙首饰
     */
    private fun hasBluetoothJewelry(): Boolean {
        val jewelryName = PreferencesUtils.getString(applicationContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "")
        return !TextUtils.isEmpty(jewelryName) && BleParams.isBluetoothJewelry(jewelryName)
    }

    /**
     * 确保KeepAliveService运行
     * 复用现有的前台服务，简单可靠
     */
    private fun ensureServiceRunning(): Boolean {
        return try {
            val serviceIntent = Intent(applicationContext, KeepAliveService::class.java).apply {
                action = BleParams.ACTION_KEEP_ALIVE
                putExtra("source", "CoroutineWorker")
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                applicationContext.startForegroundService(serviceIntent)
            } else {
                applicationContext.startService(serviceIntent)
            }

            Log.i(TAG, "KeepAliveService启动成功")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "启动KeepAliveService失败: ${e.message}")
            false
        }
    }

    /**
     * 发送心跳包 - 增强版
     */
    private fun sendHeartbeatPacket() {
        BluetoothManage.getInstance().readBattery()
    }

    /**
     * 更新保活统计信息
     */
    private fun updateKeepAliveStats(isConnected: Boolean) {
        try {
            // 更新最后检查时间
            PreferencesUtils.put(applicationContext, "last_keep_alive_check", System.currentTimeMillis())
            
            if (isConnected) {
                // 连接成功，重置失败计数
                PreferencesUtils.put(applicationContext, "keep_alive_failure_count", 0)
                PreferencesUtils.put(applicationContext, "last_successful_connection", System.currentTimeMillis())
            } else {
                // 连接失败，增加失败计数
                val failureCount = PreferencesUtils.getInt(applicationContext, "keep_alive_failure_count", 0)
                PreferencesUtils.put(applicationContext, "keep_alive_failure_count", failureCount + 1)
            }
            
            // 更新总检查次数
            val totalChecks = PreferencesUtils.getInt(applicationContext, "total_keep_alive_checks", 0)
            PreferencesUtils.put(applicationContext, "total_keep_alive_checks", totalChecks + 1)
            
        } catch (e: Exception) {
            Log.e(TAG, "更新保活统计失败: ${e.message}")
        }
    }
}
