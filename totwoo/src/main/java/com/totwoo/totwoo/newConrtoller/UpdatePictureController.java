package com.totwoo.totwoo.newConrtoller;

import android.content.Context;
import android.graphics.Bitmap;

import com.blankj.utilcode.util.ImageUtils;
import com.bumptech.glide.Glide;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.base.BaseContraller;
import com.etone.framework.component.http.HttpParams;
import com.etone.framework.component.http.HttpUtils;
import com.etone.framework.event.EventBus;
import com.etone.framework.event.EventData;
import com.etone.framework.event.TaskType;
import com.etone.framework.utils.JSONUtils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.holderBean.GetQiNiuToken;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.bean.holderBean.QiNiuResponse;
import com.totwoo.totwoo.controller.HttpValues;
import com.totwoo.totwoo.utils.FileUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.library.bitmap.BitmapHelper;

import java.io.File;
import java.net.URLDecoder;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * Created by xinyoulingxi on 2017/5/16.
 */

public class UpdatePictureController extends BaseContraller
{
    public static final String TMP_PICNAME = "tmp_picname";
    public static final String TMP_PICPATH = FileUtils.getCacheImageDir() + File.separator + TMP_PICNAME;

    public static final String HTTP_UPDATE_PICTURE_GET_TOKEN = "HTTP_UPDATE_PICTURE_GET_TOKEN";
    public static final String HTTP_UPDATE_PICTURE_UPDATEURL = "HTTP_UPDATE_PICTURE_UPDATEURL";
    public static final String HTTP_UPLOAD_BLE_LOG_URL = "HTTP_UPLAOD_BLE_LOG_URL";

    public static final String HTTP_USER_GET_VOICE_SMS = "HTTP_USER_GET_VOICE_SMS";

    private static final UpdatePictureController instance = new UpdatePictureController();
    private UpdatePictureController()
    {
        super();
    }

    public static UpdatePictureController getInstance()
    {
        return instance;
    }

    public void getUserVoiceMa(String phone)
    {
        if (phone == null)
            return;
        if (phone.startsWith("86"))
            phone = phone.replace("86", "");
        if (phone.startsWith("+86"))
            phone = phone.replace("+86", "");

        phone = "86" + phone;
        String url = HttpHelper.HOSTURL + "user/getVoiceSms";
        HttpValues hv = new HttpValues(HTTP_USER_GET_VOICE_SMS, url);
        hv.addParams("mobile", phone);

        LogUtils.e(url);
        HttpUtils.run(hv);
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e)
    {
        super.onEventException(eventType, data, e);
        switch (eventType)
        {
            case HTTP_USER_GET_VOICE_SMS:
                EventBus.onPostReceived(S.E.E_LOGIN_GET_VOICE_MA_FAILED, data);
                break;
        }
    }

    @EventInject(eventType = HTTP_USER_GET_VOICE_SMS, runThread = TaskType.Async)
    public void onGetUserVoiceMaFinished(EventData data)
    {
        HttpValues hv = (HttpValues) data;
        if (hv.isRequestOk())
        {
            EventBus.onPostReceived(S.E.E_LOGIN_GET_VOICE_MA_SUCCESSED, data);
        }
        else
        {
            EventBus.onPostReceived(S.E.E_LOGIN_GET_VOICE_MA_FAILED, data);
        }
    }

    public void getQiniuToken(Context context)
    {
        String url = HttpHelper.HOSTURL + "qiniu/getToken";
        HttpValues hp = new HttpValues(HTTP_UPDATE_PICTURE_GET_TOKEN, url);
        hp.addParams("fileType", "1");
        hp.addParams("sourceString", "couplebp");

        LogUtils.e(url);
        HttpUtils.run(hp);
    }

    @EventInject(eventType = HTTP_UPDATE_PICTURE_GET_TOKEN, runThread = TaskType.Async)
    public void onGetQiniuTokenFinished(EventData data)
    {
        HttpValues hp = (HttpValues) data;
        LogUtils.e(hp.content);
    }

    public void uploadBleLogUrl(Context context, String path)
    {
        String url = HttpHelper.HOSTURL + "Log/bluetoothLog";
        HttpValues hv = new HttpValues(HTTP_UPLOAD_BLE_LOG_URL, url);
        hv.putUserDefine("Context", context);
        hv.addParams("path", path);
        HttpUtils.run(hv);
    }

    @EventInject(eventType = HTTP_UPLOAD_BLE_LOG_URL, runThread = TaskType.UI)
    public void onUploadBleLogUrlFinished(EventData data)
    {
        HttpValues hv = (HttpValues) data;
        Context context = (Context) hv.getUserDefine("Context");
        if (hv.isRequestOk())
        {
            LogUtils.i("蓝牙日志上传成功");
        }
        else
        {
            LogUtils.i("蓝牙日志上传失败");
        }
    }

    //1:default, 0:上传一般的背景图片
    public void updateBackgroundPic(String updateUrl, int setDefault)
    {
        String url = HttpHelper.HOSTURL + "couple/UpdateBackgroundPicture";
        HttpValues hp = new HttpValues(HTTP_UPDATE_PICTURE_UPDATEURL, url);
        hp.addParams("backgroundPicture", updateUrl);
        hp.addParams("talkId", ToTwooApplication.owner.getPairedId());
        hp.addParams("setDefault", setDefault + "");
        HttpUtils.run(hp);
    }

    @EventInject(eventType = HTTP_UPDATE_PICTURE_UPDATEURL, runThread = TaskType.Async)
    public void onUpdateBackgroundPicFinished(EventData data)
    {
        HttpValues hv = (HttpValues) data;
        if (hv.isRequestOk())
        {
            EventBus.onPostReceived(S.E.E_BACKGROUND_UPDATED_SUCCESSED, hv);
            String content = hv.content;
            String res = JSONUtils.getString(content, "data", "");
            res = JSONUtils.getString(res, "backgroundPicture", "");
            res = URLDecoder.decode(res);
            hv.putUserDefine("updateUrl", res);
        }
        else
        {
            EventBus.onPostReceived(S.E.E_BACKGROUND_UPDATED_FAILED, hv);
        }
    }

    public void uploadPictures(final Context context, final String filePath)
    {
        LogUtils.e("aab uploadPictures");
        HttpHelper.card.getQiNiuToken(1, "couplebp")
                .subscribeOn(Schedulers.io())
                .subscribe(new Subscriber<HttpBaseBean<GetQiNiuToken>>() {
                    @Override
                    public void onCompleted() {
                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showLong(context, R.string.upload_filed);
                    }

                    @Override
                    public void onNext(HttpBaseBean<GetQiNiuToken> getQiNiuTokenHttpBaseBean) {
                        if (getQiNiuTokenHttpBaseBean.getErrorCode() == 0)
                        {
                            LogUtils.e("aab filePath:" + getQiNiuTokenHttpBaseBean.getData().getFilePath());
                            File file = new File(filePath);
                            RequestBody requestFile =
                                    RequestBody.create(MediaType.parse("multipart/form-data"), file);

                            MultipartBody.Part part = MultipartBody.Part.createFormData("file", file.getName(), requestFile);

                            HttpHelper.qiNiuApi.UploadFile(part, RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getFilePath()), RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getUpToken()))
                                    .subscribeOn(Schedulers.io())
                                    .observeOn(AndroidSchedulers.mainThread())
                                    .subscribe(new Subscriber<QiNiuResponse>() {
                                        @Override
                                        public void onCompleted() {
                                        }

                                        @Override
                                        public void onError(Throwable e) {
                                            ToastUtils.showLong(context, R.string.upload_filed);
                                        }

                                        @Override
                                        public void onNext(QiNiuResponse qiNiuResponse) {
                                            // 上传成功
                                            String url = qiNiuResponse.getKey();
                                            LogUtils.i("upload image success!" + qiNiuResponse.getKey());
                                            updateBackgroundPic(url, 0);
                                        }
                                    });
                        }
                    }
                });
    }

    /**
     * 上传情侣背景图片
     * @param context
     * @param filePath
     */
    public void uploadPairBackgroundPictures(final Context context, final String filePath)
    {
        HttpHelper.card.getQiNiuToken(1, "totwoobg")
                .subscribeOn(Schedulers.io())
                .subscribe(new Subscriber<HttpBaseBean<GetQiNiuToken>>() {
                    @Override
                    public void onCompleted() {
                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showLong(context, R.string.upload_filed);
                    }

                    @Override
                    public void onNext(HttpBaseBean<GetQiNiuToken> getQiNiuTokenHttpBaseBean) {
                        if (getQiNiuTokenHttpBaseBean.getErrorCode() == 0)
                        {
                            File file = new File(filePath);
                            if(!file.exists()){
                                ToastUtils.showShort(context,R.string.error_net);
                            }

                            //上传的图片大小不能超过600k
                            RequestBody requestFile =
                                    RequestBody.create(MediaType.parse("multipart/form-data"),  ImageUtils.compressByQuality(ImageUtils.getBitmap(filePath), 600 * 1024L, true)
                                    );

                            MultipartBody.Part part = MultipartBody.Part.createFormData("file", file.getName(), requestFile);

                            HttpHelper.qiNiuApi.UploadFile(part, RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getFilePath()), RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getUpToken()))
                                    .subscribeOn(Schedulers.io())
                                    .observeOn(AndroidSchedulers.mainThread())
                                    .subscribe(new Subscriber<QiNiuResponse>() {
                                        @Override
                                        public void onCompleted() {
                                        }

                                        @Override
                                        public void onError(Throwable e) {
//                                            ToastUtils.showLong(context, R.string.upload_filed);
                                        }

                                        @Override
                                        public void onNext(QiNiuResponse qiNiuResponse) {
                                            // 上传成功
                                            String url = qiNiuResponse.getKey();
                                            LogUtils.e("aab url = " + url);
                                            HttpHelper.commonService.updatePairBg(url)
                                                    .subscribeOn(Schedulers.newThread())
                                                    .observeOn(AndroidSchedulers.mainThread())
                                                    .subscribe(new Subscriber<HttpBaseBean<Object>>() {
                                                        @Override
                                                        public void onCompleted() {

                                                        }

                                                        @Override
                                                        public void onError(Throwable e) {
                                                            LogUtils.e("aab e = " + e);
                                                        }

                                                        @Override
                                                        public void onNext(HttpBaseBean<Object> objectHttpBaseBean) {
                                                            LogUtils.e("aab 上传成功");
                                                            // 预缓存背景图片，避免下次进入时闪烁
                                                            preloadBackgroundImage(context, url);
                                                        }
                                                    });
                                        }
                                    });
                        }
                    }
                });
    }

    /**
     * 预缓存背景图片，避免下次进入时闪烁
     */
    private void preloadBackgroundImage(Context context, String qiniuUrl) {
        try {
            String fullUrl = BitmapHelper.checkRealPath(qiniuUrl);
            // 使用Glide预加载图片到缓存
            Glide.with(context)
                .load(fullUrl)
                .preload(); // 只缓存，不显示
        } catch (Exception e) {
            // 预缓存失败不影响主流程
            LogUtils.e("预缓存背景图片失败: " + e.getMessage());
        }
    }

    public void uploadBleLog(final Context context, final String filePath)
    {
        HttpHelper.card.getQiNiuToken(4, "bluetooth").subscribeOn(Schedulers.io()).subscribe(new Subscriber<HttpBaseBean<GetQiNiuToken>>()
        {
            @Override
            public void onCompleted() {}

            @Override
            public void onError(Throwable e)
            {
                ToastUtils.showLong(context, R.string.upload_filed);
            }

            @Override
            public void onNext(HttpBaseBean<GetQiNiuToken> getQiNiuTokenHttpBaseBean)
            {
                if (getQiNiuTokenHttpBaseBean.getErrorCode() == 0)
                {
                    LogUtils.e("filePath:" + getQiNiuTokenHttpBaseBean.getData().getFilePath());
                    File file = new File(filePath);
                    RequestBody requestFile = RequestBody.create(MediaType.parse("multipart/form-data"), file);

                    MultipartBody.Part part = MultipartBody.Part.createFormData("file", file.getName(), requestFile);

                    HttpHelper.qiNiuApi.UploadFile(part, RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getFilePath()), RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getUpToken())).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread()).subscribe(new Subscriber<QiNiuResponse>()
                    {
                        @Override
                        public void onCompleted()
                        {
                        }

                        @Override
                        public void onError(Throwable e)
                        {
                            ToastUtils.showLong(context, R.string.upload_filed);
                        }

                        @Override
                        public void onNext(QiNiuResponse qiNiuResponse)
                        {
                            // 上传成功
                            String url = "http://image.totwoo.com/" + qiNiuResponse.getKey();
                            LogUtils.i("upload bluetooth success!" + qiNiuResponse.getKey());
                            LogUtils.i("upload url:" + url);
                            uploadBleLogUrl(context, url);
                        }
                    });
                }
            }
        });
    }

    public void uploadMemoryPic(final Context context, final String filePath)
    {
        try
        {
            Bitmap bitmap = FileUtils.getBitmapLow1080P(filePath);
            FileUtils.saveBitmapQuit(bitmap, TMP_PICNAME, 40);
            uploadMemoryRes(context, TMP_PICPATH, 1, "memory");
        }
        catch (Exception e)
        {
            e.printStackTrace();
            ToastUtils.showLong(context, R.string.upload_filed);
            EventBus.onPostReceived(S.E.E_MEMORY_RESOURCE_UPLOAD_FAILED, null);
        }
    }

    public void uploadMemoryRes(final Context context, final String filePath, int type, String suorceString)
    {
        HttpHelper.card.getQiNiuToken(type, suorceString).subscribeOn(Schedulers.io()).subscribe(new Subscriber<HttpBaseBean<GetQiNiuToken>>()
        {
            @Override
            public void onCompleted() {}

            @Override
            public void onError(Throwable e)
            {
                ToastUtils.showLong(context, R.string.upload_filed);
                EventBus.onPostReceived(S.E.E_MEMORY_RESOURCE_UPLOAD_FAILED, null);
            }

            @Override
            public void onNext(HttpBaseBean<GetQiNiuToken> getQiNiuTokenHttpBaseBean)
            {
                if (getQiNiuTokenHttpBaseBean.getErrorCode() == 0)
                {
                    LogUtils.e("filePath:" + getQiNiuTokenHttpBaseBean.getData().getFilePath());
                    File file = new File(filePath);
                    RequestBody requestFile = RequestBody.create(MediaType.parse("multipart/form-data"), file);

                    MultipartBody.Part part = MultipartBody.Part.createFormData("file", file.getName(), requestFile);

                    HttpHelper.qiNiuApi.UploadFile(part, RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getFilePath()), RequestBody.create(null, getQiNiuTokenHttpBaseBean.getData().getUpToken())).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread()).subscribe(new Subscriber<QiNiuResponse>()
                    {
                        @Override
                        public void onCompleted()
                        {
                        }

                        @Override
                        public void onError(Throwable e)
                        {
                            ToastUtils.showLong(context, R.string.upload_filed);
                            EventBus.onPostReceived(S.E.E_MEMORY_RESOURCE_UPLOAD_FAILED, null);
                        }

                        @Override
                        public void onNext(QiNiuResponse qiNiuResponse)
                        {
                            // 上传成功
                            String url = "http://image.totwoo.com/" + qiNiuResponse.getKey();
                            LogUtils.i("upload bluetooth success!" + qiNiuResponse.getKey());
                            LogUtils.i("upload url:" + url);
                            HttpParams hp = new HttpParams("", "");
                            hp.putUserDefine("url", url);
                            EventBus.onPostReceived(S.E.E_MEMORY_RESOURCE_UPLOAD_SUCCESSED, hp);
                        }
                    });
                }
            }
        });
    }
}
