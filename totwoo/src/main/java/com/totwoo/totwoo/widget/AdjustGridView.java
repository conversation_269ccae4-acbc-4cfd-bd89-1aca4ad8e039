package com.totwoo.totwoo.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.GridView;

/**
 * Created by totwoo on 2018/4/20.
 */

public class AdjustGridView extends GridView{
    public AdjustGridView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public AdjustGridView(Context context) {
        super(context);
    }

    public AdjustGridView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    @Override
    public void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {

        int expandSpec = MeasureSpec.makeMeasureSpec(
                Integer.MAX_VALUE >> 2, MeasureSpec.AT_MOST);
        super.onMeasure(widthMeasureSpec, expandSpec);
    }
}
