package com.totwoo.totwoo.widget;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.text.method.ScrollingMovementMethod;
import android.view.KeyEvent;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.ClickUtils;
import com.blankj.utilcode.util.PathUtils;
import com.liulishuo.magicprogresswidget.MagicProgressBar;
import com.totwoo.library.net.FileDownloadCallback;
import com.totwoo.library.net.HttpRequest;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.BuildConfig;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.bean.AppUpdateBean;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.DialogHelper;
import com.totwoo.totwoo.utils.MarketUtils;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.ToastUtils;

import java.io.File;

public class AppDownloadDialog extends Dialog {
    private final String downloadCachePath;
    BaseActivity context;
    AppUpdateBean info;
    //    private AxxPagView mpb;
    private TextView tvProgress;
    private boolean downloadComplete = false;
    private TextView btnConfirm;
    private TextView btnCancel;

    private MagicProgressBar app_downloading_mpb;


    DialogHelper.MyFunction function;
    int count = 0;
    private boolean isForeUpdate;

    public AppDownloadDialog(BaseActivity context, AppUpdateBean data, DialogHelper.MyFunction function) {
        super(context, R.style.MyDialog);
        this.context = context;
        this.info = data;

        this.function = function;
        isForeUpdate = info.getIs_force_update() == 1;

        if (isForeUpdate) {
            setCanceledOnTouchOutside(false);
            setOnKeyListener((dialog, keyCode, event) -> keyCode == KeyEvent.KEYCODE_BACK);
        }
        downloadCachePath = PathUtils.getInternalAppCachePath() + File.separator + "apk" +
                File.separator + "totwoo" + info.getLastest_version() + ".apk";
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.setContentView(R.layout.app_download_dialog);

        TextView tvVer = (TextView) findViewById(R.id.app_download_dialog_ver);
        TextView tvInfo = (TextView) findViewById(R.id.app_download_dialog_content);
        btnCancel = findViewById(R.id.btn_cancel);
        tvProgress = (TextView) findViewById(R.id.app_downloading_progress_tv);
        btnConfirm = findViewById(R.id.btnConfirm);
        app_downloading_mpb = findViewById(R.id.app_downloading_mpb);

//        mpb.setScaleMode(PAGScaleMode.Zoom);
        tvVer.setText(info.getLastest_version());
        tvInfo.setMovementMethod(ScrollingMovementMethod.getInstance());
        tvInfo.setText(info.getTxt());
        //记录  btnConfirm 点击次数，当 >2次时，强制更新调用 checkAndDownloadApk
        btnConfirm.setOnClickListener(v -> {
            if (TextUtils.equals(BuildConfig.FLAVOR, "googleplay")) {
                boolean b = MarketUtils.openGooglePlayMarket(context, context.getPackageName());
                if (!b) {
                    Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id=" + context.getPackageName()));
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK); // 为 Intent 添加新的任务栈
                    context.startActivity(intent);
                }
            } else {
                count++;
                if (count >= 2) {
                    checkAndDownloadApk(info.getUrl(), info.getLastest_version(), info.getPackage_size(), isForeUpdate);
                } else {
                    boolean jumpSuccess = MarketUtils.openAppMarket(context, info.getApp_market());
                    if (!jumpSuccess) {
                        checkAndDownloadApk(info.getUrl(), info.getLastest_version(), info.getPackage_size(), isForeUpdate);
                    }
                }
            }
        });

        if (isForeUpdate) {
            btnCancel.setVisibility(View.GONE);
        } else {
            btnCancel.setVisibility(View.VISIBLE);
            ClickUtils.expandClickArea(btnCancel, 20, 20, 20, 20);
            btnCancel.setOnClickListener(v -> {
                AppDownloadDialog.this.function.apply();
                dismiss();
            });

            setOnDismissListener(dialog -> {
                AppDownloadDialog.this.function.apply();
            });
        }

    }

    private void checkAndDownloadApk(String url, String version, long targetLength, boolean isForeUpdate) {
        if (PermissionUtil.hasStoragePermission(context)) {
            File file = new File(downloadCachePath);

            //因为断点续传的原因，file的长度没法作为是不是完整包的依据。就用了一个参数CommonArgs.APK_DOWNLOAD_SUCCESS判断。但是MessageActivity的会影响。所以判断有dialog的时候才去修改这个值
            LogUtils.d("aab file.length() = " + file.length());
            if (file.exists() && file.length() == 13386350) {
                CommonUtils.installApk(file, context);
            } else {
                if (downloadComplete) {
                    CommonUtils.installApk(file, context);
                } else {
                    downloads(context, url, file);
                }
            }
        } else {
            ToastUtils.showLong(context, "请开启存储权限");
        }
    }


    /**
     * 执行下载操作
     *
     * @param context 如果要显示弹窗, 须确保 Context 为 Activity 对象
     * @param url
     * @param target
     */
    public void downloads(@NonNull Context context, @NonNull String url, @NonNull File target) {
        if (target.getParentFile().exists()) {
            target.getParentFile().mkdirs();
        }

        HttpRequest.download(url, target, new FileDownloadCallback() {
            int lastProgress;

            @Override
            public void onDone() {
                //在这里处理任务完成的状态
                downloadComplete = true;

                ToastUtils.show(context, R.string.download_success, Toast.LENGTH_LONG);

                app_downloading_mpb.setVisibility(View.GONE);
                tvProgress.setVisibility(View.GONE);

                btnConfirm.setVisibility(View.VISIBLE);
                btnConfirm.setText(R.string.install_app);
                if (!isForeUpdate) {
                    btnCancel.setVisibility(View.VISIBLE);
                }

                CommonUtils.installApk(target, context);
            }

            @Override
            public void onStart() {
                downloadComplete = false;
                btnCancel.setVisibility(View.GONE);
                btnConfirm.setVisibility(View.GONE);

//                mpb.setVisibility(View.VISIBLE);
                app_downloading_mpb.setVisibility(View.VISIBLE);
                tvProgress.setVisibility(View.VISIBLE);
                tvProgress.setText(context.getString(R.string.downloading_progress) + " " + 0 + " %");

            }

            @Override
            public void onProgress(int progress, long networkSpeed) {
                if (lastProgress != progress) {
                    lastProgress = progress;

                    tvProgress.setText(context.getString(R.string.downloading_progress) + " " + progress + " %");
                    float percent = (float) progress / 100;
                    app_downloading_mpb.setPercent(percent);


                    if (progress % 2 == 0) {
                        CommonUtils.downloadingNotification(progress);
                    }
                }


//                if (lastProgress != progress) {
//                    lastProgress = progress;
//                    tvProgress.setText(context.getString(R.string.downloading_progress) + " " + progress + " %");
//
//                    double percent = (double) progress / 100;
//                    mpb.setProgress(percent);
//                }
//
//                CommonUtils.downloadingNotification(progress);
            }

            @Override
            public void onFailure() {
                super.onFailure();
                downloadComplete = false;

                app_downloading_mpb.setVisibility(View.GONE);
                tvProgress.setVisibility(View.GONE);

                if (!isForeUpdate) {
                    btnCancel.setVisibility(View.VISIBLE);
                }
                btnConfirm.setVisibility(View.VISIBLE);

                ToastUtils.showLong(context, R.string.error_net);
            }
        });
    }

    /**
     *
     */
    public void dismiss() {
        if (!context.isFinishing() && !context.isDestroyed() && isShowing()) {
            super.dismiss();
        }
    }
}