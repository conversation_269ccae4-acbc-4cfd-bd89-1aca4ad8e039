package com.totwoo.totwoo.widget

import android.content.Context
import android.util.AttributeSet
import android.view.ViewGroup
import android.widget.FrameLayout
import com.totwoo.totwoo.R
import org.libpag.PAGFile
import org.libpag.PAGView

/**
 * 封装pagView
 */
class AxxPagView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private val pagView: PAGView = PAGView(context)

    private var fileName: String? = null
    private var autoPlay: Boolean = false
    private var loop: Boolean = false
    private var minProgress: Float = 0f
    private var maxProgress: Float = 1f


    init {
        addView(pagView, LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT))

        context.theme.obtainStyledAttributes(attrs, R.styleable.AxxPagView, 0, 0).apply {
            try {
                fileName = getString(R.styleable.AxxPagView_pag_fileName)
                autoPlay = getBoolean(R.styleable.AxxPagView_pag_autoPlay, true)
                loop = getBoolean(R.styleable.AxxPagView_pag_loop, false)
            } finally {
                recycle()
            }
        }




        fileName?.let { setUpFile(it) }
        pagView.setRepeatCount(if (loop) 0 else 1)
        if (autoPlay) playAnimation()

    }

    /** ------------------------------
     * Lottie API 兼容方法
     * ------------------------------ */

    fun isPlaying() = pagView.isPlaying

    public fun setUpFile(mPagFilePath: String) {
        fileName = mPagFilePath
        val pagFile = PAGFile.Load(context.assets, mPagFilePath)
        pagView.composition = pagFile
    }

    fun setAnimation(mPagFilePath: String, loop: Boolean? = true, autoPlay: Boolean? = true) {
        setUpFile(mPagFilePath)
        setRepeatCount(if (loop == true) 0 else 1)
        if (autoPlay == true) playAnimation()
    }

    fun playAnimation() {
        pagView.play()
    }

    fun pauseAnimation() {
        pagView.pause()
    }

    fun resumeAnimation() {
        pagView.play()
    }

    fun cancelAnimation() {
        pagView.stop()
    }

    fun isAnimating(): Boolean {
        return pagView.isPlaying
    }

    fun setRepeatCount(count: Int) {
        pagView.setRepeatCount(count)
    }

    fun setProgress(progress: Double) {
        pagView.progress = progress
    }

    fun getProgress(): Double {
        return pagView.progress
    }

}