package com.totwoo.totwoo.widget;

import android.content.Context;
import android.graphics.Point;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.FrameLayout;

import com.totwoo.library.net.HttpRequest;
import com.totwoo.library.net.RequestParams;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.Owner;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.data.SysLocalDataBean;
import com.totwoo.totwoo.fragment.OnConfirmListener;
import com.totwoo.totwoo.utils.ConfigData;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.RequestCallBack;
import com.totwoo.totwoo.widget.WheelView.OnWheelViewListener;

import java.util.ArrayList;

public class BirthSettingView extends FrameLayout implements OnConfirmListener {
	private Context mContext;
	/** 年份范围的最小年份 */
	private final int MIN_YEAR = 1940;

	/** 生日年份 */
	private WheelView yearView;
	/** 生日月份 */
	private WheelView monthView;

	public int getN_year() {
		return n_year;
	}

	public void setN_year(int n_year) {
		this.n_year = n_year;
	}

	public int getN_month() {
		return n_month;
	}

	public void setN_month(int n_month) {
		this.n_month = n_month;
	}

	public int getN_day() {
		return n_day;
	}

	public void setN_day(int n_day) {
		this.n_day = n_day;
	}

	/** 生日日子 */
	private WheelView dayView;

	private ArrayList<String> birthDays = new ArrayList<String>();

	/** 当前表盘的日期 */
	private int n_year, n_month, n_day;

	public BirthSettingView(Context context) {
		this(context, null, 0);
	}

	public BirthSettingView(Context context, AttributeSet attrs) {
		this(context, attrs, 0);
	}

	public BirthSettingView(Context context, AttributeSet attrs,
			int defStyleAttr, int defStyleRes) {
		this(context, attrs, defStyleAttr);
	}

	public BirthSettingView(Context context, AttributeSet attrs,
			int defStyleAttr) {
		super(context, attrs, defStyleAttr);
		mContext = context;
		LayoutInflater.from(context).inflate(
				R.layout.fragment_birthday_setting, this);

		yearView = (WheelView) findViewById(R.id.setting_brithday_year_view);
		monthView = (WheelView) findViewById(R.id.setting_brithday_month_view);
		dayView = (WheelView) findViewById(R.id.setting_brithday_day_view);

//		Calendar cal = Calendar.getInstance();
//		int norYear = cal.get(Calendar.YEAR);
		// int curMonth = c.get(Calendar.MONTH) + 1;//通过Calendar算出的月数要+1
		// int curDate = c.get(Calendar.DATE);

		// NumericWheelAdapter numericWheelAdapter1 = new NumericWheelAdapter(
		// context, MIN_YEAR, norYear);xx
		// numericWheelAdapter1.setLabel("年");
		// yearView.setViewAdapter(numericWheelAdapter1);
		// yearView.setCyclic(false);// 是否可循环滑动
		// yearView.addScrollingListener(scrollListener);
		// yearView.setDrawShadows(false);
		// yearView.setCurrentItem(1988);
		// yearView.setVisibleItems(5);
		//
		// NumericWheelAdapter numericWheelAdapter2 = new NumericWheelAdapter(
		// context, 1, 12, "%02d");
		// numericWheelAdapter2.setLabel("月");
		// monthView.setViewAdapter(numericWheelAdapter2);
		// monthView.setCyclic(false);
		// monthView.addScrollingListener(scrollListener);
		// monthView.setDrawShadows(false);
		// monthView.setCurrentItem(5);
		// monthView.setVisibleItems(5);
		ConfigData.init(context);
		boolean isChinese = Apputils.systemLanguageIsChinese(context);
		String yearSuffix = isChinese ? "" : " Year";
		String monthSuffix = isChinese ? "" : " Month";
		yearView.setItems(ConfigData.BIRTH_YEAR, 5, yearSuffix);
		yearView.setOnWheelViewListener(wheelViewListener);
		monthView.setItems(ConfigData.BIRTH_MONTH, 5, monthSuffix);
		monthView.setOnWheelViewListener(wheelViewListener);
		dayView.setOnWheelViewListener(wheelViewListener);
		initDayView(yearView.getSeletedIndex() + MIN_YEAR,
				monthView.getSeletedIndex() + 1);

		// 设置默认的日期
		setDefaultValue();
	}

	/**
	 * 根据用户设置， 设置默认的日期<br>
	 * 如果用户已经设置生日， 默认问用户当前生日， 如果未设置， 则显示默认生日“1985年6月15日”
	 */
	public void setDefaultValue() {

		n_day = 15;// 日
		n_month = 6;// 月
		n_year = 1985;// 年

		Owner owner = ToTwooApplication.owner;

		if (owner != null && !TextUtils.isEmpty(owner.getBirthday())) {
			String[] days = owner.getBirthday().split("-");
			if (days.length == 3) {
				n_year = Integer.parseInt(days[0]);
				n_month = Integer.parseInt(ConfigData
						.enMonth(days[1], mContext));
				n_day = Integer.parseInt(days[2]);
			}
		}

        if (n_year < 1940){
            n_year = 1985;
        }

        if (n_month <= 0){
            n_month = 6;
        }

        if (n_day <= 0){
            n_day = 15;
        }

		yearView.setSeletion(n_year - MIN_YEAR);
		monthView.setSeletion(n_month - 1);
		dayView.setSeletion(n_day - 1);

	}

	// OnWheelScrollListener scrollListener = new OnWheelScrollListener() {
	//
	// @Override
	// public void onScrollingStarted(WheelView wheel) {
	//
	// }
	//
	// // 保存数据结果
	// @Override
	// public void onScrollingFinished(WheelView wheel) {
	// n_year = yearView.getCurrentItem() + MIN_YEAR;// 年
	// n_month = monthView.getCurrentItem() + 1;// 月
	// n_day = dayView.getCurrentItem() + 1;// 日
	//
	// initDayView(n_year, n_month);
	// }
	// };
	OnWheelViewListener wheelViewListener = new OnWheelViewListener() {
		public void onSelected(WheelView wheelView, int selectedIndex,
				String item) {
			switch (wheelView.getId()) {
			case R.id.setting_brithday_year_view:
				if (n_year != yearView.getSeletedIndex() + MIN_YEAR) {
					n_year = yearView.getSeletedIndex() + MIN_YEAR;
					initDayView(n_year, n_month);
				}
//				LogUtils.i("day", dayView.getSeletedIndex() + 1 + "");
				n_year = yearView.getSeletedIndex() + MIN_YEAR;// 年
				break;
			case R.id.setting_brithday_month_view:
				if (n_month != monthView.getSeletedIndex() + 1) {
					n_month = monthView.getSeletedIndex() + 1;
					initDayView(n_year, n_month);
				}
				n_month = monthView.getSeletedIndex() + 1;// 月
//				LogUtils.i("day", dayView.getSeletedIndex() + 1 + "");
				break;
			case R.id.setting_brithday_day_view:
				n_day = dayView.getSeletedIndex() + 1;// 日
				break;
			}

		}
    };

	/**
	 * 因为每月的天数需要动态计算， 每一次年月变动都需要重新更新
	 */
	private void initDayView(int year, int month) {
		// NumericWheelAdapter numericWheelAdapter = new NumericWheelAdapter(
		// mContext, 1, getDay(yearView.getSeletedIndex(),
		// monthView.getSeletedIndex()), "%02d");
		// numericWheelAdapter.setLabel("日");
		// dayView.setViewAdapter(numericWheelAdapter);
		// dayView.setCyclic(true);
		// dayView.setDrawShadows(false);
		// dayView.setVisibleItems(5);
		setDataToBirthDays();
		dayView.setItems(birthDays, 5, "");
	}

	private void setDataToBirthDays() {
		birthDays.clear();
		int day = getDay(yearView.getSeletedIndex() + MIN_YEAR,
				monthView.getSeletedIndex() + 1);

		boolean isChinese = Apputils.systemLanguageIsChinese(getContext());
		String daySuffix = isChinese ? "" : " Day";
		for (int i = 1; i <= day; i++) {
			birthDays.add(i + daySuffix);
		}
	}

	private int getDay(int year, int month) {
		int day = 30;
		boolean flag = false;
		switch (year % 4) {
		case 0:
			flag = true;
			break;
		default:
			flag = false;
			break;
		}
		switch (month) {
		case 1:
		case 3:
		case 5:
		case 7:
		case 8:
		case 10:
		case 12:
			day = 31;
			break;
		case 2:
			day = flag ? 29 : 28;
			break;
		default:
			day = 30;
			break;
		}
		return day;
	}

	@Override
	public void onSaved() {
		ToTwooApplication.owner.setBirthday(n_year + "-" + n_month + "-"
				+ n_day);

		// 同步服务器, 当前设置的用户信息
		RequestParams params = HttpHelper.getBaseParams(true);
		params.addFormDataPart("birthday",
				ToTwooApplication.owner.getBirthday());

        HttpRequest.post(
				HttpHelper.URL_UPDATE_USER_INFO, params,
				new RequestCallBack<String>() {

                    @Override
                    public void onLogicFailure(HttpBaseBean<String> t) {
                        super.onLogicFailure(t);
                        PreferencesUtils.put(mContext,
                                SysLocalDataBean.SYN_TYPE_USER_INFO, true);
                    }

                    @Override
					public void onFailure(int error, String msg) {
						PreferencesUtils.put(mContext,
								SysLocalDataBean.SYN_TYPE_USER_INFO, true);
					}
				});
	}

	@Override
	public Point getCenterPoint() {
		return null;
	}

	@Override
	public void loadAnim() {
	}
}
