package com.totwoo.totwoo.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.widget.CompoundButton;

@SuppressLint("NewApi")
public class BrightModeBtn extends CompoundButton {

	public BrightModeBtn(Context context, AttributeSet attrs, int defStyleAttr,
			int defStyleRes) {
		super(context, attrs, defStyleAttr, defStyleRes);
	}

	public BrightModeBtn(Context context, AttributeSet attrs, int defStyleAttr) {
		super(context, attrs, defStyleAttr);
	}

	public BrightModeBtn(Context context, AttributeSet attrs) {
		super(context, attrs);
	}

	public BrightModeBtn(Context context) {
		super(context);
	}

}
