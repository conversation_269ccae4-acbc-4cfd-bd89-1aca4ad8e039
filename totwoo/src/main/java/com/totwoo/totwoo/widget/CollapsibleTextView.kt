package com.totwoo.totwoo.widget

/**
 * @des:
 * <AUTHOR>
 * @date 2024/9/27 15:55
 */
import android.annotation.SuppressLint
import android.content.Context
import android.text.SpannableString
import android.text.TextPaint
import android.text.TextUtils
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.util.AttributeSet
import android.view.View
import android.view.View.OnClickListener
import android.widget.TextView
import com.totwoo.totwoo.R


@SuppressLint("AppCompatCustomView")
class CollapsibleTextView @JvmOverloads constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0
) : TextView(context, attrs, defStyleAttr) {

    private var mSuffixColor = -0xffff01

    private var mCollapsedLines = 1

    private var mSuffixTrigger = false

    private var mText: String? = null

    private var mCustomClickListener: OnClickListener? = null

    private var mShouldInitLayout = true

    private var mExpanded = false

    private var mCollapsedText = " Show All"
    private var mExpandedText = " Hide"


    private val mClickListener = OnClickListener { v ->
        if (!mSuffixTrigger) {
            mExpanded = !mExpanded
            applyState(mExpanded)
        }
        mCustomClickListener?.onClick(v)
    }


    init {
        val attributes = context.theme
                .obtainStyledAttributes(attrs, R.styleable.CollapsibleTextView, defStyleAttr, 0)

        mSuffixColor = attributes.getColor(R.styleable.CollapsibleTextView_suffixColor, -0xffff01)
        mCollapsedLines = attributes.getInt(R.styleable.CollapsibleTextView_collapsedLines, 1)
        mCollapsedText = attributes.getString(R.styleable.CollapsibleTextView_collapsedText)!!
        if (TextUtils.isEmpty(mCollapsedText)) mCollapsedText = " Show All"
        mExpandedText = attributes.getString(R.styleable.CollapsibleTextView_expandedText)!!
        if (TextUtils.isEmpty(mExpandedText)) mExpandedText = " Hide"
        mSuffixTrigger = attributes.getBoolean(R.styleable.CollapsibleTextView_suffixTrigger, false)

        this.mText = if (getText() == null) null else getText().toString()
        movementMethod = LinkMovementMethod.getInstance()
        super.setOnClickListener(mClickListener)
    }

    private val mClickSpanListener
            : ClickableSpan = object : ClickableSpan() {
        override fun onClick(widget: View) {
            if (mSuffixTrigger) {
                mExpanded = !mExpanded
                applyState(mExpanded)
            }
        }

        override fun updateDrawState(ds: TextPaint) {
            super.updateDrawState(ds)
            ds.isUnderlineText = false
        }
    }

    private fun applyState(expanded: Boolean) {
        if (TextUtils.isEmpty(mText)) return

        var note = mText!!
        val suffix: String
        if (expanded) {
            suffix = mExpandedText
        } else {
            if (mCollapsedLines - 1 < 0) {
                throw RuntimeException("CollapsedLines must equal or greater than 1")
            }
            val lineEnd: Int = layout.getLineEnd(mCollapsedLines - 1)
            suffix = mCollapsedText
            val newEnd = lineEnd - suffix.length - 1
            var end = /*if (newEnd > 0) newEnd else*/ lineEnd

            val paint: TextPaint = paint
            val maxWidth = mCollapsedLines * (measuredWidth - paddingLeft - paddingRight)
            while (paint.measureText(note.substring(0, end) + suffix) > maxWidth) end--
            note = note.substring(0, end-1)
        }

        val str = SpannableString(note + suffix)
        if (mSuffixTrigger) {
            str.setSpan(mClickSpanListener,
                    note.length,
                    note.length + suffix.length,
                    SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
        str.setSpan(ForegroundColorSpan(mSuffixColor),
                note.length,
                note.length + suffix.length,
                SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE)
        post { setText(str) }
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        if (mShouldInitLayout && getLineCount() > mCollapsedLines) {
            mShouldInitLayout = false
            applyState(mExpanded)
        }
    }

    fun setFullString(str: String?) {
        this.mText = str
        mShouldInitLayout = true
        setText(mText)
        requestLayout()
    }

    override fun setOnClickListener(l: OnClickListener?) {
        mCustomClickListener = l
    }

    fun isExpanded(): Boolean {
        return mExpanded
    }

    fun setExpanded(mExpanded: Boolean) {
        if (this.mExpanded != mExpanded) {
            this.mExpanded = mExpanded
            applyState(mExpanded)
        }
    }

    fun getSuffixColor(): Int {
        return mSuffixColor
    }

    fun setSuffixColor(mSuffixColor: Int) {
        this.mSuffixColor = mSuffixColor
        applyState(mExpanded)
    }

    fun getCollapsedLines(): Int {
        return mCollapsedLines
    }

    fun setCollapsedLines(mCollapsedLines: Int) {
        this.mCollapsedLines = mCollapsedLines
        mShouldInitLayout = true
        setText(mText)
    }

    fun isSuffixTrigger(): Boolean {
        return mSuffixTrigger
    }

    fun setSuffixTrigger(mSuffixTrigger: Boolean) {
        this.mSuffixTrigger = mSuffixTrigger
        applyState(mExpanded)
    }

    fun getCollapsedText(): String {
        return mCollapsedText
    }

    fun setCollapsedText(mCollapsedText: String?) {
        this.mCollapsedText = mCollapsedText!!
        applyState(mExpanded)
    }

    fun getExpandedText(): String {
        return mExpandedText
    }

    fun setExpandedText(mExpandedText: String?) {
        this.mExpandedText = mExpandedText!!
        applyState(mExpanded)
    }
}
