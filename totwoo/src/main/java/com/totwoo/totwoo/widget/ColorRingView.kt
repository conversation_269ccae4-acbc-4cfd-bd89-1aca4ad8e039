package com.totwoo.totwoo.widget

import android.animation.AnimatorSet
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.animation.DecelerateInterpolator
import android.view.animation.OvershootInterpolator
import com.blankj.utilcode.util.SizeUtils
import com.totwoo.totwoo.bean.ColorBean
import com.totwoo.totwoo.ble.BluetoothManage
import com.totwoo.totwoo.utils.NotifyUtil
import kotlin.math.atan2
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.sqrt

class ColorRingView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 防抖相关
    private var lastPreviewTime = 0L
    private val DEBOUNCE_INTERVAL = 300L // 300ms 的防抖间隔

    // 默认颜色数组
    private val defaultColors = arrayOf(
        ColorBean("RED", "#E96F6F"),
        ColorBean("PINK", "#FD9BB1"),
        ColorBean("YELLOW", "#E3D6A7"),
        ColorBean("GREEN", "#B3D59E"),
        ColorBean("BLUE", "#6DAFF2"),
        ColorBean("PURPLE", "#AF97BA"),
        ColorBean("WHITE", "#EAEAEA"),
        ColorBean("CYAN", "#A7CCCC"),
        ColorBean("ORANGE", "#ECB893")
    )

    // 当前使用的颜色数组
    private var colors: Array<ColorBean> = defaultColors

    // 绘制相关参数
    private var centerX = 0f
    private var centerY = 0f
    private var ringRadius = 0f
    private var selectedIndex = 0
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val path = Path()

    // 每个颜色块的角度
    private var sweepAngle = 360f / colors.size
    // 选中时向外凸出的距离
    private val selectedOffset = 50f
    // 圆环宽度
    private val ringWidth = SizeUtils.dp2px(45f)
    // 内圆半径(固定不变)
    private var innerRadius = 0f

    // 动画相关
    private var animatorSet: AnimatorSet? = null
    private var scaleAnimators = mutableMapOf<Int, ValueAnimator>()
    private var scales = FloatArray(colors.size) { 1f }
    private var previousSelectedIndex = -1

    init {
        paint.style = Paint.Style.FILL
        
        // 启用硬件加速以提高绘制性能和动画流畅度
        setLayerType(LAYER_TYPE_HARDWARE, null)
    }

    /**
     * 设置自定义颜色数组
     */
    fun setColors(newColors: Array<ColorBean>) {
        if (newColors.isEmpty()) return
        colors = newColors
        sweepAngle = 360f / colors.size
        scales = FloatArray(colors.size) { 1f }
        selectedIndex = 0
        invalidate()
    }

    /**
     * 重置为默认颜色数组
     */
    fun resetToDefaultColors() {
        colors = defaultColors
        sweepAngle = 360f / colors.size
        scales = FloatArray(colors.size) { 1f }
        selectedIndex = 0
        invalidate()
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        centerX = w / 2f
        centerY = h / 2f
        ringRadius = minOf(w, h) * 0.45f
        innerRadius = ringRadius - ringWidth
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        // 绘制所有颜色块
        colors.forEachIndexed { index, colorBean ->
            val colorValue = Color.parseColor(colorBean.color)
            paint.color = colorValue
            
            // 从0度开始，顺时针旋转
            val startAngle = -90f + (index * sweepAngle)
            
            path.reset()
            
            // 获取当前块的缩放比例
            val scale = scales[index]
            
            // 外圆半径 - 选中时增加
            val outerRadius = ringRadius + (selectedOffset * (scale - 1f))
            
            // 计算颜色块的起始和结束点
            val startRad = Math.toRadians((startAngle).toDouble())
            val endRad = Math.toRadians((startAngle + sweepAngle).toDouble())
            
            // 内圆弧起点
            path.moveTo(
                centerX + innerRadius * cos(startRad).toFloat(),
                centerY + innerRadius * sin(startRad).toFloat()
            )
            
            // 连接到外圆弧起点
            path.lineTo(
                centerX + outerRadius * cos(startRad).toFloat(),
                centerY + outerRadius * sin(startRad).toFloat()
            )
            
            // 绘制外圆弧
            path.arcTo(
                centerX - outerRadius,
                centerY - outerRadius,
                centerX + outerRadius,
                centerY + outerRadius,
                startAngle,
                sweepAngle,
                false
            )
            
            // 连接到内圆弧终点
            path.lineTo(
                centerX + innerRadius * cos(endRad).toFloat(),
                centerY + innerRadius * sin(endRad).toFloat()
            )
            
            // 绘制内圆弧
            path.arcTo(
                centerX - innerRadius,
                centerY - innerRadius,
                centerX + innerRadius,
                centerY + innerRadius,
                startAngle + sweepAngle,
                -sweepAngle,
                false
            )
            
            path.close()
            
            // 填充颜色
            canvas.drawPath(path, paint)
        }
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN,
            MotionEvent.ACTION_MOVE -> {
                val dx = event.x - centerX
                val dy = event.y - centerY
                
                val distance = sqrt(dx * dx + dy * dy)
                if (distance < innerRadius || distance > ringRadius + selectedOffset) {
                    return true
                }
                
                var angle = Math.toDegrees(atan2(dy.toDouble(), dx.toDouble()))
                angle = (angle + 90 + 360) % 360
                
                val newIndex = ((angle / sweepAngle).toInt()) % colors.size
                if (newIndex != selectedIndex) {
                    previousSelectedIndex = selectedIndex
                    selectedIndex = newIndex
                    startScaleAnimation()
//                    performHapticFeedback()

                    // 添加防抖预览逻辑
                    val currentTime = System.currentTimeMillis()
                    if (currentTime - lastPreviewTime >= DEBOUNCE_INTERVAL) {
                        lastPreviewTime = currentTime
                        // 获取当前选中颜色的 RGB 值
                        val colorValue = NotifyUtil.getColorValue(colors[selectedIndex].name)
                        // 调用预览方法
                        BluetoothManage.getInstance().setTouchColorPreview(colorValue)
                    }
                }
                return true
            }
        }
        return super.onTouchEvent(event)
    }

    private fun performHapticFeedback() {
        // 可以添加触觉反馈，提升交互体验
        // 这里可以使用HapticFeedbackConstants.VIRTUAL_KEY等
        performHapticFeedback(android.view.HapticFeedbackConstants.VIRTUAL_KEY)
    }

    private fun startScaleAnimation() {
        // 取消之前的动画
        animatorSet?.cancel()
        
        val newAnimatorSet = AnimatorSet()
        val animators = mutableListOf<ValueAnimator>()
        
        // 为所有颜色块创建动画
        colors.forEachIndexed { index, _ ->
            val currentScale = scales[index]
            val targetScale = if (index == selectedIndex) 1.45f else 1f // 增大选中时的缩放比例
            
            val anim = ValueAnimator.ofFloat(currentScale, targetScale).apply {
                duration = 300 // 稍微增加动画时间，让效果更明显
                
                // 为选中项使用OvershootInterpolator，有弹性效果
                // 为非选中项使用DecelerateInterpolator，平滑过渡
                interpolator = if (index == selectedIndex) {
                    OvershootInterpolator(2.0f) // 增强弹性效果
                } else {
                    DecelerateInterpolator()
                }
                
                addUpdateListener { animator ->
                    scales[index] = animator.animatedValue as Float
                    invalidate()
                }
            }
            
            animators.add(anim)
            scaleAnimators[index] = anim
        }
        
        // 将MutableList<ValueAnimator>转换为Collection<Animator>
        newAnimatorSet.playTogether(animators as Collection<android.animation.Animator>)
        newAnimatorSet.start()
        
        animatorSet = newAnimatorSet
    }

    // 获取当前选中的颜色key
    fun getSelectedColor(): String {
        return colors[selectedIndex].name
    }

    // 设置选中的颜色
    fun setSelectedColor(colorKey: String) {
        colors.forEachIndexed { index, colorBean ->
            if (colorBean.name == colorKey) {
                previousSelectedIndex = selectedIndex
                selectedIndex = index
                startScaleAnimation()
                return
            }
        }
    }
} 