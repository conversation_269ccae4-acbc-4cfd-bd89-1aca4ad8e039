package com.totwoo.totwoo.widget;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.EdgeToEdgeUtils;

import butterknife.BindView;
import butterknife.ButterKnife;

public class CommonMiddleDialog extends Dialog {
    @BindView(R.id.dialog_common_title_tv)
    TextView tvTitle;
    @BindView(R.id.dialog_common_info_tv)
    TextView tvInfo;
    @BindView(R.id.dialog_common_message_tv)
    TextView tvMessage;
    @BindView(R.id.dialog_common_cancel_tv)
    TextView tvCancel;
    @BindView(R.id.dialog_common_middle_view)
    View viewMiddleLine;
    @BindView(R.id.dialog_common_sure_tv)
    TextView tvSure;
    @BindView(R.id.dialog_common_extra_info_tv)
    TextView tvExtraInfo;
    @BindView(R.id.dialog_common_head_iv)
    ImageView ivHead;
    @BindView(R.id.dialog_common_head_view)
    View viewHead;
    @BindView(R.id.dialog_common_intro_iv)
    ImageView ivIntro;
    @BindView(R.id.dialog_common_intro_view)
    View viewIntro;
    @BindView(R.id.dialog_common_custom_fl)
    FrameLayout customView;

    private Context context;
    private View rootView;

    public CommonMiddleDialog(Context context) {
        super(context, R.style.MyDialog);
        this.context = context;
        rootView = LayoutInflater.from(context).inflate(
                R.layout.dialog_common_middle, null);
        ButterKnife.bind(this, rootView);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.setContentView(rootView);

        // 为Dialog启用Edge-to-Edge适配
        EdgeToEdgeUtils.enableEdgeToEdgeForDialog(this);

        WindowManager.LayoutParams params = getWindow().getAttributes();
        params.width = CommonUtils.dip2px(context, 300);
        getWindow().setAttributes(params);
    }

    public void setTitle(CharSequence title) {
        tvTitle.setVisibility(View.VISIBLE);
        tvTitle.setText(title);
    }

    public void setTitle(int titleId) {
        tvTitle.setVisibility(View.VISIBLE);
        tvTitle.setText(titleId);
    }

    public void setInfo(CharSequence text) {
        tvInfo.setVisibility(View.VISIBLE);
        tvInfo.setText(text);
    }

    public void setInfo(int textId) {
        tvInfo.setVisibility(View.VISIBLE);
        tvInfo.setText(textId);
    }

    public void setMessage(CharSequence text) {
        tvMessage.setVisibility(View.VISIBLE);
        tvMessage.setText(text);
    }

    public void setMessage(int textId) {
        tvMessage.setVisibility(View.VISIBLE);
        tvMessage.setText(textId);
    }

    public void setExtraInfo(CharSequence text) {
        tvExtraInfo.setVisibility(View.VISIBLE);
        tvExtraInfo.setText(text);
    }

    public void setExtraInfo(int textId) {
        tvExtraInfo.setVisibility(View.VISIBLE);
        tvExtraInfo.setText(textId);
    }
    public void setExtraInfo(CharSequence text, View.OnClickListener onClickListener) {
        tvExtraInfo.setVisibility(View.VISIBLE);
        tvExtraInfo.setText(text);
        tvExtraInfo.setOnClickListener(onClickListener);
    }

    public void setExtraInfo(int textId, View.OnClickListener onClickListener) {
        tvExtraInfo.setVisibility(View.VISIBLE);
        tvExtraInfo.setText(textId);
        tvExtraInfo.setOnClickListener(onClickListener);
    }

    public void setCancel(int textId, View.OnClickListener onClickListener) {
        tvCancel.setVisibility(View.VISIBLE);
        viewMiddleLine.setVisibility(View.VISIBLE);
        tvCancel.setText(textId);
        tvCancel.setOnClickListener(onClickListener);
    }

    public void setCancel(CharSequence text, View.OnClickListener onClickListener) {
        tvCancel.setVisibility(View.VISIBLE);
        viewMiddleLine.setVisibility(View.VISIBLE);
        tvCancel.setText(text);
        tvCancel.setOnClickListener(onClickListener);
    }

    public void setCustomView(View view){
        customView.setVisibility(View.VISIBLE);
        customView.addView(view);
    }

    public void setCancel(int textId) {
        setCancel(textId, v -> dismiss());
    }

    public void setCancel(CharSequence text) {
        setCancel(text, v -> dismiss());
    }

    public void setSure(int textId, View.OnClickListener onClickListener){
        tvSure.setText(textId);
        tvSure.setOnClickListener(onClickListener);
    }

    public void setSure(CharSequence text, View.OnClickListener onClickListener){
        tvSure.setText(text);
        tvSure.setOnClickListener(onClickListener);
    }

    public void setSure(View.OnClickListener onClickListener){
        setSure(R.string.confirm,onClickListener);
    }

    public void setHeadIcon(int resourceId){
        ivHead.setVisibility(View.VISIBLE);
        viewHead.setVisibility(View.VISIBLE);
        ivHead.setImageResource(resourceId);
    }

    public void setIntroIcon(int resourceId){
        ivIntro.setVisibility(View.VISIBLE);
        viewIntro.setVisibility(View.VISIBLE);
        ivIntro.setImageResource(resourceId);
    }


    public void dismiss() {
        try {
            super.dismiss();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
