package com.totwoo.totwoo.widget;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;

import com.totwoo.totwoo.R;

/**
 * 定制的小圆点指示器， 标识图片张数， 页码
 *
 * <AUTHOR>
 * @date 2015-2015年7月10日
 */
public class CountView extends View {
    private Paint mPaint;
    private int mCount;
    private int mSelected;
    private Bitmap mPointSelected, mPoint;
    private int mSpace;

    /**
     * @param context
     */
    public CountView(Context context) {
        super(context);
        initCountView();
    }

    /**
     * @see android.view.View#View(android.content.Context,
     * android.util.AttributeSet)
     */
    public CountView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initCountView();

    }

    private final void initCountView() {
        mPaint = new Paint();
        mPoint = BitmapFactory.decodeResource(getContext().getResources(),
                R.drawable.point_unselect_white);
        mPointSelected = BitmapFactory.decodeResource(getContext()
                .getResources(), R.drawable.point_select_white);
        mSpace = mPoint.getWidth() * 2;
        setPadding(4, 4, 4, 4);
    }

    public void setSelected(int selected) {
        this.mSelected = selected;
        invalidate();
    }

    public void setCount(int count) {
        mCount = count;
        requestLayout();
        invalidate();
    }

    /**
     * @see android.view.View#measure(int, int)
     */
    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        setMeasuredDimension(measureWidth(widthMeasureSpec),
                measureHeight(heightMeasureSpec));
    }

    /**
     * Determines the width of this view
     *
     * @param measureSpec A measureSpec packed into an int
     * @return The width of the view, honoring constraints from measureSpec
     */
    private int measureWidth(int measureSpec) {
        int result = 0;
        int specMode = MeasureSpec.getMode(measureSpec);
        int specSize = MeasureSpec.getSize(measureSpec);

        if (specMode == MeasureSpec.EXACTLY) {
            result = specSize;
        } else {
            result = (mPointSelected.getWidth() + mSpace) * mCount + getPaddingLeft()
                    + getPaddingRight();
            if (specMode == MeasureSpec.AT_MOST) {
                // Respect AT_MOST value if that was what is called for by
                // measureSpec
                result = Math.min(result, specSize);
            }
        }

        return result;
    }

    /**
     * Determines the height of this view
     *
     * @param measureSpec A measureSpec packed into an int
     * @return The height of the view, honoring constraints from measureSpec
     */
    private int measureHeight(int measureSpec) {
        int result = 0;
        int specMode = MeasureSpec.getMode(measureSpec);
        int specSize = MeasureSpec.getSize(measureSpec);

        if (specMode == MeasureSpec.EXACTLY) {
            result = specSize;
        } else {
            result = mPointSelected.getHeight() + getPaddingTop() + getPaddingBottom();
            if (specMode == MeasureSpec.AT_MOST) {
                // Respect AT_MOST value if that was what is called for by
                // measureSpec
                result = Math.min(result, specSize);
            }
        }
        return result;
    }

    /**
     * @see android.view.View#onDraw(android.graphics.Canvas)
     */
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        for (int i = 0; i < mCount; i++) {
            Bitmap tar = i == mSelected ? mPointSelected : mPoint;
            canvas.drawBitmap(tar, getPaddingLeft() + i * (mPoint.getWidth() + mSpace),
                    getHeight() / 2 - tar.getHeight() / 2, mPaint);
        }
    }
}
