package com.totwoo.totwoo.widget

import android.view.View
import androidx.viewpager2.widget.ViewPager2

class CurveTransformer: ViewPager2.PageTransformer {
    private val maxTranslateY = 70f // 最大 Y 偏移值，可根据实际调节
    private val scaleFactor = 0.72f // 缩放因子

    override fun transformPage(page: View, position: Float) {
        page.apply {
            val absPos = Math.abs(position)

            // 缩放
            val scale = 1 - (1 - scaleFactor) * absPos
            scaleX = scale
            scaleY = scale

            // 模拟“高度差”：y轴平移
            val translateY = maxTranslateY * absPos
            translationY = translateY

            // 可选：淡出透明度（非必须）
            alpha = 1 - absPos * 0.3f
        }
    }
}