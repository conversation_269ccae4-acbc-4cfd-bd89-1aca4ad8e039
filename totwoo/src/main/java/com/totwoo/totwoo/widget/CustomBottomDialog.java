package com.totwoo.totwoo.widget;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;

public class CustomBottomDialog extends Dialog {

    private View rootView;
    private TextView mTitleView;
    private TextView mInfoView;
    private ImageView mCancelView;
    private FrameLayout mMainLayout;
    private ConstraintLayout mSaveLayout;
    private TextView mSaveView;
    private ImageView mTopBg;

    public CustomBottomDialog(Context context) {
        super(context, R.style.safe_time_dialog);
        initDialog(context);
    }

    public CustomBottomDialog(Context context, int theme) {
        super(context, theme);
        initDialog(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(rootView);

        // 设置全屏， 靠底部展示
        WindowManager.LayoutParams params = getWindow().getAttributes();
        params.width = Apputils.getScreenWidth(getContext());
        getWindow().setAttributes(params);
        getWindow().setGravity(Gravity.BOTTOM);
    }

    /**
     * 初始化对话框
     *
     * @param context
     */
    private void initDialog(Context context) {
        rootView = LayoutInflater.from(context).inflate(
                R.layout.dialog_bottom, null);

        mTitleView = (TextView) rootView.findViewById(R.id.dialog_bottom_title);
        mInfoView = (TextView) rootView.findViewById(R.id.dialog_bottom_info);
        mCancelView = (ImageView) rootView.findViewById(R.id.dialog_bottom_cancel);
        mMainLayout = (FrameLayout) rootView.findViewById(R.id.dialog_bottom_main_layout);
        mSaveLayout = (ConstraintLayout) rootView.findViewById(R.id.dialog_bottom_save);
        mSaveView = (TextView) rootView.findViewById(R.id.dialog_bottom_save_tv);
        mTopBg = (ImageView) rootView.findViewById(R.id.dialog_bottom_top_bg);

        mCancelView.setOnClickListener(v -> dismiss());
    }

    public void setSaveClick(View.OnClickListener onClickListener){
        mSaveLayout.setVisibility(View.VISIBLE);
        mSaveView.setOnClickListener(onClickListener);
    }

    public void setSaveClick(CharSequence text,View.OnClickListener onClickListener){
        mSaveLayout.setVisibility(View.VISIBLE);
        mSaveView.setOnClickListener(onClickListener);
        mSaveView.setText(text);
    }

    public void setTitle(CharSequence title){
        mTitleView.setText(title);
    }
    
    public void setInfo(CharSequence info){
        mInfoView.setText(info);
        mInfoView.setVisibility(View.VISIBLE);
    }

    public void setMainView(View view){
        mMainLayout.addView(view);
    }

    public void setTopBgRes(int res){
        mTopBg.setImageResource(res);
    }

    public void setCancelRes(int res){
        mCancelView.setImageResource(res);
    }
}
