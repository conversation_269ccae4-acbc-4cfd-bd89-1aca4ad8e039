package com.totwoo.totwoo.widget;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager.LayoutParams;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.StyleRes;

import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.fragment.OnConfirmListener;
import com.totwoo.totwoo.utils.EdgeToEdgeUtils;

/**
 * 自定义的Dialog， 统一应用内部对话框风格<br>
 * 对话框取消按钮默认为“取消”， 点击关闭对话框
 *
 * <AUTHOR>
 * @date 2015-2015年7月15日
 */
public class CustomDialog extends Dialog {
    public View getRootView() {
        return rootView;
    }

    public void setRootView(View rootView) {
        this.rootView = rootView;
    }

    private View rootView;

    public FrameLayout getMainLayout() {
        return mainLayout;
    }

    public void setMainLayout(FrameLayout mainLayout) {
        this.mainLayout = mainLayout;
    }

    private FrameLayout mainLayout;
    private TextView titleView;
    private TextView topRightView;
    private TextView infoView;
    private TextView confirmBtn;
    private TextView cancelBtn;
    private TextView messageTv;

    public CustomDialog(Context context) {
        super(context, R.style.custom_dialog);
        initDialog(context);
    }

    public CustomDialog(Context context, int theme) {
        super(context, theme);
        initDialog(context);
    }

    public void setStyle(@StyleRes int themeResId) {
        setStyle(themeResId);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(rootView);

        // 为Dialog启用Edge-to-Edge适配
        EdgeToEdgeUtils.enableEdgeToEdgeForDialog(this);

        // 设置全屏， 靠底部展示
        LayoutParams params = getWindow().getAttributes();
        params.width = Apputils.getScreenWidth(getContext());
        getWindow().setAttributes(params);
        getWindow().setGravity(Gravity.BOTTOM);
    }

    /**
     * 初始化对话框
     *
     * @param context
     */
    private void initDialog(Context context) {
        rootView = LayoutInflater.from(context).inflate(
                R.layout.dialog_content, null);

        titleView = (TextView) rootView.findViewById(R.id.dialog_title_tv);
        topRightView = (TextView) rootView.findViewById(R.id.dialog_top_right_tv);
        infoView = (TextView) rootView.findViewById(R.id.dialog_title_info);
        messageTv = (TextView) rootView
                .findViewById(R.id.dialog_main_message_tv);
        cancelBtn = (TextView) rootView.findViewById(R.id.dialog_cancel_btn);
        confirmBtn = (TextView) rootView.findViewById(R.id.dialog_confirm_btn);
        mainLayout = (FrameLayout) rootView
                .findViewById(R.id.dialog_main_layout);

        // 设置默认的取消按钮监听
        cancelBtn.setOnClickListener(v -> dismiss());
    }

    /**
     * 设置确认按钮文字及点击监听
     *
     * @param listener
     */
    public void setPositiveButton(int textId,
                                  android.view.View.OnClickListener listener) {
        confirmBtn.setVisibility(View.VISIBLE);
        confirmBtn.setOnClickListener(listener);
        confirmBtn.setText(textId);
    }

    public void setPositiveButton(String msg, View.OnClickListener listener) {
        confirmBtn.setVisibility(View.VISIBLE);
        confirmBtn.setOnClickListener(listener);
        confirmBtn.setText(msg);
    }

    /**
     * 设置取消按钮文字， 及点击监听
     *
     * @param listener
     */
    public void setNegativeButton(int textId,
                                  android.view.View.OnClickListener listener) {
        cancelBtn.setVisibility(View.VISIBLE);
        cancelBtn.setOnClickListener(listener);
        cancelBtn.setText(textId);
    }


    public void setNegativeButtonTextColor(int color) {
        cancelBtn.setTextColor(color);
    }

    public void setNegativeButton(String msg, View.OnClickListener listener) {
        cancelBtn.setVisibility(View.VISIBLE);
        cancelBtn.setOnClickListener(listener);
        cancelBtn.setText(msg);
    }


    /**
     * 设置确认按钮的点击监听， 默认文字为确认
     *
     * @param listener
     */
    public void setPositiveButton(android.view.View.OnClickListener listener) {
        confirmBtn.setVisibility(View.VISIBLE);
        confirmBtn.setOnClickListener(listener);
    }

    /**
     * 设置取消按钮的点击监听， 默认文字为取消, 默认点击干掉对话框
     */
    public void setNegativeButton(int text) {
        cancelBtn.setVisibility(View.VISIBLE);
        cancelBtn.setText(text);
    }

    /**
     * 设置取消按钮的点击监听， 默认文字为取消
     */
    public void setNegativeButtonText(int textId) {
        cancelBtn.setVisibility(View.VISIBLE);
        cancelBtn.setText(textId);
    }

    /**
     * 对于纯文字式的对话框， 设置中间文字信息
     *
     * @param textId
     */
    public void setMessage(int textId) {
        messageTv.setText(textId);
    }

    public void setMessage(String msg) {
        messageTv.setText(msg);
    }

    @Override
    public void setTitle(int titleId) {
        titleView.setVisibility(View.VISIBLE);
        titleView.setText(titleId);
    }

    public void setTitle(String title) {
        titleView.setVisibility(View.VISIBLE);
        titleView.setText(title);
    }

    public void setInfo(String info) {
        infoView.setVisibility(View.VISIBLE);
        infoView.setText(info);
    }

    public void setInfo(CharSequence info) {
        infoView.setVisibility(View.VISIBLE);
        infoView.setText(info);
    }

    public void setTopRight(String info, View.OnClickListener clickListener) {
        topRightView.setVisibility(View.VISIBLE);
        topRightView.setText(info);
        topRightView.setOnClickListener(clickListener);
    }

    /**
     * 自定义对话框， 中心主View设置<br>
     * 对于自定义的View, 可实现 OnDialogConfirmListener 接口， 点击确认按钮时会调用
     *
     * @param
     */
    public void setMainLayoutView(final View contentView) {
        if (contentView instanceof OnConfirmListener) {

            setPositiveButton(v -> ((OnConfirmListener) contentView).onSaved());
        }

        mainLayout.removeAllViews();
        mainLayout.addView(contentView);
    }

    @Override
    public void show() {
        try {
            // 防止 WindowManager$BadTokenException
            super.show();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 设置确定按钮可否点击
     */
    public void setPositiveButtonClickable(boolean clickState) {
        confirmBtn.setVisibility(View.VISIBLE);
        confirmBtn.setClickable(clickState);
    }

    /**
     * 设置取消按钮的隐藏显示
     *
     * @param
     */
    public void setNegativeButtonVisibility(int visibility) {
        cancelBtn.setVisibility(visibility);
    }
}
