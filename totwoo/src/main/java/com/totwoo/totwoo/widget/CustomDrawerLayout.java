package com.totwoo.totwoo.widget;

import android.content.Context;
import android.util.AttributeSet;

import androidx.customview.widget.ViewDragHelper;
import androidx.drawerlayout.widget.DrawerLayout;

import com.totwoo.library.util.Apputils;

import java.lang.reflect.Field;

/**
 * 自定义的抽屉菜单布局， 解决统一定制的问题
 * 
 * <AUTHOR>
 * @date 2015-2015年7月3日
 */
public class CustomDrawerLayout extends DrawerLayout {

	public CustomDrawerLayout(Context context) {
		super(context);

		initLayout(context);
	}

	public CustomDrawerLayout(Context context, AttributeSet attrs) {
		super(context, attrs);

		initLayout(context);
	}

	public CustomDrawerLayout(Context context, AttributeSet attrs, int defStyle) {
		super(context, attrs, defStyle);

		initLayout(context);
	}

	// @Override
	// protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
	// widthMeasureSpec = MeasureSpec.makeMeasureSpec(
	// MeasureSpec.getSize(widthMeasureSpec), MeasureSpec.EXACTLY);
	// heightMeasureSpec = MeasureSpec.makeMeasureSpec(
	// MeasureSpec.getSize(heightMeasureSpec), MeasureSpec.EXACTLY);
	// super.onMeasure(widthMeasureSpec, heightMeasureSpec);
	// }

	/**
	 * 初始化基本布局
	 * 
	 * @param context
	 */
	private void initLayout(Context context) {

		// try {
		// Field minFlingField = this.getClass().getSuperclass()
		// .getDeclaredField("MIN_FLING_VELOCITY");
		// minFlingField.setAccessible(true);
		// minFlingField.setInt(this, 20);
		// } catch (Exception e) {
		// e.printStackTrace();
		// }
		//
		// setDrawerLeftEdgeSize(context, 100);
	}

	public void setDrawerLeftEdgeSize(Context context, float dp) {
		try {
			// find ViewDragHelper and set it accessible
			Field leftDraggerField = this.getClass().getSuperclass()
					.getDeclaredField("mLeftDragger");
			leftDraggerField.setAccessible(true);
			ViewDragHelper leftDragger = (ViewDragHelper) leftDraggerField
					.get(this);
			// find edgesize and set is accessible
			Field edgeSizeField = leftDragger.getClass().getDeclaredField(
					"mEdgeSize");
			edgeSizeField.setAccessible(true);
			int edgeSize = edgeSizeField.getInt(leftDragger);
			edgeSizeField.setInt(leftDragger,
					Math.max(edgeSize, Apputils.dp2px(context, dp)));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}