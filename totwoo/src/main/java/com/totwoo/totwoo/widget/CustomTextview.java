package com.totwoo.totwoo.widget;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.totwoo.totwoo.R;

public class CustomTextview extends androidx.appcompat.widget.AppCompatTextView {
    public CustomTextview(@NonNull Context context) {
        super(context);
        setSelected(false);
    }

    public CustomTextview(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        setSelected(false);
    }

    public CustomTextview(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setSelected(false);
    }

    public  void setSelected(boolean b){
        if (b) {
            setBackgroundResource(R.drawable.sel_black_10_btn_bg);
            setTextColor(getResources().getColor(R.color.white));
        } else {
            setBackgroundResource(R.drawable.change_vibration_unselect_bg);

            setTextColor(getResources().getColor(R.color.text_color_black_important));
        }

    }


    /**
     *  自定义表情
     * @param b
     */
    public  void setCustomColorSelected(boolean b){
        if (b) {
            setBackgroundResource(R.drawable.sel_black_10_btn_bg);
            setTextColor(getResources().getColor(R.color.text_color_black));
        } else {
            setBackgroundResource(R.drawable.change_vibration_unselect_bg);
            setTextColor(getResources().getColor(R.color.text_color_black));
        }

    }



}
