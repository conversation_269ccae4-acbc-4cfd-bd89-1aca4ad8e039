package com.totwoo.totwoo.widget;

import android.annotation.TargetApi;
import android.content.Context;
import android.graphics.Point;
import android.os.Build;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.Animation.AnimationListener;
import android.view.animation.TranslateAnimation;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.totwoo.library.net.HttpRequest;
import com.totwoo.library.net.RequestParams;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.UserInfoSettingActivity;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.data.SysLocalDataBean;
import com.totwoo.totwoo.fragment.OnConfirmListener;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.RequestCallBack;

public class GenderSettingView extends FrameLayout implements OnConfirmListener {
	public Context mContext;

	/** 男孩图标 */
	private ImageView maleView;
	/** 女孩图标 */
	private ImageView femaleView;

	/** 当前选择性别 */
	private int n_gender;

	/** 起始动画的左上方的点 */
	private Point centerPoint;

	private LinearLayout male_ll;

	private LinearLayout female_ll;

	public GenderSettingView(Context context) {
		this(context, null, 0);
	}

	public GenderSettingView(Context context, Point left_top) {
		this(context, null, 0);
	}

	public GenderSettingView(Context context, AttributeSet attrs) {
		this(context, attrs, 0);
	}

	@TargetApi(Build.VERSION_CODES.LOLLIPOP)
	public GenderSettingView(Context context, AttributeSet attrs,
			int defStyleAttr, int defStyleRes) {
		this(context, attrs, defStyleAttr);
	}

	public GenderSettingView(Context context, AttributeSet attrs,
			int defStyleAttr) {
		this(context, attrs, null);
	}

	public GenderSettingView(Context context, AttributeSet attrs, Point point) {
		super(context, attrs);
		mContext = context;
		this.centerPoint = point;

		LayoutInflater.from(context).inflate(R.layout.fragment_gender_setting,
				this);
		maleView = (ImageView) findViewById(R.id.setting_gender_male_img);
		femaleView = (ImageView) findViewById(R.id.setting_gender_female_img);
		male_ll = (LinearLayout) findViewById(R.id.setting_gender_male_ll);
		female_ll = (LinearLayout) findViewById(R.id.setting_gender_female_ll);

		if (point == null) {
			femaleView.setVisibility(View.VISIBLE);
			maleView.setVisibility(View.VISIBLE);
		} else {
			femaleView.setAlpha(0F);
			maleView.setAlpha(0f);
			male_ll.getChildAt(1).setVisibility(INVISIBLE);
			female_ll.getChildAt(1).setVisibility(INVISIBLE);
		}

		maleView.setOnClickListener(new OnClickListener() {
			@Override
			public void onClick(View v) {
				n_gender = 0;
				goNext(v);
			}
		});
		femaleView.setOnClickListener(new OnClickListener() {
			@Override
			public void onClick(View v) {
				n_gender = 1;
				goNext(v);
			}
		});
	}

	/**
	 * 前往下一设置
	 */
	private void goNext(View v) {
		if (mContext instanceof UserInfoSettingActivity) {
			((UserInfoSettingActivity) mContext).goNext(v);
		}
	}

	@Override
	public void onSaved() {
		ToTwooApplication.owner.setGender(n_gender);

		// 同步服务器, 当前设置的用户信息
		RequestParams params = HttpHelper.getBaseParams(true);
		params.addFormDataPart("sex", ToTwooApplication.owner.getGender() == 0 ? "0" : "1");

        HttpRequest.post(
				HttpHelper.URL_UPDATE_USER_INFO, params,
				new RequestCallBack<String>() {
                    @Override
                    public void onLogicFailure(HttpBaseBean<String> t)
					{
                        super.onLogicFailure(t);
                        PreferencesUtils.put(mContext, SysLocalDataBean.SYN_TYPE_USER_INFO, true);
						LogUtils.e("msgLogicFailure:" + t.getData());
                    }

                    @Override
					public void onFailure(int error, String msg)
                    {
						PreferencesUtils.put(mContext, SysLocalDataBean.SYN_TYPE_USER_INFO, true);
						LogUtils.e("error:" + error + ", msgFailure:" + msg);
					}

					@Override
					protected void onSuccess(HttpBaseBean<String> stringHttpBaseBean) {
						super.onSuccess(stringHttpBaseBean);
						LogUtils.e("msgSuccess:" + stringHttpBaseBean.getData());
					}
				});
	}

	@Override
	public Point getCenterPoint() {
		int[] loc = new int[2];
		int x, y;
		if (n_gender == 0) {

			maleView.getLocationOnScreen(loc);

			x = loc[0] + maleView.getMeasuredWidth() / 2;

			y = loc[1] + maleView.getMeasuredHeight() / 2;

		} else {
			femaleView.getLocationOnScreen(loc);
			x = loc[0] + femaleView.getWidth() / 2;
			y = loc[1] + femaleView.getHeight() / 2;
		}
		return new Point(x, y);
	}

    @Override
	public void loadAnim()
	{
		n_gender = ToTwooApplication.owner.getGender();

		int[] loc = new int[2];
		int x, y;
		if (n_gender == 0) {
			maleView.getLocationOnScreen(loc);
			x = loc[0] + maleView.getMeasuredWidth() / 2;
			y = loc[1] + maleView.getMeasuredHeight() / 2;

		} else {
			femaleView.getLocationOnScreen(loc);
			x = loc[0] + femaleView.getWidth() / 2;
			y = loc[1] + femaleView.getHeight() / 2;
		}
		femaleView.setAlpha(1f);
		maleView.setAlpha(1f);
		if (centerPoint != null) {
			TranslateAnimation ta = new TranslateAnimation(centerPoint.x - x,
					0, centerPoint.y - y, 0);
			ta.setDuration(1000);
			ta.setFillAfter(true);
			ta.setAnimationListener(new AnimationListener() {
				@Override
				public void onAnimationStart(Animation animation) {
					if (n_gender == 0) {
						maleView.setVisibility(View.VISIBLE);
					} else {
						femaleView.setVisibility(View.VISIBLE);
					}
				}

				@Override
				public void onAnimationRepeat(Animation animation) {

				}

				@Override
				public void onAnimationEnd(Animation animation) {
					femaleView.setVisibility(View.VISIBLE);
					maleView.setVisibility(View.VISIBLE);
					male_ll.getChildAt(1).setVisibility(VISIBLE);
					female_ll.getChildAt(1).setVisibility(VISIBLE);
				}
			});
			if (n_gender == 0) {
				male_ll.startAnimation(ta);
				female_ll.getChildAt(1).setVisibility(VISIBLE);
			} else {
				female_ll.startAnimation(ta);
				male_ll.getChildAt(1).setVisibility(VISIBLE);
			}
		}
	}
}
