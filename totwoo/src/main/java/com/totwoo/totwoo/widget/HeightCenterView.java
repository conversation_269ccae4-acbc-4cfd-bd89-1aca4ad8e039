package com.totwoo.totwoo.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.FrameLayout;

import com.totwoo.library.net.HttpRequest;
import com.totwoo.library.net.RequestParams;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.data.SysLocalDataBean;
import com.totwoo.totwoo.fragment.OnInfoConfirmListener;
import com.totwoo.totwoo.utils.ConfigData;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.RequestCallBack;
import com.totwoo.totwoo.widget.WheelView.OnWheelViewListener;

public class HeightCenterView extends FrameLayout implements OnInfoConfirmListener {
    private Context mContext;

    /**
     * 最小身高
     */
    private final int MIN_HEIGHT = 150;

    /**
     * 身高标尺
     */
    private WheelView wheelView;

    /**
     * 当前表盘指示身高
     */
    private int n_height;

    public int getN_height() {
        return n_height;
    }

    public void setN_height(int n_height) {
        this.n_height = n_height;
    }

    /**
     * 起始动画的左上方的点
     */

    public HeightCenterView(Context context) {
        this(context, null, 0);
    }


    public HeightCenterView(Context context, AttributeSet attrs,
                            int defStyleAttr, int defStyleRes) {
        this(context, attrs, defStyleAttr);
    }

    public HeightCenterView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    /**
     * 包含起始动画的构造方法， 如果需要有过度动画， 必须通过该构造方法构建
     *
     * @param context
     * @param attrs   可以为null
     */
    public HeightCenterView(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;

        LayoutInflater.from(context).inflate(R.layout.height_setting,
                this);
        wheelView = (WheelView) findViewById(R.id.setting_height_wheelview);

        wheelView.setItems(ConfigData.SETTING_HEIGHT, 5, "");
        wheelView.setOnWheelViewListener(viewListener);
        if (ToTwooApplication.owner.getHeight() < MIN_HEIGHT) {
            wheelView.setSeletion(165 - MIN_HEIGHT);
        } else {
            wheelView.setSeletion(ToTwooApplication.owner.getHeight()
                    - MIN_HEIGHT);
        }

    }

    OnWheelViewListener viewListener = new OnWheelViewListener() {
        public void onSelected(WheelView wheelView, int selectedIndex,
                               String item) {
            n_height = wheelView.getSeletedIndex() + MIN_HEIGHT;
        }
    };

    @Override
    public void onSaved() {
        ToTwooApplication.owner.setHeight(n_height);

        // 同步服务器, 当前设置的用户信息
        RequestParams params = HttpHelper.getBaseParams(true);
        params.addFormDataPart("height", ToTwooApplication.owner.getHeight() + "");

        HttpRequest.post(
                HttpHelper.URL_UPDATE_USER_INFO, params,
                new RequestCallBack<String>() {

                    @Override
                    public void onLogicFailure(HttpBaseBean<String> t) {
                        super.onLogicFailure(t);
                        PreferencesUtils.put(mContext,
                                SysLocalDataBean.SYN_TYPE_USER_INFO, true);
                    }

                    @Override
                    public void onFailure(int error, String msg) {
                        PreferencesUtils.put(mContext,
                                SysLocalDataBean.SYN_TYPE_USER_INFO, true);
                    }
                });
    }
}
