package com.totwoo.totwoo.widget;

import android.annotation.TargetApi;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Paint.FontMetrics;
import android.graphics.Point;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.os.Build;
import android.util.AttributeSet;
import android.view.View;

import androidx.core.content.res.ResourcesCompat;

import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;

/**
 * 身高设置中可随刻度伸张的小人
 * 
 * <AUTHOR>
 * @date 2015-2015年7月23日
 */
public class HeightPersonView extends View {
	private Context mContext;

	private static float DEFAULT_BODY_HEIGHT = 0;

	private float person_body_height;

	/** 用户实际身高， 默认为165cm */
	private int height_value;

	private Paint paint;

	Point mPoint;

	private Typeface gothiciPlain;

	private Bitmap maleHeadBm;

	private Bitmap femaleHeadBm;

	private Bitmap maleBodyBm;

	private Bitmap femaleBoydBm;

	private boolean amiComplete;

	public boolean isAmiComplete() {
		return amiComplete;
	}

	public void setAmiComplete(boolean amiComplete) {
		this.amiComplete = amiComplete;
		invalidate();
	}

	@TargetApi(Build.VERSION_CODES.LOLLIPOP)
	public HeightPersonView(Context context, AttributeSet attrs,
			int defStyleAttr, int defStyleRes) {
		super(context, attrs, defStyleAttr, defStyleRes);
		initView(context);
	}

	public HeightPersonView(Context context, AttributeSet attrs,
			int defStyleAttr) {
		super(context, attrs, defStyleAttr);
		initView(context);
	}

	public HeightPersonView(Context context, AttributeSet attrs) {
		super(context, attrs);
		initView(context);
	}

	public HeightPersonView(Context context) {
		super(context);
		initView(context);
	}

	/**
	 * 初始化View 相关的工具
	 * 
	 * @param context
	 */
	private void initView(Context context) {
        mContext = context;
        mPoint = new Point();

        paint = new Paint();
        gothiciPlain = ResourcesCompat.getFont(mContext,
                R.font.gothici);
        paint.setDither(true);
        paint.setAntiAlias(true);
        paint.setStrokeWidth(2);
        paint.setColor(Color.parseColor("#20000000"));

    }

	@Override
	protected void onDraw(Canvas canvas) {
		super.onDraw(canvas);

		float center_x = canvas.getWidth() * 2 / 3f;
		float max_height = 0;
		Rect rect = new Rect();

		// 先绘制头部
		Bitmap bm = null;

		if (DEFAULT_BODY_HEIGHT == 0) {
			DEFAULT_BODY_HEIGHT = Apputils.dp2px(mContext, 65);
		}

		// 计算当前身高值的绘制身体高度
		person_body_height = DEFAULT_BODY_HEIGHT / 165f * height_value * 1.2f;
		int offset = Apputils.dp2px(mContext, 15);

		// mPoint.x = rect.left;
		// mPoint.y = rect.top;
		// 下面的线

		// 绘制身体

		if (ToTwooApplication.owner.getGender() == 0) {
			if (maleBodyBm == null) {
				maleBodyBm = BitmapFactory.decodeResource(
						mContext.getResources(), R.drawable.person_male_body);
			}
			bm = maleBodyBm;
		} else {
			if (femaleBoydBm == null) {
				femaleBoydBm = BitmapFactory.decodeResource(
						mContext.getResources(), R.drawable.person_female_body);
			}
			bm = femaleBoydBm;
		}

		rect.left = (int) (center_x - (person_body_height * bm.getWidth() / bm
				.getHeight()) / 2);
		rect.top = (int) (canvas.getHeight() - person_body_height) - offset
				- Apputils.dp2px(mContext, 1.5f);
		rect.right = (int) (center_x + (person_body_height * bm.getWidth() / bm
				.getHeight()) / 2);
		rect.bottom = canvas.getHeight() - offset;
		canvas.drawBitmap(bm, null, rect, null);

		if (ToTwooApplication.owner.getGender() == 0) {
			if (maleHeadBm == null) {
				maleHeadBm = BitmapFactory.decodeResource(
						mContext.getResources(), R.drawable.person_male_head);
			}
			bm = maleHeadBm;
		} else {
			if (femaleHeadBm == null) {
				femaleHeadBm = BitmapFactory.decodeResource(
						mContext.getResources(), R.drawable.person_female_head);
			}
			bm = femaleHeadBm;
		}
		rect.left = (int) (center_x - bm.getWidth() / 2f);
		rect.top = (int) (canvas.getHeight() - person_body_height - bm
				.getHeight()) + 5;

		rect.top = rect.top - offset;
		rect.right = (int) (center_x + bm.getWidth() / 2f);
		rect.bottom = (int) (canvas.getHeight() - person_body_height) + 5;
		rect.bottom = rect.bottom - offset;
		canvas.drawBitmap(bm, null, rect, null);
		max_height = rect.top;

		String text = height_value + " cm";

		paint.setTypeface(gothiciPlain);
		paint.setTextSize(mContext.getResources().getDimension(
				R.dimen.setting_height_value_text_size));

		FontMetrics fm = paint.getFontMetrics();

		if (amiComplete) {
			paint.setColor(Color.parseColor("#30000000"));
			canvas.drawText(text, 0, max_height + fm.descent, paint);
			paint.setColor(Color.parseColor("#20000000"));
			canvas.drawLine(paint.measureText(text) + 10, max_height,
					canvas.getWidth(), max_height, paint);
			canvas.drawLine(0, canvas.getHeight() - offset, canvas.getWidth(),
					canvas.getHeight() - offset, paint);
		}
		mPoint.x = (int) center_x;
		mPoint.y = canvas.getHeight() / 2;

		if (onDrawCompleteListener != null) {
			onDrawCompleteListener.OnDrawComplete();
		}
	}

	public int getHeight_value() {
		return height_value;
	}

	/** 设置用户身高值， 重绘View */
	public void setHeight_value(int height_value) {
		this.height_value = height_value;
		invalidate();
	}

	public Point getLeftTopPoint() {
		return mPoint;
	}

	public Point getPersonCenter() {

		return null;
	}

	private OnDrawCompleteListener onDrawCompleteListener;

	public OnDrawCompleteListener getOnDrawCompleteListener() {
		return onDrawCompleteListener;
	}

	public void setOnDrawCompleteListener(
			OnDrawCompleteListener onDrawCompleteListener) {
		this.onDrawCompleteListener = onDrawCompleteListener;
	}

	public interface OnDrawCompleteListener {
		void OnDrawComplete();
	}
}
