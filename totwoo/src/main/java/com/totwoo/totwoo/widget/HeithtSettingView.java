package com.totwoo.totwoo.widget;

import android.content.Context;
import android.graphics.Point;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.Animation.AnimationListener;
import android.view.animation.TranslateAnimation;
import android.widget.FrameLayout;

import com.totwoo.library.net.HttpRequest;
import com.totwoo.library.net.RequestParams;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.data.SysLocalDataBean;
import com.totwoo.totwoo.fragment.OnConfirmListener;
import com.totwoo.totwoo.utils.ConfigData;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.RequestCallBack;
import com.totwoo.totwoo.widget.HeightPersonView.OnDrawCompleteListener;
import com.totwoo.totwoo.widget.WheelView.OnWheelViewListener;

public class HeithtSettingView extends FrameLayout implements OnConfirmListener {
    private Context mContext;

    /**
     * 最小身高
     */
    private final int MIN_HEIGHT = 150;

    /**
     * 身高标尺
     */
    private WheelView wheelView;

    /**
     * 小人View
     */
    private HeightPersonView personView;

    /**
     * 当前表盘指示身高
     */
    private int n_height;

    public int getN_height() {
        return n_height;
    }

    public void setN_height(int n_height) {
        this.n_height = n_height;
    }

    /**
     * 起始动画的左上方的点
     */
    private Point centerPoint;

    public HeithtSettingView(Context context) {
        this(context, null, 0);
    }

    public HeithtSettingView(Context context, Point left_topPoint) {
        this(context, null, 0);
    }

    public HeithtSettingView(Context context, AttributeSet attrs,
                             int defStyleAttr, int defStyleRes) {
        this(context, attrs, defStyleAttr);
    }

    public HeithtSettingView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public HeithtSettingView(Context context, AttributeSet attrs,
                             int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    /**
     * 包含起始动画的构造方法， 如果需要有过度动画， 必须通过该构造方法构建
     *
     * @param context
     * @param attrs         可以为null
     * @param left_topPoint 动画起始点
     */
    public HeithtSettingView(Context context, AttributeSet attrs,
                             Point left_topPoint) {
        super(context, attrs);
        mContext = context;
        this.centerPoint = left_topPoint;

        LayoutInflater.from(context).inflate(R.layout.fragment_height_setting,
                this);
        wheelView = (WheelView) findViewById(R.id.setting_height_wheelview);
        personView = (HeightPersonView) findViewById(R.id.setting_height_person_layout);

        if (left_topPoint == null) {
            wheelView.setVisibility(View.VISIBLE);
            personView.setVisibility(View.VISIBLE);
        } else {
            wheelView.setVisibility(View.INVISIBLE);
            personView.setAlpha(0);
        }

        // NumericWheelAdapter numericWheelAdapter1 = new NumericWheelAdapter(
        // context, MIN_HEIGHT, 200);
        // numericWheelAdapter1.setLabel(" cm");

        // wheelView.setViewAdapter(numericWheelAdapter1);
        // wheelView.setCyclic(false);// 是否可循环滑动
        // wheelView.addChangingListener(changeListener);
        // wheelView.setVisibleItems(5);
        // wheelView.setDrawShadows(false);
        wheelView.setItems(ConfigData.SETTING_HEIGHT, 5, "");
        wheelView.setOnWheelViewListener(viewListener);
        if (ToTwooApplication.owner.getHeight() < MIN_HEIGHT) {
            wheelView.setSeletion(165 - MIN_HEIGHT);
        } else {
            wheelView.setSeletion(ToTwooApplication.owner.getHeight()
                    - MIN_HEIGHT);
        }

    }

    // OnWheelChangedListener changeListener = new OnWheelChangedListener() {
    // @Override
    // public void onChanged(WheelView wheel, int oldValue, int newValue) {
    // n_height = wheel.getCurrentItem() + MIN_HEIGHT;
    // personView.setHeight_value(n_height);
    // }
    // };
    WheelView.OnWheelViewListener viewListener = new OnWheelViewListener() {
        public void onSelected(WheelView wheelView, int selectedIndex,
                               String item) {
            n_height = wheelView.getSeletedIndex() + MIN_HEIGHT;
            personView.setHeight_value(n_height);
        }
    };

    private int animX;

    private int animY;

    @Override
    public void onSaved() {
        ToTwooApplication.owner.setHeight(n_height);

        // 同步服务器, 当前设置的用户信息
        RequestParams params = HttpHelper.getBaseParams(true);
        params.addFormDataPart("height", ToTwooApplication.owner.getHeight()
                + "");

        HttpRequest.post(
                HttpHelper.URL_UPDATE_USER_INFO, params,
                new RequestCallBack<String>() {

                    @Override
                    public void onLogicFailure(HttpBaseBean<String> t) {
                        super.onLogicFailure(t);
                        PreferencesUtils.put(mContext,
                                SysLocalDataBean.SYN_TYPE_USER_INFO, true);
                    }


                    @Override
                    public void onFailure(int error, String msg) {
                        PreferencesUtils.put(mContext,
                                SysLocalDataBean.SYN_TYPE_USER_INFO, true);
                    }
                });
    }

    @Override
    public Point getCenterPoint() {
        int[] loc = new int[2];
        personView.getLocationOnScreen(loc);
        int x = loc[0] + personView.getLeftTopPoint().x;
        int y = loc[1] + personView.getLeftTopPoint().y;

        return new Point(x, y);
    }

    @Override
    public void loadAnim() {

        int[] loc = new int[2];
        personView.getLocationOnScreen(loc);
        animX = loc[0] + personView.getLeftTopPoint().x;
        animY = loc[1] + personView.getLeftTopPoint().y;
        personView.setAlpha(1);
        if (centerPoint != null) {
            TranslateAnimation ta = new TranslateAnimation(centerPoint.x
                    - animX, 0, centerPoint.y - animY, 0);
            ta.setDuration(1000);
            ta.setFillAfter(true);
            ta.setAnimationListener(new AnimationListener() {
                @Override
                public void onAnimationStart(Animation animation) {

                }

                @Override
                public void onAnimationRepeat(Animation animation) {

                }

                @Override
                public void onAnimationEnd(Animation animation) {
                    wheelView.setVisibility(View.VISIBLE);
                    personView.setAmiComplete(true);
                }
            });
            personView.startAnimation(ta);
        }
        personView.setOnDrawCompleteListener(new OnDrawCompleteListener() {

            @Override
            public void OnDrawComplete() {

            }
        });
    }

    public HeightPersonView getPersonView() {
        return personView;
    }

}
