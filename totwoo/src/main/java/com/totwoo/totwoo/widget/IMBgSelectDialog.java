package com.totwoo.totwoo.widget;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestOptions;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.ImBgBean;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.PreferencesUtils;

import java.util.ArrayList;

/**
 * Created by totwoo on 2018/8/22.
 */

public class IMBgSelectDialog {
    private CustomDialog dialog;
    private Context context;
    private RecyclerView im_bg_rv;
    private ArrayList<ImBgBean> beans;
    private ImbgClickListener imbgClickListener;
    private ImBgSelectAdapter imBgSelectAdapter;

    public IMBgSelectDialog(Context context) {
        this.context = context;
        beans = new ArrayList<>();
        beans.add(new ImBgBean(true, 0));
        for (int resourceId : CommonArgs.IM_BG_RESOURCE) {
            beans.add(new ImBgBean(false,resourceId));
        }
//        beans.add(new ImBgBean(false, R.drawable.im_bg));
//        beans.add(new ImBgBean(false, R.drawable.im_bg_1));
//        beans.add(new ImBgBean(false, R.drawable.im_bg_2));
//        beans.add(new ImBgBean(false, R.drawable.im_bg_3));
//        beans.add(new ImBgBean(false, R.drawable.im_bg_4));
//        beans.add(new ImBgBean(false, R.drawable.im_bg_5));
        setSelect(PreferencesUtils.getInt(context,CommonArgs.IM_BG_SELECT_INDEX,1));
    }

    private void setSelect(int positon) {
        if (positon < 0 || positon >= beans.size()) {
            return;
        }
        for (int i = 0; i < beans.size(); i++) {
            ImBgBean imBgBean = beans.get(i);
            if (i == positon) {
                imBgBean.setSelect(true);
            }else{
                imBgBean.setSelect(false);
            }
            beans.set(i,imBgBean);
        }
    }

    public void setBgItemClickListener(ImbgClickListener imbgClickListener) {
        this.imbgClickListener = imbgClickListener;
    }

    public void showBgSelectDialog() {
        if (dialog == null) {
            dialog = new CustomDialog(context, R.style.custom_dialog_tran);
            View v = View.inflate(context, R.layout.im_bg_select_dialog, null);
            dialog.setRootView(v);

            im_bg_rv = v.findViewById(R.id.im_bg_rv);

            im_bg_rv.setLayoutManager(new LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false));
            imBgSelectAdapter = new ImBgSelectAdapter();

            im_bg_rv.setAdapter(imBgSelectAdapter);
        }
        dialog.show();
    }

    public void notifyAdapter(){
        setSelect(0);
        imBgSelectAdapter.notifyDataSetChanged();
    }


    private class ImBgSelectAdapter extends RecyclerView.Adapter<ImBgSelectAdapter.ViewHolder> {

        @Override
        public ImBgSelectAdapter.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.im_bg_item, parent, false);
            ImBgSelectAdapter.ViewHolder viewHolder = new ImBgSelectAdapter.ViewHolder(view);
            return viewHolder;
        }

        @Override
        public void onBindViewHolder(ImBgSelectAdapter.ViewHolder holder, final int position) {

            if (beans.get(position).isAdd()) {
                holder.mImageCl.setVisibility(View.GONE);
                holder.mAddCl.setVisibility(View.VISIBLE);
                holder.mAddCl.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        imbgClickListener.onAddClick();
                    }
                });
            } else {
                holder.mImageCl.setVisibility(View.VISIBLE);
                holder.mAddCl.setVisibility(View.GONE);

                Glide.with(context).load(beans.get(position).getImageResourceId()).apply(new RequestOptions().transform(new RoundedCorners(CommonUtils.dip2px(context, 4)))).into(holder.mbgIv);

                holder.mbgIv.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        imbgClickListener.onItemClick(beans.get(position).getImageResourceId());
                        setSelect(position);
                        notifyDataSetChanged();
                        PreferencesUtils.put(context,CommonArgs.IM_BG_SELECT_INDEX,position);
                    }
                });
                if(beans.get(position).isSelect()){
                    holder.mbgSelectIv.setVisibility(View.VISIBLE);
                }else{
                    holder.mbgSelectIv.setVisibility(View.GONE);
                }
            }

            if (position == beans.size() - 1) {
                holder.mView.setVisibility(View.VISIBLE);
            } else {
                holder.mView.setVisibility(View.GONE);
            }
        }

        @Override
        public int getItemCount() {
            return beans == null ? 0 : beans.size();
        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            ImageView mbgIv;
            ImageView mbgSelectIv;
            View mView;
            ConstraintLayout mAddCl;
            ConstraintLayout mImageCl;

            public ViewHolder(View itemView) {
                super(itemView);
                mbgIv = (ImageView) itemView.findViewById(R.id.im_bg_iv);
                mbgSelectIv = (ImageView) itemView.findViewById(R.id.im_bg_iv_select);
                mView = itemView.findViewById(R.id.im_bg_view);
                mAddCl = itemView.findViewById(R.id.im_bg_add_cl);
                mImageCl = itemView.findViewById(R.id.im_bg_image_cl);
            }
        }
    }

    public interface ImbgClickListener {
        void onItemClick(int resourceId);

        void onAddClick();
    }
}
