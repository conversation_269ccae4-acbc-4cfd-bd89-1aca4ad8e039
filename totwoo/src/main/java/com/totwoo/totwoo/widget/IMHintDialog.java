package com.totwoo.totwoo.widget;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Typeface;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.totwoo.totwoo.R;


public class IMHintDialog extends Dialog {

    Context context;
    View.OnClickListener onClickListener;
    TextView tvTitle;
    String title;
    String par1;
    String par2;
    String par3;
    String par4;

    public IMHintDialog(Context context, View.OnClickListener onClickListener) {
        super(context, R.style.MyDialog);
        this.context = context;
        this.onClickListener = onClickListener;
    }

    public IMHintDialog(Context context, View.OnClickListener onClickListener,String title,String par1,String par2,String par3) {
        super(context, R.style.MyDialog);
        this.context = context;
        this.onClickListener = onClickListener;
        this.title = title;
        this.par1 = par1;
        this.par2 = par2;
        this.par3 = par3;
    }

    public IMHintDialog(Context context, View.OnClickListener onClickListener,String title,String par1,String par2,String par3,String par4) {
        super(context, R.style.MyDialog);
        this.context = context;
        this.onClickListener = onClickListener;
        this.title = title;
        this.par1 = par1;
        this.par2 = par2;
        this.par3 = par3;
        this.par4 = par4;
    }

    public IMHintDialog(Context context, View.OnClickListener onClickListener,String title,String par1,String par2) {
        super(context, R.style.MyDialog);
        this.context = context;
        this.onClickListener = onClickListener;
        this.title = title;
        this.par1 = par1;
        this.par2 = par2;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.setContentView(R.layout.im_hint_dialog);
        tvTitle = (TextView) findViewById(R.id.setting_dialog_title);
        tvTitle .setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));

        if(!TextUtils.isEmpty(title)){
            tvTitle.setText(title);
        }
        if(!TextUtils.isEmpty(par1)){
            ((TextView) findViewById(R.id.text_dialog_par1)).setText(par1);
        }
        if(!TextUtils.isEmpty(par2)){
            ((TextView) findViewById(R.id.text_dialog_par2)).setText(par2);
        }else{
            ((TextView) findViewById(R.id.text_dialog_par2)).setVisibility(View.GONE);
        }
        if(!TextUtils.isEmpty(par3)){
            ((TextView) findViewById(R.id.text_dialog_par3)).setText(par3);
        }else{
            ((TextView) findViewById(R.id.text_dialog_par3)).setVisibility(View.GONE);
        }

        if(!TextUtils.isEmpty(par4)){
            ((TextView) findViewById(R.id.text_dialog_par4)).setText(par4);
            ((TextView) findViewById(R.id.text_dialog_par4)).setVisibility(View.VISIBLE);
        }else{
            ((TextView) findViewById(R.id.text_dialog_par4)).setVisibility(View.GONE);
        }

        findViewById(R.id.btnConfirm).setOnClickListener(onClickListener);
    }
}