package com.totwoo.totwoo.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.RectF;
import android.graphics.Shader;
import android.util.AttributeSet;
import android.view.View;

import com.totwoo.library.util.Apputils;

/**
 *
 * Created by lixingmao on 2017/1/4.
 */

public class JewSearchingView extends View {
    private final int[] GENT_COLORS = new int[]{Color.parseColor("#0d36d1fe"), Color.parseColor("#0d7e36f0"),Color.parseColor("#0df036e9")};
    private Paint mPaint;
    private RectF largeRect;
    private LinearGradient linearGradient;
    private float longSide;
    private float shortSide;

    private int center;
    private final float PRE_SPEED = 0.1f; // 每帧旋转角度, 速度
    private final float PARALLAX_SPEED = 0.2f; // 不同圆环旋转角度差值
    private Matrix souceMat, perCountMat, perMat;
    private int count;
    private RectF centerOval;

    public JewSearchingView(Context context) {
        super(context);
        init(context);
    }

    public JewSearchingView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public JewSearchingView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

//    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
//    public JewSearchingView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
//        super(context, attrs, defStyleAttr, defStyleRes);
//        init(context);
//    }

    private void init(Context context) {
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setDither(true);
        mPaint.setStyle(Paint.Style.FILL_AND_STROKE);
        mPaint.setStrokeWidth(20);

        longSide = Apputils.dp2px(context, 123);
        shortSide = longSide * 2 / 3;

        center = Apputils.dp2px(context, 150);

        largeRect = new RectF(center - longSide, center - shortSide, center + longSide, center + shortSide);

        linearGradient = new LinearGradient(center - longSide, center, center + longSide, center, GENT_COLORS, new float[]{0, 0.5f, 1},  Shader.TileMode.CLAMP);
        mPaint.setShader(linearGradient);

        souceMat = new Matrix();
        perCountMat = new Matrix();
        perMat = new Matrix();
        linearGradient.getLocalMatrix(souceMat);

        centerOval = new RectF(center - shortSide * 0.9f, center - shortSide * 0.9f, center + shortSide * 0.9f, center + shortSide * 0.9f);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        perCountMat.set(souceMat);

        canvas.rotate(count * PRE_SPEED, center, center);

        perCountMat.postRotate(-count * PRE_SPEED, center, center);

        mPaint.setShader(linearGradient);
        mPaint.setColor(Color.WHITE);

        drawOval(canvas, false);

        canvas.scale(0.6f, 0.6f, center, center);

        drawOval(canvas, true);

        mPaint.setShader(null);
        mPaint.setColor(Color.parseColor("#33ffffff"));
        canvas.drawOval(centerOval, mPaint);

        count ++;

        invalidate();
    }

    private void drawOval(Canvas canvas, boolean little) {
        int pre = 180 / 4;
        for (int i = 0; i < 4; i++) {
            canvas.save();
            float angle = count * (little ? PRE_SPEED + PARALLAX_SPEED :PRE_SPEED) + (pre * i);

            canvas.rotate(angle, center, center);

            perMat.set(perCountMat);
            perMat.postRotate(- angle, center, center);
            perMat.postScale(getScale(angle), 1, center, center);

            linearGradient.setLocalMatrix(perMat);

            canvas.drawOval(largeRect, mPaint);
            canvas.restore();


//            if (little && i % 2 == 1){
//                canvas.save();
//
//                angle = count * (PRE_SPEED + PARALLAX_SPEED) + pre * i;
//                canvas.rotate(angle, center, center);
//                perMat.set(perCountMat);
//                perMat.postRotate(- angle, center, center);
//                perMat.postScale(getScale(angle), 1, center, center);
//                linearGradient.setLocalMatrix(perMat);
//
//                canvas.drawOval(largeRect, mPaint);
//
//                canvas.restore();
//            }

        }
    }


    private float getScale(float v) {
        double side1 = Math.abs(Math.cos(Math.atan(longSide / shortSide) + v)) * Math.sqrt(longSide * longSide + shortSide * shortSide);
        double side2 = Math.abs(Math.sin(Math.atan(longSide / shortSide) + v)) * Math.sqrt(longSide * longSide + shortSide * shortSide);

        return (float) (Math.max(side1, side2)/longSide);
    }
}
