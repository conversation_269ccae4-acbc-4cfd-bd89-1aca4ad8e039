package com.totwoo.totwoo.widget;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.utils.EdgeToEdgeUtils;

/**
 * Created by xinyoulingxi on 2017/8/10.
 */

public class LoadingDialog extends Dialog {
    private View rootView;
    private TextView tv;

    public LoadingDialog(Context context, String msg) {
        super(context, R.style.custom_dialog2);
        initDialog(context, msg);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(rootView);

        // 为Dialog启用Edge-to-Edge适配
        EdgeToEdgeUtils.enableEdgeToEdgeForDialog(this);
    }

    private void initDialog(Context context, String msg) {
        rootView = LayoutInflater.from(context).inflate(R.layout.dialog_loading, null);
        tv = rootView.findViewById(R.id.dialog_loading_content);
        if (!TextUtils.isEmpty(msg)) {
            tv.setText(msg);
        }
        this.setCancelable(false);
        this.setCanceledOnTouchOutside(false);
    }
}
