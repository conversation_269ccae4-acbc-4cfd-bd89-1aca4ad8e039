package com.totwoo.totwoo.widget;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;


public class LoveSpaceDialog extends Dialog {
    @BindView(R.id.common_share_dialog_title)
    TextView tvTitle;
    private View rootView;
    @BindView(R.id.common_share_rv)
    RecyclerView rvIcons;
    @BindView(R.id.love_space_share_cancel)
    ImageView mCancel;

    private List<CommonShareType> types;
    private View.OnClickListener itemClickListener;
    private Context mContext;

    public LoveSpaceDialog(Context context, List<CommonShareType> types, View.OnClickListener itemClickListener) {
        super(context, R.style.custom_dialog_tran);
        initDialog(context);
        mContext = context;
        this.types = types;
        this.itemClickListener = itemClickListener;
        rvIcons.setLayoutManager(new LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false));
        CommonShareIconAdapter commonShareIconAdapter = new CommonShareIconAdapter();
        rvIcons.setAdapter(commonShareIconAdapter);
    }

    /**
     * 初始化对话框
     *
     * @param context
     */
    private void initDialog(Context context) {
        rootView = LayoutInflater.from(context).inflate(
                R.layout.love_space_share_dialog, null);
        ButterKnife.bind(this,rootView);
        mCancel.setOnClickListener(v -> dismiss());
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(rootView);

        // 设置全屏， 靠底部展示
        WindowManager.LayoutParams params = getWindow().getAttributes();
        params.width = Apputils.getScreenWidth(getContext());
        params.height = Apputils.getScreenHeight(getContext());
        getWindow().setAttributes(params);
        getWindow().setGravity(Gravity.BOTTOM);
    }

    public void setCustomTitle(CharSequence title) {
        tvTitle.setVisibility(View.VISIBLE);
        tvTitle.setText(title);
    }

    private class CommonShareIconAdapter extends RecyclerView.Adapter<CommonShareIconAdapter.ViewHolder> {

        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.love_space_share_icon_item, parent, false);
            return new ViewHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            holder.mIconIv.setOnClickListener(itemClickListener);
            switch (types.get(position)) {
                case QQ:
                    holder.mIconIv.setImageResource(R.drawable.share_common_qq);
                    holder.mIconIv.setTag(CommonShareType.QQ);
                    break;
                case QZONE:
                    holder.mIconIv.setImageResource(R.drawable.share_common_qzone);
                    holder.mIconIv.setTag(CommonShareType.QZONE);
                    break;
                case WECHAT:
                    holder.mIconIv.setImageResource(R.drawable.share_common_wechat);
                    holder.mIconIv.setTag(CommonShareType.WECHAT);
                    break;
                case FRIENDS:
                    holder.mIconIv.setImageResource(R.drawable.share_common_friends);
                    holder.mIconIv.setTag(CommonShareType.FRIENDS);
                    break;
                case WEIBO:
                    holder.mIconIv.setImageResource(R.drawable.share_common_weibo);
                    holder.mIconIv.setTag(CommonShareType.WEIBO);
                    break;
                case MESSAGE:
                    holder.mIconIv.setImageResource(R.drawable.share_common_msg);
                    holder.mIconIv.setTag(CommonShareType.MESSAGE);
                    break;
                case TWITTER:
                    holder.mIconIv.setImageResource(R.drawable.share_common_twitter);
                    holder.mIconIv.setTag(CommonShareType.TWITTER);
                    break;
                case FACEBOOK:
                    holder.mIconIv.setImageResource(R.drawable.share_common_fb);
                    holder.mIconIv.setTag(CommonShareType.FACEBOOK);
                    break;
            }

        }

        @Override
        public int getItemCount() {
            return types.size();
        }

        public class ViewHolder extends RecyclerView.ViewHolder {
            ImageView mIconIv;

            public ViewHolder(View itemView) {
                super(itemView);
                mIconIv = (ImageView) itemView.findViewById(R.id.common_share_icon_iv);
            }
        }
    }
}