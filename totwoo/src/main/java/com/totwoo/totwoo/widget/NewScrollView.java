package com.totwoo.totwoo.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.ScrollView;

/**
 * Created by xinyoulingxi on 2017/5/11.
 */

public class NewScrollView extends ScrollView
{
    public static interface ScrollViewListener
    {
        public void onScrollViewChanged(NewScrollView sc, int x, int y, int oldx, int oldy);
    }

    public ScrollViewListener scrollViewListener;

    public NewScrollView(Context context)
    {
        super(context);
    }

    public NewScrollView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    public NewScrollView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public void setScrollViewListener(ScrollViewListener scrollViewListener) {
        this.scrollViewListener = scrollViewListener;
    }

    @Override
    protected void onScrollChanged(int x, int y, int oldx, int oldy) {
        super.onScrollChanged(x, y, oldx, oldy);
        if (scrollViewListener != null) {
            scrollViewListener.onScrollViewChanged(this, x, y, oldx, oldy);
        }
    }
}
