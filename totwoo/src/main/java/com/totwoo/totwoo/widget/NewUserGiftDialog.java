package com.totwoo.totwoo.widget;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Typeface;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.totwoo.totwoo.R;

public class NewUserGiftDialog extends Dialog {

    Context context;
    View.OnClickListener onClickListener;
    View.OnClickListener onCancelListener;
    CharSequence charSequenceTitle;
    String title;
    String buttonText;

    public NewUserGiftDialog(Context context, View.OnClickListener onClickListener, View.OnClickListener onCancelListener) {
        super(context, R.style.MyDialog);
        this.context = context;
        this.onClickListener = onClickListener;
        this.onCancelListener = onCancelListener;
    }
    public NewUserGiftDialog(Context context, View.OnClickListener onClickListener, View.OnClickListener onCancelListener,String title,String buttonText) {
        super(context, R.style.MyDialog);
        this.context = context;
        this.onClickListener = onClickListener;
        this.onCancelListener = onCancelListener;
        this.title = title;
        this.buttonText = buttonText;
    }
    public NewUserGiftDialog(Context context, View.OnClickListener onClickListener, View.OnClickListener onCancelListener,CharSequence charSequenceTitle,String buttonText) {
        super(context, R.style.MyDialog);
        this.context = context;
        this.onClickListener = onClickListener;
        this.onCancelListener = onCancelListener;
        this.charSequenceTitle = charSequenceTitle;
        this.buttonText = buttonText;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.setContentView(R.layout.new_user_gift_dialog);
        TextView tvTitle = (TextView) findViewById(R.id.setting_dialog_title);
        tvTitle .setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        if(!TextUtils.isEmpty(title)){
            tvTitle.setText(title);
        }
        if(charSequenceTitle != null){
            tvTitle.setText(charSequenceTitle);
        }
        if(!TextUtils.isEmpty(buttonText)){
            ((TextView)findViewById(R.id.btnConfirm)).setText(buttonText);
        }
        findViewById(R.id.btnConfirm).setOnClickListener(onClickListener);
        findViewById(R.id.btnCancel).setOnClickListener(onCancelListener);
    }
}