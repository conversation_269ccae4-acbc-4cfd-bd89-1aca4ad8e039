package com.totwoo.totwoo.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Paint.Style;
import android.graphics.Rect;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.View;

import androidx.core.content.res.ResourcesCompat;

import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;

/**
 * OTA 升级时提供ota 进度的进度View
 * 
 * <AUTHOR>
 * @date 2015年11月15日
 */
@SuppressLint("NewApi")
public class OTAProgressView extends View {

	private Context mContext;
	/** 当前进度 */
	private int currProgerss;

	/** 当前的界面状态, 0 表示正常升级状态, -1, 表示警告失败状态 */
	private int layoutState;

	private Paint paint;

	public OTAProgressView(Context context) {
		super(context);
		initView(context);
	}

	public OTAProgressView(Context context, AttributeSet attrs) {
		super(context, attrs);
		initView(context);
	}

	public OTAProgressView(Context context, AttributeSet attrs, int defStyleAttr) {
		super(context, attrs, defStyleAttr);
		initView(context);
	}

	public OTAProgressView(Context context, AttributeSet attrs,
			int defStyleAttr, int defStyleRes) {
		super(context, attrs, defStyleAttr, defStyleRes);
		initView(context);
	}

	/**
	 * 初始化界面元素
	 * 
	 * @param context
	 */
	private void initView(Context context) {
		mContext = context;
		setBackgroundResource(R.drawable.jew_con_btn_bg_white);

		paint = new Paint();
		paint.setAntiAlias(true);
		paint.setDither(true);
		paint.setStyle(Style.STROKE);
		paint.setStrokeWidth(Apputils.dp2px(mContext, 1));
		paint.setColor(Color.WHITE);
	}

	@Override
	protected void onDraw(Canvas canvas) {
		super.onDraw(canvas);
		int center_x = getWidth() / 2;
		int center_y = getHeight() / 2;

		if (layoutState == -1) {
			Bitmap errorBm = BitmapFactory.decodeResource(
					mContext.getResources(), R.drawable.ota_error_icon);

			Rect rect = new Rect();
			rect.left = (int) (center_x - errorBm.getWidth() / 2f);
			rect.top = (int) (center_y - errorBm.getHeight() / 2f);
			rect.right = (int) (center_x + errorBm.getWidth() / 2f);
			rect.bottom = (int) (center_y + errorBm.getHeight() / 2f);

			canvas.drawBitmap(errorBm, null, rect, null);
		} else {

            // 绘制进度
            int off = 10;
            RectF rect = new RectF(off, off, getWidth() - off, getHeight()
                    - off);
            paint.setStyle(Style.STROKE);
            canvas.drawArc(rect, 270, currProgerss / 100f * 360, false, paint);

            // 绘制进度数字
            paint.setTypeface(ResourcesCompat.getFont(mContext,
                    R.font.agencyb));
            String text = String.valueOf(currProgerss);
            paint.setStyle(Style.FILL);
            paint.setTextSize(Apputils.sp2px(mContext, 46));
            canvas.drawText(text, center_x - paint.measureText(text) / 2 - 20,
                    center_y, paint);
            float right = center_x + paint.measureText(text) / 2 - 20;

            // 绘制 百分号
            paint.setTypeface(ResourcesCompat.getFont(mContext,
                    R.font.agencyr));
            text = "%";
            paint.setTextSize(Apputils.sp2px(mContext, 10));
            canvas.drawText(text, right + 4, center_y, paint);

            // 绘制已完成
            paint.setTypeface(ResourcesCompat.getFont(mContext,
                    R.font.gothic));
            text = mContext.getString(R.string.finished);
            paint.setTextSize(Apputils.sp2px(mContext, 10));
            canvas.drawText(text, center_x - paint.measureText(text) / 2,
                    center_y + Apputils.dp2px(mContext, 22), paint);
        }
	}

	public int getCurrProgerss() {
		return currProgerss;
	}

	public int getLayoutState() {
		return layoutState;
	}

	/**
	 * 设置当前进度
	 * 
	 * @param currProgerss
	 */
	public void setCurrProgerss(int currProgerss) {
		this.currProgerss = currProgerss;
		invalidate();
	}

	/**
	 * 设置 View 当前的界面状态
	 * 
	 * @param currProgerss
	 *            0 表示正常升级状态, 1, 表示警告失败状态
	 */
	public void setLayoutState(int layoutState) {
		this.layoutState = layoutState;
		invalidate();
	}
}
