package com.totwoo.totwoo.widget;

import android.annotation.TargetApi;
import android.content.Context;
import android.graphics.Camera;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.os.Build;
import android.util.AttributeSet;
import android.util.Log;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.widget.RelativeLayout;

import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;

import java.util.concurrent.TimeUnit;

import rx.Observable;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;
import rx.functions.Action1;
import rx.schedulers.Schedulers;

/**
 * Created by huanggaowei on 2016/10/14.
 */

public class PagingView extends RelativeLayout {

    private static final String TAG = "PagingView";

    public interface pagingListener {
        void open();
    }

    public void setPagingListener(pagingListener pagingListener) {
        mPagingListener = pagingListener;
    }

    public pagingListener mPagingListener;

    public Camera mCamera;

    public Matrix mMatrix;

    public GestureDetector mGestureDetector;

    public boolean mOpening;
    //Y轴角度
    public float mCameraY;

    public float mMaxMoveDistance;

    public boolean isOpening;

    public boolean isOpening() {
        return isOpening;
    }

    public PagingView(Context context) {
        super(context);
        init();
    }

    public PagingView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public PagingView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }


    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    public PagingView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init();
    }

    private void init() {
        mCamera = new Camera();
        mMatrix = new Matrix();
        mMaxMoveDistance = (Apputils.getScreenWidth(getContext()) / 2);
        mGestureDetector = new GestureDetector(getContext(), new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
                Log.i(TAG, "onFling");

                return super.onFling(e1, e2, velocityX, velocityY);
            }


            @Override
            public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
                //X轴移动距离小于50并且没有开始渐变则不处理
                if (Math.abs(e1.getRawX() - e2.getRawX()) < 50 && mOpening) {
                    return true;
                }

                //Y轴移动距离大于50并且没有开始渐变则不处理
                if (Math.abs(e1.getRawY() - e2.getRawY()) > 50 && mOpening) {
                    return true;
                }
                //从右向左滑才打开
                if (e1.getRawX() < e2.getRawX()) {
                    return true;
                }
                //计算打开程度
                mCameraY = (e1.getRawX() - e2.getRawX()) / mMaxMoveDistance * 90;
                invalidate();
//                Log.i(TAG, "" + mCameraY);
                return super.onScroll(e1, e2, distanceX, distanceY);
            }
        });
    }

    @Override
    protected void onDraw(Canvas canvas) {
        mMatrix.reset();
        mCamera.save();
        //负的才是往后转
        mCamera.rotateY(-mCameraY);
        mCamera.setLocation(0, 0, -50);
        mCamera.getMatrix(mMatrix);
        mCamera.restore();
//        mCamera.setLocation(0,0, -30);
//        mCamera.getMatrix(mMatrix);
//        canvas.translate(0,700);
        canvas.concat(mMatrix);
        int alphalevel = mCameraY > 60 ? 0 : 100 - (int) mCameraY * 100 / 60;
        //根据打开程度设置阴影
        setBackgroundColor(Color.argb(alphalevel, 0, 0, 0));
        super.onDraw(canvas);
    }

    Subscription subscribe;

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
            case MotionEvent.ACTION_MOVE:
                mGestureDetector.onTouchEvent(event);
                if (subscribe != null) {
                    subscribe.unsubscribe();
                }
                break;
            case MotionEvent.ACTION_UP:
                //翻到看不见才算打开否则合上封面
                if (mCameraY < 90) {
                    if (subscribe != null) {
                        subscribe.unsubscribe();
                    }
                    subscribe = Observable.interval(20, TimeUnit.MILLISECONDS)
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeOn(Schedulers.newThread())
                            .subscribe(new Action1<Long>() {
                                @Override
                                public void call(Long aLong) {
                                    mCameraY = mCameraY - 2;
                                    mCameraY = mCameraY < 0 ? 0 : mCameraY;
                                    if (mCameraY == 0) {
                                        subscribe.unsubscribe();
                                    }
                                    invalidate();
                                }
                            }, new Action1<Throwable>() {
                                @Override
                                public void call(Throwable throwable) {
                                    // RxJava错误处理回调
                                    LogUtils.e("PagingView", "Animation error: " + throwable.getMessage());
                                    if (subscribe != null) {
                                        subscribe.unsubscribe();
                                    }
                                }
                            });
                } else {
                    isOpening = true;
                    this.setVisibility(GONE);
                    if (mPagingListener != null) {
                        mPagingListener.open();
                    }
                }
                break;
        }
        return true;
    }
}
