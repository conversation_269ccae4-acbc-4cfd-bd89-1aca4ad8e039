package com.totwoo.totwoo.widget;

import android.content.Context;
import android.graphics.Point;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.FrameLayout;

import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.Owner;
import com.totwoo.totwoo.fragment.OnConfirmListener;
import com.totwoo.totwoo.utils.ConfigData;
import com.totwoo.totwoo.widget.WheelView.OnWheelViewListener;

import java.util.ArrayList;
import java.util.Calendar;

public class PeriodSettingView extends FrameLayout implements OnConfirmListener {
	private Context mContext;
	private final int MIN_YEAR = 1940;

	private Calendar calendar;

	private WheelView yearView;
	private WheelView monthView;

	public int getN_year() {
		return n_year;
	}

	public void setN_year(int n_year) {
		this.n_year = n_year;
	}

	public int getN_month() {
		return n_month;
	}

	public void setN_month(int n_month) {
		this.n_month = n_month;
	}

	public int getN_day() {
		return n_day;
	}

	public void setN_day(int n_day) {
		this.n_day = n_day;
	}

	private WheelView dayView;

	private ArrayList<String> birthDays = new ArrayList<String>();

	private ArrayList<String> months = new ArrayList<>();

	/** 当前表盘的日期 */
	private int n_year, n_month, n_day;

	public PeriodSettingView(Context context) {
		this(context, null, 0);
	}

	public PeriodSettingView(Context context, AttributeSet attrs) {
		this(context, attrs, 0);
	}

	public PeriodSettingView(Context context, AttributeSet attrs,
                             int defStyleAttr, int defStyleRes) {
		this(context, attrs, defStyleAttr);
	}

	public PeriodSettingView(Context context, AttributeSet attrs,
                             int defStyleAttr) {
		super(context, attrs, defStyleAttr);
		mContext = context;
		LayoutInflater.from(context).inflate(
				R.layout.fragment_birthday_setting, this);
		calendar = Calendar.getInstance();

		yearView = (WheelView) findViewById(R.id.setting_brithday_year_view);
		monthView = (WheelView) findViewById(R.id.setting_brithday_month_view);
		dayView = (WheelView) findViewById(R.id.setting_brithday_day_view);

		ConfigData.init(context);
		yearView.setItems(ConfigData.BIRTH_YEAR, 5, "");
		yearView.setOnWheelViewListener(wheelViewListener);
		monthView.setItems(ConfigData.BIRTH_MONTH, 5, "");
		monthView.setOnWheelViewListener(wheelViewListener);
		dayView.setOnWheelViewListener(wheelViewListener);
		initDayView(yearView.getSeletedIndex() + MIN_YEAR,
				monthView.getSeletedIndex() + 1);

		// 设置默认的日期
		setDefaultValue();
	}

	/**
	 * 根据用户设置， 设置默认的日期<br>
	 * 如果用户已经设置经期时间， 默认问用户当前经期时间， 如果未设置， 则显示默认日期，上个月的今天。
	 *
	 */
	private void setDefaultValue() {
		//


		n_day = 15;// 日
		n_month = 6;// 月
		n_year = 1985;// 年

		Owner owner = ToTwooApplication.owner;

		if (owner != null && !TextUtils.isEmpty(owner.getPeriodDay())) {
			String[] days = owner.getPeriodDay().split("-");
			if (days.length == 3) {
				n_year = Integer.parseInt(days[0]);
				n_month = Integer.parseInt(ConfigData
						.enMonth(days[1], mContext));
				n_day = Integer.parseInt(days[2]);
			}
		}

        if (n_year < 1940){
            n_year = 1985;
        }

        if (n_month <= 0){
            n_month = 6;
        }

        if (n_day <= 0){
            n_day = 15;
        }

		yearView.setSeletion(n_year - MIN_YEAR);
		monthView.setSeletion(n_month - 1);
		dayView.setSeletion(n_day - 1);

	}

	OnWheelViewListener wheelViewListener = new OnWheelViewListener() {
		public void onSelected(WheelView wheelView, int selectedIndex,
				String item) {
			switch (wheelView.getId()) {
			case R.id.setting_brithday_year_view:
				if (n_year != yearView.getSeletedIndex() + MIN_YEAR) {
					n_year = yearView.getSeletedIndex() + MIN_YEAR;
					initDayView(n_year, n_month);
				}
//				LogUtils.i("day", dayView.getSeletedIndex() + 1 + "");
				n_year = yearView.getSeletedIndex() + MIN_YEAR;// 年
				break;
			case R.id.setting_brithday_month_view:
				if (n_month != monthView.getSeletedIndex() + 1) {
					n_month = monthView.getSeletedIndex() + 1;
					initDayView(n_year, n_month);
				}
				n_month = monthView.getSeletedIndex() + 1;// 月
//				LogUtils.i("day", dayView.getSeletedIndex() + 1 + "");
				break;
			case R.id.setting_brithday_day_view:
				n_day = dayView.getSeletedIndex() + 1;// 日
				break;
			}

		}
    };

	/**
	 * 因为每月的天数需要动态计算， 每一次年月变动都需要重新更新
	 */
	private void initDayView(int year, int month) {
//		calendar.get(Calendar.YEAR);
//		if(year == calendar.get(Calendar.YEAR)){
//			monthView.setItems(ConfigData.BIRTH_MONTH, 5, "");
//		}
		setDataToBirthDays();
		dayView.setItems(birthDays, 5, "");
	}

	private void setMonths(){
		months.clear();

	}

	private void setDataToBirthDays() {
		birthDays.clear();
		int day = getDay(yearView.getSeletedIndex() + MIN_YEAR,
				monthView.getSeletedIndex() + 1);
		for (int i = 1; i <= day; i++) {
			if (!Apputils.systemLanguageIsChinese(mContext)) {
				switch (i) {
				case 1:
				case 21:
				case 31:
					birthDays.add(i + "st");
					break;
				case 2:
				case 22:
					birthDays.add(i + "nd");
					break;
				case 3:
				case 23:
					birthDays.add(i + "rd");
					break;
				default:
					birthDays.add(i + getContext().getString(R.string.th));
					break;
				}
			} else {
				birthDays.add(i + getContext().getString(R.string.th));
			}

		}
	}

	private int getDay(int year, int month) {
		int day = 30;
		boolean flag = false;
		switch (year % 4) {
		case 0:
			flag = true;
			break;
		default:
			flag = false;
			break;
		}
		switch (month) {
		case 1:
		case 3:
		case 5:
		case 7:
		case 8:
		case 10:
		case 12:
			day = 31;
			break;
		case 2:
			day = flag ? 29 : 28;
			break;
		default:
			day = 30;
			break;
		}
		return day;
	}

	@Override
	public void onSaved() {
		ToTwooApplication.owner.setPeriodDay(n_year + "-" + n_month + "-"
				+ n_day);
	}

	@Override
	public Point getCenterPoint() {
		return null;
	}

	@Override
	public void loadAnim() {
	}
}
