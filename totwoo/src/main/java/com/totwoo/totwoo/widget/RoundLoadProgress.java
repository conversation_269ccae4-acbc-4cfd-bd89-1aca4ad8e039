package com.totwoo.totwoo.widget;

import android.annotation.TargetApi;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.widget.ImageView;

import com.totwoo.totwoo.R;

/**
 * 自定义的圆形进度条, 继承自 ImageView, 围绕中心 View 绘制圆形进度
 * Created by lixingmao on 16/7/14.
 */
public class RoundLoadProgress extends ImageView {

    /**
     * 进度条宽度
     */
    private int ringWidth;

    /**
     * 进度条颜色
     */
    private int ringColor;
    /**
     * 进度条背景颜色
     */
    private int ringBgColor;

    /**
     * 当前进度, 默认最大进度为 100
     */
    private int progress;

    /**
     * 设置最大进度
     */
    private int maxProgress = 100;

    /**
     * 是否显示进度条
     */
    private boolean showProgress ;

    private Paint mPaint;

    private RectF mRect;


    public RoundLoadProgress(Context context) {
        super(context);
        init(context, null);
    }

    public RoundLoadProgress(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public RoundLoadProgress(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    @TargetApi(android.os.Build.VERSION_CODES.LOLLIPOP)
    public RoundLoadProgress(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        setLayerType(LAYER_TYPE_SOFTWARE, null);

        TypedArray cc = context.obtainStyledAttributes(attrs, R.styleable.RoundLoadProgress);

        ringWidth = cc.getDimensionPixelSize(R.styleable.RoundLoadProgress_progressWidth, 20);
        ringColor = cc.getColor(R.styleable.RoundLoadProgress_progressColor, context.getResources().getColor(R.color.app_theme_color));
        ringBgColor = cc.getColor(R.styleable.RoundLoadProgress_progressBackground, Color.parseColor("#1f000000"));
        showProgress = cc.getBoolean(R.styleable.RoundLoadProgress_showProgress, false);

        cc.recycle();

        mPaint = new Paint();
        mPaint.setStrokeWidth(ringWidth);
        mPaint.setDither(true);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setAntiAlias(true);

        mRect = new RectF();
    }

    @Override
    protected void onDraw(Canvas canvas) {

        int layer = canvas.save();

        // 压缩图层, 让父控件 ImageView 绘制中心图片
        float scal = (getWidth()-ringWidth*2f)/getWidth();
        canvas.scale(scal, scal, getWidth()/2, getWidth()/2);

        super.onDraw(canvas);

        // 回复之前的图层比例
        canvas.restoreToCount(layer);

        if (!showProgress){
            return;
        }

        // 绘制进度
        float offset = ringWidth / 2f;
        mRect.left = offset;
        mRect.top = offset;
        mRect.right = getWidth() - offset;
        mRect.bottom = getHeight() - offset;

        // 绘制进度
        mPaint.setColor(ringBgColor);
        canvas.drawOval(mRect, mPaint);

        mPaint.setColor(ringColor);
        float angle = (progress * 360f) / maxProgress;
        canvas.drawArc(mRect, 270, angle, false, mPaint);
    }

    /**
     * 设置进度
     *
     * @param progress
     */
    public void setProgress(int progress) {
        this.progress = progress;
        invalidate();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {

        int wideMode = MeasureSpec.getMode(widthMeasureSpec);
        int measureWide = MeasureSpec.getSize(widthMeasureSpec);
        int heightMode = MeasureSpec.getMode(heightMeasureSpec);
        int measureHeight = MeasureSpec.getSize(heightMeasureSpec);

        super.onMeasure(MeasureSpec.makeMeasureSpec(measureWide + ringWidth * 2, wideMode),
                MeasureSpec.makeMeasureSpec(measureHeight + ringWidth * 2, heightMode));
    }

    public int getProgress() {
        return progress;
    }

    /**
     * 设置最大进度, 默认100
     *
     * @param maxProgress
     */
    public void setMaxProgress(int maxProgress) {
        this.maxProgress = maxProgress;
    }


    public int getRingWidth() {
        return ringWidth;
    }

    public void setRingWidth(int ringWidth) {
        this.ringWidth = ringWidth;
    }

    public int getRingColor() {
        return ringColor;
    }

    public void setRingColor(int ringColor) {
        this.ringColor = ringColor;
    }

    public int getRingBgColor() {
        return ringBgColor;
    }

    public void setRingBgColor(int ringBgColor) {
        this.ringBgColor = ringBgColor;
    }

    public boolean isShowProgress() {
        return showProgress;
    }

    public void setShowProgress(boolean showProgress) {
        this.showProgress = showProgress;
        invalidate();
    }
}
