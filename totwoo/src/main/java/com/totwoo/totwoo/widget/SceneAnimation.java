package com.totwoo.totwoo.widget;

import android.os.Handler;
import android.widget.ImageView;


/**
 * Created by xinyoulingxi on 2017/10/25.
 */

public class SceneAnimation
{
    private static final Handler handler = new Handler();
    private ImageView mImageView;
    private int[] mFrameRess;
    private int[] mDurations;
    private int mDuration;

    private int mLastFrameNo;
    private long mBreakDelay;

    public SceneAnimation(ImageView pImageView, int[] pFrameRess, int[] pDurations)
    {
        mImageView = pImageView;
        mFrameRess = pFrameRess;
        mDurations = pDurations;
        mLastFrameNo = pFrameRess.length - 1;

        mImageView.setBackgroundResource(mFrameRess[0]);
        play(1);
    }

    public SceneAnimation(ImageView pImageView, int[] pFrameRess, int pDuration, boolean start)
    {
        mImageView = pImageView;
        mFrameRess = pFrameRess;
        mDuration = pDuration;
        mLastFrameNo = pFrameRess.length - 1;

        mImageView.setBackgroundResource(mFrameRess[0]);
        if (start)
            playConstant(1);
    }

    public void start()
    {
        playConstant(1);
    }

    public SceneAnimation(ImageView pImageView, int[] pFrameRess, int pDuration, long pBreakDelay)
    {
        mImageView = pImageView;
        mFrameRess = pFrameRess;
        mDuration = pDuration;
        mLastFrameNo = pFrameRess.length - 1;
        mBreakDelay = pBreakDelay;

        mImageView.setBackgroundResource(mFrameRess[0]);
        playConstant(1);
    }

    private void play(final int pFrameNo)
    {
        handler.postDelayed(new Runnable()
        {
            public void run()
            {
                mImageView.setBackgroundResource(mFrameRess[pFrameNo]);
                if (pFrameNo == mLastFrameNo)
                    play(0);
                else
                    play(pFrameNo + 1);
            }
        }, mDurations[pFrameNo]);
    }

    private class MyRunnable implements Runnable
    {
        private int pFrameNo;
        public MyRunnable(int pFrameNo)
        {
            this.pFrameNo = pFrameNo;
        }

        public void setFrame(int pFrameNo)
        {
            this.pFrameNo = pFrameNo;
        }

        @Override
        public void run()
        {
            mImageView.setBackgroundResource(mFrameRess[pFrameNo]);

            if (pFrameNo == mLastFrameNo)
                playConstant(0);
            else
                playConstant(pFrameNo + 1);
        }
    }

    private MyRunnable runnable;
    private void playConstant(final int pFrameNo)
    {
        if (runnable == null)
            runnable = new MyRunnable(pFrameNo);
        runnable.setFrame(pFrameNo);
        if (pFrameNo == mLastFrameNo)
            return;
        handler.postDelayed(runnable, pFrameNo == mLastFrameNo && mBreakDelay > 0 ? mBreakDelay : mDuration);
    }

    public void stop()
    {
        handler.removeCallbacks(runnable);
    }
}