package com.totwoo.totwoo.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.ColorFilter;
import android.graphics.Paint;
import android.graphics.PixelFormat;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SortedList;
import androidx.recyclerview.widget.SortedListAdapterCallback;

import com.etone.framework.utils.ObjectUtils;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.data.nfc.SecretInfoBean;

import java.util.List;
import java.util.WeakHashMap;

/**
 * 封装 NFC 首饰对应用户信息的列表, 及相关逻辑
 * <p>
 * 注: 内部数据仅作引用, 禁止直接操作数据; 外部修改数据后, 记得调用 Notify 即可
 */
public class SecretGridView extends RecyclerView {
    public static final int ADDITIONAL_ACTION_TYPE_NONE = 0;
    public static final int ADDITIONAL_ACTION_TYPE_ADD = 1;
    public static final int ADDITIONAL_ACTION_TYPE_CUSTOM_URL = 2;

    private Context context;
    private SortedList<SecretInfoBean> secretInfoList;
    private SecretInfoAdapter adapter;
    private SecretInfoClickListener secretInfoClickListener;
    private boolean editMode;

    private WeakHashMap<String, CustomUrlDrawable> customUrlDrawableCache;

    private boolean selectMode = false;

    /**
     * 数据之外最后一项追加 Action, 对应的 Type
     * 参考 SecretGridView.ADDITIONAL_ACTION_TYPE_*
     */
    private int additionalType = ADDITIONAL_ACTION_TYPE_NONE;

    /**
     * Icon 背景色, 默认 #ff222222 的圆角, 如有定制, 可设置
     */
    private int iconBackgroundRes = R.drawable.shape_secret_icon_bg_2;

    public SecretGridView(Context context) {
        super(context);
        init();
    }

    public SecretGridView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public SecretGridView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }


    /**
     * 初始化逻辑
     */
    private void init() {
        context = getContext();
        setOverScrollMode(OVER_SCROLL_NEVER);
        GridLayoutManager lm = new GridLayoutManager(getContext(), 4);
        setLayoutManager(lm);

        customUrlDrawableCache = new WeakHashMap<>(4);

        adapter = new SecretInfoAdapter();
        secretInfoList = new SortedList<>(SecretInfoBean.class, new SortedListAdapterCallback<SecretInfoBean>(adapter) {
            @Override
            public int compare(SecretInfoBean o1, SecretInfoBean o2) {
                return o1.getType() != null && o2.getType() != null
                        ? o1.getType().getOrder() - o2.getType().getOrder()
                        : 0;
            }

            @Override
            public boolean areContentsTheSame(SecretInfoBean oldItem, SecretInfoBean newItem) {
                return ObjectUtils.isEquals(oldItem.getType(), newItem.getType())
                        && ObjectUtils.isEquals(oldItem.getName(), newItem.getName())
                        && oldItem.isSelected() == newItem.isSelected()
                        && ObjectUtils.isEquals(oldItem.getValue(), newItem.getValue())
                        && ObjectUtils.isEquals(oldItem.getExtraData(), newItem.getExtraData());
            }

            @Override
            public boolean areItemsTheSame(SecretInfoBean item1, SecretInfoBean item2) {
                // 自定义链接判断 ID, 其他直接判断类型
                try {
                    if (item1.getType().isCustom() && item2.getType().isCustom()) {
                        return ObjectUtils.isEquals(item1.getId(), item2.getId());
                    } else {
                        return ObjectUtils.isEquals(item1.getType(), item2.getType());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    return false;
                }
            }
        });

        setAdapter(adapter);
    }


    /**
     * 设置或者更新数据
     *
     * @param list
     */
    @SuppressLint("NotifyDataSetChanged")
    public void setSecretInfoList(List<SecretInfoBean> list) {
        if (list != null) {
            // 内部会调用刷新
            secretInfoList.replaceAll(list);
            // 调用下面方法, 强制全局刷新, 否则在存在 ADDITIONAL_ACTION 的情况下,
            // 会造成 RecyclerView 定位到最后位置, 滚动到底部
            adapter.notifyDataSetChanged();
        }
    }

    /**
     * 添加点击监听
     *
     * @param secretInfoClickListener
     */
    public void setSecretInfoClickListener(SecretInfoClickListener secretInfoClickListener) {
        this.secretInfoClickListener = secretInfoClickListener;
    }


    /**
     * 单个数据变化通知
     */
    public void notifyItemViewer(SecretInfoBean bean) {
        if (secretInfoList == null || bean == null) {
            return;
        }
        int index = secretInfoList.indexOf(bean);

        if (index != SortedList.INVALID_POSITION) {
            adapter.notifyItemChanged(index);
        }
    }


    /**
     * 单个数据变化通知
     */
    public void notifyItemAddOrNotify(SecretInfoBean bean) {
        if (secretInfoList == null || bean == null) {
            return;
        }
        // 首次添加数据, 默认选择
        bean.setSelected(true);
        int index = secretInfoList.indexOf(bean);

        secretInfoList.add(bean);
        if (index != SortedList.INVALID_POSITION) {
            adapter.notifyItemChanged(index);
        }
    }


    /**
     * 单个数据变化通知
     */
    public void notifyItemRemove(SecretInfoBean bean) {
        if (secretInfoList == null || bean == null) {
            return;
        }
        secretInfoList.remove(bean);
    }

    /**
     * 切换编辑模式, 返回切换后的编辑模式
     */
    @SuppressLint("NotifyDataSetChanged")
    public boolean toggleEditMode() {
        editMode = !editMode;
        if (adapter != null) {
            adapter.notifyDataSetChanged();
        }
        LogUtils.d("SecretGridView toggleEditMode: " + editMode);
        return editMode;
    }

    /**
     * 更新所有
     */
    @SuppressLint("NotifyDataSetChanged")
    public void notifyAllItem() {
        if (adapter != null) {
            adapter.notifyDataSetChanged();
        }
    }

    /**
     * 切换选择模式, 返回切换后的模式状态
     */
    @SuppressLint("NotifyDataSetChanged")
    public boolean toggleSelectMode(boolean selectMode) {
        this.selectMode = selectMode;
        if (adapter != null) {
            adapter.notifyDataSetChanged();
        }
        LogUtils.d("SecretGridView setSelectMode: " + selectMode);
        return selectMode;
    }

    public boolean isSelectMode() {
        return selectMode;
    }

    /**
     * 设置 Icon 的背景, 仅支持 ResId
     *
     * @param resId
     */
    public void setIconBackground(int resId) {
        this.iconBackgroundRes = resId;
    }

    /**
     * 当前编辑状态
     *
     * @return
     */
    public boolean isEditMode() {
        return editMode;
    }

    /**
     * 追加 Item 类型切换
     *
     * @param type
     */
    public void setAdditionalType(int type) {
        if (additionalType == type) {
            return;
        }
        boolean oldNone = additionalType == ADDITIONAL_ACTION_TYPE_NONE;
        additionalType = type;

        if (adapter == null) {
            return;
        }
        if (oldNone) {
            adapter.notifyItemInserted(secretInfoList != null ? secretInfoList.size() : 0);
        } else {
            adapter.notifyItemChanged(secretInfoList != null ? secretInfoList.size() : 0);
        }
    }


    static class SecretInfoViewHolder extends ViewHolder {
        private ImageView iconView;
        private View closeView;
        private TextView titleView;
        private CheckBox selectBox;

        public SecretInfoViewHolder(@NonNull ViewGroup itemView) {
            super(itemView);
            iconView = itemView.findViewById(R.id.secret_info_icon);
            closeView = itemView.findViewById(R.id.secret_info_close);
            titleView = itemView.findViewById(R.id.secret_info_title);
            selectBox = itemView.findViewById(R.id.secret_info_cb);
        }
    }


    public interface SecretInfoClickListener {
        /**
         * 点击回调, 注意判断编辑模式, 编辑模式下为删除操作
         *
         * @param bean
         */
        void onClick(SecretInfoBean bean);

        /**
         * Action 点击回调
         *
         * @param additionalType
         */
        void onAdditionalClick(int additionalType);
    }


    class SecretInfoAdapter extends Adapter<SecretInfoViewHolder> {
        @NonNull
        @Override
        public SecretInfoViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            ViewGroup itemView = (ViewGroup) View.inflate(context, R.layout.nfc_secret_info_item, null);
            return new SecretInfoViewHolder(itemView);
        }

        @Override
        public void onBindViewHolder(@NonNull SecretInfoViewHolder holder, int position) {
            holder.iconView.setBackgroundResource(iconBackgroundRes);

            if (position >= secretInfoList.size()) {
                holder.closeView.setVisibility(GONE);
                holder.selectBox.setVisibility(GONE);
                switch (additionalType) {
                    case ADDITIONAL_ACTION_TYPE_ADD:
                        holder.titleView.setText("");
                        holder.iconView.setImageResource(R.drawable.icon_add_wite_large);

                        break;
                    case ADDITIONAL_ACTION_TYPE_CUSTOM_URL:
                        holder.titleView.setText(R.string.custom_url);
                        holder.iconView.setImageResource(R.drawable.icon_secret_url);
                        break;
                    case ADDITIONAL_ACTION_TYPE_NONE:
                    default:
                        break;
                }

                holder.iconView.setOnClickListener(v -> {
                    if (secretInfoClickListener != null) {
                        secretInfoClickListener.onAdditionalClick(additionalType);
                    }
                });
                return;
            }

            SecretInfoBean bean = secretInfoList.get(position);

            if (bean.getType().isCustom()) {
                // 自定义链接的图标特殊处理:
                // 没有 Value 数据, 显示类型默认图标
                // 有 Name 数据但是没有 color 数据, 随机选取色值, 生成对应 Drawable
                // 有 color 数据, 直接使用 color 色值, 生成对应的 Drawable
                if (TextUtils.isEmpty(bean.getName())) {
                    holder.iconView.setImageResource(bean.getType().iconId);
                } else {
                    String colorStr = bean.getExtraData() != null ? bean.getExtraData().get("color") : null;
                    int color = parseColorFromServer(colorStr);
                    holder.iconView.setImageDrawable(getCustomDrawable(context, color, bean.getName().substring(0, 1)));
                }
            } else {
                holder.iconView.setImageResource(bean.getType().iconId);
            }

            // CheckBox 的选中与否
            if (selectMode) {
                holder.itemView.setOnClickListener(v -> {
                    if (secretInfoClickListener != null) {
                        secretInfoClickListener.onClick(bean);
                    }
                });
            } else {
                holder.itemView.setOnClickListener(null);
            }

            holder.iconView.setOnClickListener(v -> {
                if (secretInfoClickListener != null) {
                    secretInfoClickListener.onClick(bean);
                }
            });
            if (bean.getType().isCustom() && !TextUtils.isEmpty(bean.getName())) {
                holder.titleView.setText(bean.getName());
            } else {
                holder.titleView.setText(bean.getType().nameRes);
            }
            // 编辑模式检查
            if (editMode) {
                holder.closeView.setVisibility(VISIBLE);
                holder.closeView.setOnClickListener(v -> {
                    if (secretInfoClickListener != null) {
                        secretInfoClickListener.onClick(bean);
                    }
                });
            } else {
                holder.closeView.setVisibility(GONE);
            }
            // 选择模式检查
            if (isSelectMode() || (!editMode && bean.isSelected())) {
                holder.selectBox.setVisibility(VISIBLE);
                holder.selectBox.setChecked(bean.isSelected());
                holder.selectBox.setButtonDrawable(isSelectMode() ? R.drawable.secret_checkbox_bg : R.drawable.icon_show);
            } else {
                holder.selectBox.setVisibility(GONE);
            }
        }

        /**
         * 数据中存储了通服务器协定交互的字符串, 这里根据 UI 解析成对应的颜色值
         *
         * @param colorStr
         * @return
         */
        private int parseColorFromServer(String colorStr) {
            if (!TextUtils.isEmpty(colorStr)) {
                switch (colorStr) {
                    case "red":
                        return 0xFFFE0100;
                    case "yellow":
                        return 0xFFF7B500;
                    case "green":
                        return 0xFF20B6AB;
                    case "orange":
                        return 0xFFFD7300;
                    case "blue":
                        return 0xFF0091FF;
                    default:
                        try {
                            return Color.parseColor(colorStr);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                }
            }
            return 0;
        }

        /**
         * 做自定义 URL Drawable 缓存, 防止重复创建
         *
         * @param context
         * @param color
         * @param name
         * @return
         */
        private Drawable getCustomDrawable(Context context, int color, String name) {
            if (customUrlDrawableCache == null) {
                customUrlDrawableCache = new WeakHashMap<>(4);
            }
            String key = color + name;
            CustomUrlDrawable drawable = customUrlDrawableCache.get(key);
            if (drawable == null) {
                drawable = new CustomUrlDrawable(context, color, name);
                customUrlDrawableCache.put(key, drawable);
            }
            return drawable;
        }

        @Override
        public int getItemCount() {
            int count = secretInfoList == null ? 0 : secretInfoList.size();
            return hasAdditionalAction() ? count + 1 : count;
        }

        private boolean hasAdditionalAction() {
            return additionalType != ADDITIONAL_ACTION_TYPE_NONE;
        }
    }

    static class CustomUrlDrawable extends Drawable {
        private Context context;
        private int bgColor;
        private Paint paint;
        private String text;
        private int rx; // 圆角


        public CustomUrlDrawable(Context context, int bgColor, String text) {
            this.context = context;
            this.bgColor = bgColor;
            this.text = text;
            paint = new Paint();
            paint.setAntiAlias(true);
            rx = Apputils.dp2px(context, 12);
        }

        @Override
        public void draw(@NonNull Canvas canvas) {
            // 绘制背景
            paint.setStyle(Paint.Style.FILL);
            paint.setColor(bgColor);

            canvas.drawRoundRect(new RectF(getBounds()), rx, rx, paint);

            // 绘制文字
            paint.setColor(0xffffffff);
            paint.setTextSize(Apputils.sp2px(context, 29));
            float width = paint.measureText(text);
            Paint.FontMetrics fontM = paint.getFontMetrics();
            float y = (getBounds().height() - (fontM.descent - fontM.ascent)) / 2 - fontM.ascent;
            canvas.drawText(text, (getBounds().width() - width) / 2, y, paint);
        }

        @Override
        public void setAlpha(int alpha) {
        }

        @Override
        public void setColorFilter(@Nullable ColorFilter colorFilter) {
        }

        @Override
        public int getOpacity() {
            return PixelFormat.OPAQUE;
        }
    }
}
