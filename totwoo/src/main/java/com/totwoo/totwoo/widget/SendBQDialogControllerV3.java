package com.totwoo.totwoo.widget;

import static com.totwoo.totwoo.utils.NotifyUtil.LONG_VIBRATION_SEC;
import static com.totwoo.totwoo.utils.NotifyUtil.SHORT_VIBRATION_SEC;
import static com.zhpan.bannerview.constants.PageStyle.MULTI_PAGE;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.text.Editable;
import android.text.Layout;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannedString;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.style.ReplacementSpan;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.Group;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;

import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.ClickUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.blankj.utilcode.util.SpanUtils;
import com.blankj.utilcode.util.Utils;
import com.google.android.flexbox.FlexboxLayout;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.hjq.shape.view.ShapeTextView;
import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.adapter.CustomColorLibraryAdapter;
import com.totwoo.totwoo.bean.BQItemDataBean;
import com.totwoo.totwoo.bean.ColorLibraryBean;
import com.totwoo.totwoo.bean.JewelryNotifyModel;
import com.totwoo.totwoo.bean.MeanBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.data.TotwooLogic;
import com.totwoo.totwoo.utils.ACache;
import com.totwoo.totwoo.utils.AppObserver;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.umeng.analytics.MobclickAgent;
import com.zhpan.bannerview.BannerViewPager;
import com.zhpan.bannerview.BaseBannerAdapter;
import com.zhpan.bannerview.BaseViewHolder;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * Created by zlk on 2024/11/5.
 * 新版能自定义表情 皓月版
 */
public class SendBQDialogControllerV3 extends SendBQDialogController {
    private static final int CHINESE_JAPANESE_LIMIT = 15;
    private static final int OTHER_LANGUAGE_LIMIT = 20;

    // 初始化数据
    List<BQItemDataBean> data;

    //滑动表情
    BQItemDataBean selectedItem;

    TextView tvMeaning;

    ImageView newIV;

    //含义
    private int selectedIndex = -1; // 记录当前选中的索引


    public SendBQDialogControllerV3(Context context, TotwooLogic tl) {
        super(context, tl);

    }

    public void setData(List<BQItemDataBean> data) {
        this.data = data;
    }

    @Override
    public CustomDialog showBQDialog(boolean doPrepare) {

        dialog = new CustomDialog(context, R.style.send_totwoo_bg_dialog);
        View v = View.inflate(context, R.layout.the_heart_bq_dialog_new, null);
        dialog.setRootView(v);

        Window window = dialog.getWindow();

        if (window != null) {
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            window.getDecorView().setBackgroundResource(android.R.color.transparent);
            dialog.getWindow().setDimAmount(0.5f);
        }

        BQDialogListener listener = new BQDialogListener();
        tvMeaning = v.findViewById(R.id.the_heart_bq_dialog_tips);

        newIV = v.findViewById(R.id.the_heart_bq_dialog_img);


        v.findViewById(R.id.the_heart_bq_dialog_send).setOnClickListener(listener);

        BannerViewPager<BQItemDataBean> viewPager2 = v.findViewById(R.id.the_heart_bq_dialog_view_pager);

        // 设置适配器
        viewPager2.setAdapter(new SimpleAdapter())
                .setPageTransformer(new CurveTransformer())
                .setPageMargin(SizeUtils.dp2px(15))
                .setPageStyle(MULTI_PAGE)
                .setRevealWidth(SizeUtils.dp2px(110), SizeUtils.dp2px(110))
                .setOnPageClickListener((clickedView, position) -> {
                    selectedItem = data.get(position);
                    onSelect(tvMeaning, selectedItem.getText());
                }, true)
                .registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {

                    @Override
                    public void onPageSelected(int position) {
                        super.onPageSelected(position);
                        if (data == null || data.isEmpty()) {
                            return;
                        }
                        selectedItem = data.get(position);
                        //更新tvMeaning 内容
                        onSelect(tvMeaning, selectedItem.getText());

                        PreferencesUtils.put(ToTwooApplication.baseContext, CommonUtils.SHOW_BQ_CUSTOM__GUIDE_TAG, false);
                    }

                })
                .setScrollDuration(500)
                .create(data);

        //设置选中
        selectedItem = data.get(0);
        onSelect(tvMeaning, selectedItem.getText());

        tvMeaning.setOnClickListener(listener);

        this.doPrepare = doPrepare;
        View targetView = v.findViewById(R.id.the_heart_bq_dialog_view_pager_layout);
        viewPager2.postDelayed(() -> {
            boolean needShow = PreferencesUtils.getBoolean(ToTwooApplication.baseContext, CommonUtils.SHOW_BQ_CUSTOM__GUIDE_TAG, true);
            if (needShow) {
                Rect spanImageBounds = getSpanImageBoundsGlobal(tvMeaning);
                showGuide1(targetView, spanImageBounds);
            }
        }, 400);

        dialog.show();
        return dialog;
    }


    private Rect getSpanImageBoundsGlobal(TextView textView) {
        // 获取 TextView 的 Layout
        Layout layout = textView.getLayout();
        if (layout == null) return null;

        // 获取 TextView 的内容
        CharSequence text = textView.getText();

        // 确保 CharSequence 转换为 Spannable
        Spannable spannable;
        if (text instanceof Spannable) {
            spannable = (Spannable) text;
        } else if (text instanceof SpannedString) {
            spannable = new SpannableString(text);
        } else {
            return null;
        }

        // 获取所有 CustomDynamicDrawableSpan
        ReplacementSpan[] spans = spannable.getSpans(0, spannable.length(), ReplacementSpan.class);
        if (spans == null || spans.length == 0) {
            Log.d("Debug", "No ReplacementSpan found in text!");
            return null;
        }

        // 遍历 spans 并获取绘制区域
        for (ReplacementSpan span : spans) {

            int start = spannable.getSpanStart(span);
            int end = spannable.getSpanEnd(span);

            // 获取 Span 相对 TextView 的位置
            float left = layout.getPrimaryHorizontal(start);
            float right = layout.getPrimaryHorizontal(end);
            int line = layout.getLineForOffset(start);
            int top = layout.getLineTop(line);
            int bottom = layout.getLineBottom(line);

            // 转换为全局坐标
            int[] textViewLocation = new int[2];
            textView.getLocationOnScreen(textViewLocation); // 获取 TextView 在屏幕中的位置

            Rect bounds = new Rect();
            bounds.left = (int) (textViewLocation[0] + left);
            bounds.top = (int) (textViewLocation[1] + top);
            bounds.right = (int) (textViewLocation[0] + right);
            bounds.bottom = (int) (textViewLocation[1] + bottom);

            return bounds;
        }

        Log.d("Debug", "No CustomDynamicDrawableSpan matched.");
        return null;
    }


    public void showGuide1(View targetView, Rect spanImageBounds) {
        // 截图目标 View
        Bitmap highlightBitmap = getBitmapFromView(targetView);

        // 获取目标 View 的位置
        int[] location = new int[2];
        targetView.getLocationOnScreen(location);
        int targetX = location[0];
        int targetY = location[1];

        CustomDialog dialog = new CustomDialog(context, R.style.custom_dialog);
        View v = View.inflate(context, R.layout.the_heart_bq_new_guide1, null);

//        v 加个透明度动画 400ms
        v.animate().alpha(1f).setDuration(600).start();
        dialog.setRootView(v);

        // 设置高亮区域的图片
        ImageView highlightView = v.findViewById(R.id.highlightImage);
        highlightView.setImageBitmap(highlightBitmap);

        // 设置位置
        ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) highlightView.getLayoutParams();
        params.leftMargin = targetX;
        params.topMargin = targetY - BarUtils.getStatusBarHeight();
        highlightView.setLayoutParams(params);


        Window window = dialog.getWindow();

        if (window != null) {
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            window.getDecorView().setBackgroundResource(android.R.color.transparent);

            window.setLayout(
                    WindowManager.LayoutParams.MATCH_PARENT,
                    WindowManager.LayoutParams.MATCH_PARENT
            );
            window.setFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
                    WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS);
        }
        v.setOnClickListener(v1 -> {
            dialog.dismiss();

            //含义不能编辑
            if (TextUtils.equals(selectedItem.getIs_edit(), "0") || TextUtils.equals(selectedItem.getIs_edit(), "2")) {
                return;
            }
            showGuide2(spanImageBounds);
        });

        dialog.show();
    }


    public void showGuide2(Rect bounds) {
        // 截图目标 View
        if (bounds == null) {
            return;
        }

        CustomDialog dialog = new CustomDialog(context, R.style.custom_dialog);
        View v = View.inflate(context, R.layout.the_heart_bq_new_guide2, null);
        dialog.setRootView(v);

        // 设置高亮区域的图片
        ImageView highlightImage = v.findViewById(R.id.highlightImage);

        // 设置位置
        // 将蒙层的位置调整到目标区域
        ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) highlightImage.getLayoutParams();
//        params.width = bounds.width();
//        params.height = bounds.height();
        params.leftMargin = bounds.left;
        params.topMargin = bounds.top - BarUtils.getStatusBarHeight();
        highlightImage.setLayoutParams(params);


        Window window = dialog.getWindow();

        if (window != null) {
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            window.getDecorView().setBackgroundResource(android.R.color.transparent);

            window.setLayout(
                    WindowManager.LayoutParams.MATCH_PARENT,
                    WindowManager.LayoutParams.MATCH_PARENT
            );
            window.setFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
                    WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS);
        }
        v.setOnClickListener(v1 -> {
            PreferencesUtils.put(ToTwooApplication.baseContext, CommonUtils.SHOW_BQ_CUSTOM__GUIDE_TAG, false);
            v.animate().alpha(0f).setDuration(500).start();
            dialog.dismiss();
        });

        dialog.show();
    }


    public Bitmap getBitmapFromView(View view) {
        Bitmap bitmap = Bitmap.createBitmap(view.getWidth(), view.getHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        view.draw(canvas);
        return bitmap;
    }


    private void displayNew() {
        if (TextUtils.equals(selectedItem.getIs_read(), "0")) {
            newIV.setVisibility(View.VISIBLE);
//            selectedItem.setIsShowNew("1");

            //更新为已读
            HttpHelper.commonService.setRead(selectedItem.getId())
                    .subscribeOn(Schedulers.newThread())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(new AppObserver<>() {
                        @Override
                        public void onSuccess(Object object) {
                            selectedItem.setIs_read("1");
                            ACache.get(Utils.getApp()).put(CommonArgs.EMOJI_LIST, new Gson().toJson(data));
                        }
                    });
        } else {
            newIV.setVisibility(View.INVISIBLE);
        }
    }

    private void onSelect(TextView tvMeaning, String content) {
        displayNew();
        //更新tvMeaning 内容
        SpanUtils append = SpanUtils.with(tvMeaning)
                .append(CommonUtils.getCustomBQMeaning(content));
        switch (selectedItem.getIs_edit()) { //0-含义颜色都不能编辑， 1-含义颜色都能编辑（在可编辑情况下：长条TWO90 爱你app判断; 2- 只有颜色可编辑主要是TWO82）
            case "0":
                append.append("");
                break;
            case "1":
                append.appendImage(R.drawable.bq_edit);
                break;
            case "2": //可编辑颜色，链接设备
                if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_UNPAIRED) {
                    append.appendImage(R.drawable.bq_edit);
                }
                break;
            default:
                break;
        }
        append.create();
    }

    /**
     * 显示功能弹窗
     *
     * @return
     */
    public void showBQMenuDialog() {
        CustomDialog dialog = new CustomDialog(context, R.style.custom_dialog);
        View v = View.inflate(context, R.layout.the_bq_menu_dialog, null);
        dialog.setRootView(v);

        Window window = dialog.getWindow();
        if (window != null) {
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            window.getDecorView().setBackgroundResource(android.R.color.transparent);
        }

        ImageView iv = v.findViewById(R.id.the_heart_bq_dialog_img);
        TextView tv = v.findViewById(R.id.tv_meaning);

        TextView hint = v.findViewById(R.id.the_heart_bq_dialog_hint);

        iv.setImageResource(CommonUtils.getImg(selectedItem.getEmoji_encoding()));
        tv.setText(CommonUtils.getCustomBQMeaning(selectedItem.getText()));

        //未添加设备隐藏
        if (JewInfoSingleton.getInstance().getConnectState() == JewInfoSingleton.STATE_UNPAIRED) {
            hint.setVisibility(View.GONE);
        } else {
            hint.setVisibility(View.VISIBLE);
            hint.setText(getContentHint(selectedItem.getEmoji_encoding()));
        }

        TextView editMean = v.findViewById(R.id.btn_edit_meaning);
        TextView editColor = v.findViewById(R.id.btn_edit_color);

        if (selectedItem != null) {
            // //0-含义颜色都不能编辑， 1-含义颜色都能编辑（在可编辑情况下：长条TWO90 爱你app判断; 2- 只有颜色可编辑主要是TWO82）
            switch (selectedItem.getIs_edit()) {
                case "0":
                    editMean.setVisibility(View.GONE);
                    editColor.setVisibility(View.GONE);
                    break;
                case "1"://长条TWO90 爱你 含义不可编辑
                    if (BleParams.isCtJewlery() && selectedItem != null && TextUtils.equals(selectedItem.getEmoji_encoding(), CommonUtils.CONTENT_LOVE)) {
                        editMean.setVisibility(View.GONE);
                    } else {
                        editMean.setVisibility(View.VISIBLE);
                    }
                    editColor.setVisibility(View.VISIBLE);
                    break;
                case "2":
                    editMean.setVisibility(View.GONE);
                    if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_UNPAIRED) {
                        editColor.setVisibility(View.VISIBLE);
                    } else {
                        editColor.setVisibility(View.GONE);
                    }
                    break;
            }
            //未连接
            if (JewInfoSingleton.getInstance().getConnectState() == JewInfoSingleton.STATE_UNPAIRED) {
                editColor.setVisibility(View.GONE);
            }
        }


        if (Apputils.systemLanguageIsFA_DE_IT(context)) {
            editMean.setTextSize(12);
            editColor.setTextSize(12);
        } else {
            editMean.setTextSize(16);
            editColor.setTextSize(16);
        }

        editMean.setOnClickListener(v1 -> {
            showBQMeaningDialog(data -> {
                tv.setText(CommonUtils.getCustomBQMeaning(data.getText()));
            });

            //
            if (selectedItem.getEmoji_encoding().equals(CommonUtils.CONTENT_LOVE)) {
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.CUSTOM_MEME_LOVE_EDIT_MEAN_BUTTON);
            } else if (selectedItem.getEmoji_encoding().equals(CommonUtils.CONTENT_MISS)) {
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.CUSTOM_MEME_MISS_EDIT_MEAN_BUTTON);
            } else if (selectedItem.getEmoji_encoding().equals(CommonUtils.CONTENT_NEED_YOU)) {
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.CUSTOM_MEME_NEED_EDIT_MEAN_BUTTON);
            }
        });


        editColor.setOnClickListener(v1 -> {
            showBQColorDialog(false);
            if (selectedItem.getEmoji_encoding().equals(CommonUtils.CONTENT_LOVE)) {
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.CUSTOM_MEME_LOVE_EDIT_COLOR_BUTTON);
            } else if (selectedItem.getEmoji_encoding().equals(CommonUtils.CONTENT_MISS)) {
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.CUSTOM_MEME_MISS_EDIT_COLOR_BUTTON);
            } else if (selectedItem.getEmoji_encoding().equals(CommonUtils.CONTENT_NEED_YOU)) {
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.CUSTOM_MEME_NEED_EDIT_COLOR_BUTTON);
            }
        });
        v.findViewById(R.id.the_heart_bq_dialog_close).setOnClickListener(v1 -> {
            dialog.dismiss();
        });

        dialog.show();
    }

    //皓月独有的
    private String getContentHint(String emojiEncoding) {
        String customBQMeaning = CommonUtils.getCustomBQMeaning(emojiEncoding);
        switch (emojiEncoding) {
            case CommonUtils.CONTENT_MISS:
                if (BleParams.isButtonBatteryJewelry()) {//电池款
                    return context.getString(R.string.send_miss_battery, customBQMeaning);
                } else if (BleParams.isSM2()) {// 80蓝光
                    return context.getString(R.string.send_miss_80, customBQMeaning);  //
                } else if (BleParams.isMWJewlery() || BleParams.isCtJewlery()) {//皓月长条
                    return context.getString(R.string.send_miss_33, customBQMeaning);
                } else { //敲击
                    return context.getString(R.string.send_miss_tap, customBQMeaning);
                }

            case CommonUtils.CONTENT_LOVE:
                if (BleParams.isButtonBatteryJewelry() || BleParams.needRemovePYWOColor()) {//电池款 只能app发送
                    return context.getString(R.string.bq_how_send_need);
                } else if (BleParams.isSM2()) {// 80蓝光
                    return context.getString(R.string.send_love_80, customBQMeaning); //
                } else if (BleParams.isMWJewlery() || BleParams.isCtJewlery()) {//皓月长条
                    return context.getString(R.string.send_love_33, customBQMeaning);
                } else { //敲击
                    return context.getString(R.string.send_love_tap, customBQMeaning);
                }
            default:
                return context.getString(R.string.bq_how_send_need);
        }
    }


    //编辑含义
    public void showBQMeaningDialog(Listener listener) {
        CopyOnWriteArrayList<MeanBean> tags = new CopyOnWriteArrayList<>(CommonUtils.getRecommendedMeaning());

        CustomDialog dialog = new CustomDialog(context, R.style.custom_dialog);
        View v = View.inflate(context, R.layout.the_bq_meaning_dialog, null);
        dialog.setRootView(v);

        Window window = dialog.getWindow();
        if (window != null) {
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            window.getDecorView().setBackgroundResource(android.R.color.transparent);
        }
        EditText input = v.findViewById(R.id.the_heart_bq_dialog_edit);
        FlexboxLayout flexboxLayout = v.findViewById(R.id.tags);

        v.findViewById(R.id.the_heart_bq_dialog_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String inputContent = input.getText().toString().trim();
                if (TextUtils.isEmpty(inputContent)) {
                    dialog.dismiss();
                    return;
                }
                showExitDialog(dialog);
            }
        });

        //增加防重点击
        TextView saveBtn = v.findViewById(R.id.the_heart_bq_dialog_save);
//        saveBtn.setText(R.string.next_step);
        ClickUtils.applySingleDebouncing(saveBtn, v2 -> {
            String inputContent = input.getText().toString().trim();
            //有被占用提醒
            for (BQItemDataBean bqItemDataBean : data) {
                if (bqItemDataBean.getText().equals(inputContent)) {
                    ToastUtils.showLong(context, CommonUtils.compantGetString(R.string.custom_bq_meaning_used));
                    return;
                }
            }

            if (TextUtils.isEmpty(inputContent)) {
                dialog.dismiss();
                return;
            }

            // 1. 限制输入字符数量
            int charCount = calculateCharacterCount(inputContent);
            if (charCount > (containsChineseOrJapanese(inputContent)
                    ? CHINESE_JAPANESE_LIMIT
                    : OTHER_LANGUAGE_LIMIT)) {
                ToastUtils.showLong(context, CommonUtils.compantGetString(R.string._15_characters_max));
                return;
            }

            //接口 存储
            String meanContent;
            if (selectedIndex == -1) {
                meanContent = input.getText().toString().trim();
            } else {
                meanContent = tags.get(selectedIndex).getMeaningCode();
            }

            HttpHelper.commonService.SaveEmoji(ToTwooApplication.otherPhone,
                            selectedItem.getEmoji_encoding(),
                            meanContent)
                    .subscribeOn(Schedulers.newThread())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(new AppObserver<>() {
                        @Override
                        public void onSuccess(JsonObject jsonObject) {
                            dialog.dismiss();
                            //更新数据
                            selectedItem.setText(meanContent);
                            selectedItem.setId(jsonObject.get("id").getAsString());

                            listener.onResult(selectedItem);
                            onSelect(tvMeaning, selectedItem.getText());
                            ACache.get(Utils.getApp()).put(CommonArgs.EMOJI_LIST, new Gson().toJson(data));
//                            ToastUtils.showLong(context, CommonUtils.compantGetString(R.string.safe_doc_save_success));
//                            showBQColorDialog(true);
                        }

                        @Override
                        public void onFailed(int errCode, String msg) {
                            super.onFailed(errCode, msg);
                            ToastUtils.showLong(context, CommonUtils.compantGetString(R.string.verification_failed));
                        }
                    });
        });

        input.addTextChangedListener(new TextWatcher() {

            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                String inputText = s.toString();


                // 2. 检查输入是否与推荐标签一致
                for (int i = 0; i < flexboxLayout.getChildCount(); i++) {
                    TextView textView = (TextView) flexboxLayout.getChildAt(i);
                    if (s.toString().equalsIgnoreCase(textView.getText().toString())) {
                        handleTag(textView, true);
                        selectedIndex = i;
                    } else {
                        selectedIndex = -1;
                        handleTag(textView, false);
                    }
                }
            }
        });


        // 移除默认标签,当前已配置的默认标签不展示
        for (BQItemDataBean datum : data) {
            for (MeanBean tag : tags) {
                if (datum.getText().equals(tag.getMeaningCode())) {
                    tags.remove(tag);
                }
            }
        }

        for (int i = 0; i < tags.size(); i++) {
            int index = i;
            // 添加标签到 FlexboxLayout
            TextView textView = addTag(tags.get(i).getContent(), flexboxLayout);
            textView.setOnClickListener(v1 -> {
                //选中颜色变红
                // 重置上一个选中项的背景
                if (selectedIndex != -1) {
                    TextView previousSelectedTv = (TextView) flexboxLayout.getChildAt(selectedIndex);
                    handleTag(previousSelectedTv, false);
                }
                // 设置当前选中项的背景
                handleTag(textView, true);

                input.setText(CommonUtils.getCustomBQMeaning(tags.get(index).getContent()));
                input.setSelection(input.getText().toString().length());
                selectedIndex = index;
            });
        }

        for (int i = 0; i < flexboxLayout.getChildCount(); i++) {
            TextView textView = (TextView) flexboxLayout.getChildAt(i);
            if (CommonUtils.getCustomBQMeaning(selectedItem.getText()).equalsIgnoreCase(textView.getText().toString())) {
                handleTag(textView, true);
                selectedIndex = i;
            } else {
                handleTag(textView, false);
            }
        }
        dialog.show();
    }


    /**
     * 判断字符串是否包含中文或日语字符
     */
    private boolean containsChineseOrJapanese(String text) {
        for (char c : text.toCharArray()) {
            if (isChinese(c) || isJapanese(c)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 计算字符总数（中文/日语按1字符，其他按1字符）
     */
    private int calculateCharacterCount(String text) {
        int count = 0;
        for (char c : text.toCharArray()) {
            if (isChinese(c) || isJapanese(c)) {
                count++;
            } else {
                count++;
            }
        }
        return count;
    }

    /**
     * 判断是否为中文字符
     */
    private boolean isChinese(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        return ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B;
    }

    /**
     * 判断是否为日语字符
     */
    private boolean isJapanese(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        return ub == Character.UnicodeBlock.HIRAGANA
                || ub == Character.UnicodeBlock.KATAKANA;
    }

    private void handleTag(TextView textView, boolean isSelect) {
        if (isSelect) {
            textView.setBackgroundResource(R.drawable.tag_background_selected);
            textView.setTextColor(context.getResources().getColor(R.color.text_color_black));
        } else {
            textView.setBackgroundResource(R.drawable.tag_background);
            textView.setTextColor(context.getResources().getColor(R.color.tim_send_black));
        }
    }


    private TextView addTag(String text, FlexboxLayout flexboxLayout) {
        TextView textView = new TextView(context);
        textView.setText(text);
        textView.setTextColor(Color.parseColor("#FF333333"));
        textView.setBackgroundResource(R.drawable.tag_background);
        int pv = SizeUtils.dp2px(6);
        int ph = SizeUtils.dp2px(12);

        textView.setPadding(ph, pv, ph, pv);
        textView.setTextSize(14);

        // 设置布局参数
        FlexboxLayout.LayoutParams params = new FlexboxLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
        );

        int margin = SizeUtils.dp2px(12);
        params.setMargins(0, 0, margin, margin);  // 默认设置右边距
        flexboxLayout.addView(textView, params);
        return textView;
    }


    //编辑颜色
    public void showBQColorDialog(boolean isNext) {
        JewelryNotifyModel oldSetModel = new JewelryNotifyModel(true, selectedItem.getNotify_color(),
                "1".equals(selectedItem.getShock_type()) ? 6 : 3);

        JewelryNotifyModel nowSetModel = new JewelryNotifyModel(oldSetModel.isNotifySwitch(), oldSetModel.getFlashColor(), oldSetModel.getVibrationSeconds());

        CustomDialog dialog = new CustomDialog(context, R.style.custom_dialog);
        View v = View.inflate(context, R.layout.the_bq_color_dialog, null);
        dialog.setRootView(v);

        Window window = dialog.getWindow();
        if (window != null) {
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            window.getDecorView().setBackgroundResource(android.R.color.transparent);
        }

        ShapeTextView mLongVibrationTv = v.findViewById(R.id.long_vibration_tv);
        ShapeTextView mShortVibrationTv = v.findViewById(R.id.short_vibration_tv);
        ImageView mShortVibrationIv = v.findViewById(R.id.short_vibration_iv);
        ImageView mLongVibrationIv = v.findViewById(R.id.long_vibration_iv);

        Group group = v.findViewById(R.id.groupVibration);
        AxxPagView jewPag = v.findViewById(R.id.jewPag);

        if (BleParams.isCtJewlery() && selectedItem != null && selectedItem.getEmoji_encoding().equals(CommonUtils.CONTENT_LOVE)) {
            jewPag.setVisibility(View.VISIBLE);
            group.setVisibility(View.GONE);
        } else {
            jewPag.setVisibility(View.GONE);
            group.setVisibility(View.VISIBLE);
        }

        //电池款，没震动强度
        if (BleParams.isButtonBatteryJewelry()) {
            group.setVisibility(View.GONE);
        }

        TextView dialogMeaningContent = v.findViewById(R.id.the_heart_bq_dialog_meaning_content);
        dialogMeaningContent.setText(context.getString(R.string.bq_set_color_content, CommonUtils.getCustomBQMeaning(selectedItem.getEmoji_encoding())));

        switch (nowSetModel.getVibrationSeconds()) {
            case LONG_VIBRATION_SEC:
                setTextColorBtn(true, mLongVibrationTv, mShortVibrationTv, mShortVibrationIv, mLongVibrationIv);
                break;
            case SHORT_VIBRATION_SEC:
                setTextColorBtn(false, mLongVibrationTv, mShortVibrationTv, mShortVibrationIv, mLongVibrationIv);
                break;
        }

        mLongVibrationTv.setOnClickListener(v1 -> {
            nowSetModel.setVibrationSeconds(NotifyUtil.LONG_VIBRATION_SEC);
            notifyJewelryCustomBQ(nowSetModel);
            setTextColorBtn(true, mLongVibrationTv, mShortVibrationTv, mShortVibrationIv, mLongVibrationIv);
        });

        mShortVibrationTv.setOnClickListener(v1 -> {
            nowSetModel.setVibrationSeconds(NotifyUtil.SHORT_VIBRATION_SEC);
            notifyJewelryCustomBQ(nowSetModel);
            setTextColorBtn(false, mLongVibrationTv, mShortVibrationTv, mShortVibrationIv, mLongVibrationIv);
        });

        v.findViewById(R.id.the_heart_bq_dialog_close).setOnClickListener(v1 -> {
            if (TextUtils.equals(nowSetModel.getFlashColor(), oldSetModel.getFlashColor()) && nowSetModel.getVibrationSeconds() == oldSetModel.getVibrationSeconds()) {
                dialog.dismiss();
                return;
            }
            showExitDialog(dialog);
        });
        ClickUtils.applySingleDebouncing(v.findViewById(R.id.the_heart_bq_dialog_save), v1 -> {
            //网络请求
            HttpHelper.commonService.saveEmojiColor(ToTwooApplication.otherPhone,
                            selectedItem.getEmoji_encoding(),
                            nowSetModel.getVibrationSeconds() == LONG_VIBRATION_SEC ? "1" : "0",
                            NotifyUtil.getHexColorValue(nowSetModel.getFlashColor()),
                            isNext ? "1" : "0"
                    )
                    .subscribeOn(Schedulers.newThread())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(new AppObserver<>() {
                        @Override
                        public void onSuccess(JsonObject jsonObject) {
                            dialog.dismiss();
                            //保存信息指令 v2走本地，v3不走本地
//                            NotifyUtil.setCustomNotify(context, selectedItem.getEmoji_encoding(), nowSetModel);
                            selectedItem.setNotify_color(nowSetModel.getFlashColor());
                            selectedItem.setShock_type(nowSetModel.getVibrationSeconds() == LONG_VIBRATION_SEC ? "1" : "0");
                            ACache.get(Utils.getApp()).put(CommonArgs.EMOJI_LIST, new Gson().toJson(data));
                        }

                        @Override
                        public void onFailed(int errCode, String msg) {
                            super.onFailed(errCode, msg);
                            ToastUtils.showLong(context, CommonUtils.compantGetString(R.string.save_failed));
                        }
                    });
        });

        RecyclerView colorLibraryRecyclerView = v.findViewById(R.id.notify_setting_color_library_rv);
        colorLibraryRecyclerView.setHasFixedSize(true);

        int spanCount = BleParams.isCtJewlery() ? 7 : 6;
        colorLibraryRecyclerView.setLayoutManager(new GridLayoutManager(context, spanCount));
        CustomColorLibraryAdapter colorLibraryAdapter = new CustomColorLibraryAdapter(nowSetModel.getFlashColor(), spanCount,
                selectedItem.getEmoji_encoding().equals(CommonUtils.CONTENT_LOVE), false);

        colorLibraryRecyclerView.setAdapter(colorLibraryAdapter);
        colorLibraryAdapter.setOnItemClickListener((adapter, view, position) -> {
            ColorLibraryBean colorLibraryBean = colorLibraryAdapter.getItem(position);

            if (colorLibraryBean != null) {
                nowSetModel.setFlashColor(colorLibraryBean.getColor());//颜色名字
                colorLibraryAdapter.setSelectColor(colorLibraryBean.getColor());
                notifyJewelryCustomBQ(nowSetModel);
            }
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.CUSTOM_MEME_EDIT_COLOR_ALL);
        });

        dialog.show();
    }

    private void setTextColorBtn(boolean isLong, ShapeTextView mLongVibrationTv, ShapeTextView mShortVibrationTv, ImageView mShortVibrationIv, ImageView mLongVibrationIv) {
        if (isLong) {
            mLongVibrationIv.setVisibility(View.VISIBLE);
            mShortVibrationIv.setVisibility(View.GONE);

            mLongVibrationTv.getShapeDrawableBuilder()
                    .setSolidColor(0xFFFFFFFF)
                    .setStrokeWidth(SizeUtils.dp2px(1.5f))
                    .setStrokeColor(Color.parseColor("#FFCD2D64"))
                    // 注意：最后需要调用一下 intoBackground 方法才能生效
                    .intoBackground();


            mShortVibrationTv.getShapeDrawableBuilder()
                    .setSolidColor(Color.parseColor("#FFEBEBEB"))
                    .setStrokeWidth(SizeUtils.dp2px(1.5f))
                    .setStrokeColor(Color.parseColor("#FFEBEBEB"))
                    // 注意：最后需要调用一下 intoBackground 方法才能生效
                    .intoBackground();
        } else {
            mLongVibrationIv.setVisibility(View.GONE);
            mShortVibrationIv.setVisibility(View.VISIBLE);

            mShortVibrationTv.getShapeDrawableBuilder()
                    .setSolidColor(0xFFFFFFFF)
                    .setStrokeWidth(SizeUtils.dp2px(1.5f))
                    .setStrokeColor(Color.parseColor("#FFCD2D64"))
                    // 注意：最后需要调用一下 intoBackground 方法才能生效
                    .intoBackground();


            mLongVibrationTv.getShapeDrawableBuilder()
                    .setSolidColor(Color.parseColor("#FFEBEBEB"))
                    .setStrokeWidth(SizeUtils.dp2px(1.5f))
                    .setStrokeColor(Color.parseColor("#FFEBEBEB"))
                    // 注意：最后需要调用一下 intoBackground 方法才能生效
                    .intoBackground();
        }
    }


    public void showExitDialog(CustomDialog dialog) {
        final CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(context);
        commonMiddleDialog.setMessage(R.string.warn_no_save);
        commonMiddleDialog.setSure(v -> {
            commonMiddleDialog.dismiss();
            dialog.dismiss();
        });
        commonMiddleDialog.setCancel(R.string.cancel);
        commonMiddleDialog.show();
    }


    private void notifyJewelryCustomBQ(JewelryNotifyModel nowSetModel) {
        if (!ToTwooApplication.isDebug && JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            ToastUtils.show(context, R.string.error_jewelry_connect, Toast.LENGTH_LONG);
            return;
        }
        if (BleParams.isCodeJewelry()) {
            BluetoothManage.getInstance().notifyMorseCode(nowSetModel.getVibrationSeconds(), NotifyUtil.getColorValue(nowSetModel.getFlashColor()));
        } else {
            //长条，爱你 跑马灯
            if (BleParams.isCtJewlery() && selectedItem != null && selectedItem.getEmoji_encoding().equals(CommonUtils.CONTENT_LOVE)) {
                BluetoothManage.getInstance().notifyJewelry(NotifyUtil.MARQUEE_VIBRATION_SEC, nowSetModel.getFlashColorValue());
            } else {
                BluetoothManage.getInstance().notifyJewelry(nowSetModel.getVibrationSeconds(), nowSetModel.getFlashColorValue());
            }
        }
    }


    private class BQDialogListener implements View.OnClickListener {
        @Override
        public void onClick(View v) {
            switch (v.getId()) {
                case R.id.the_heart_bq_dialog_tips:

                    //0-含义颜色都不能编辑， 1-含义颜色都能编辑（在可编辑情况下：长条TWO90 爱你app判断; 2- 只有颜色可编辑主要是TWO82）
                    if (TextUtils.equals(selectedItem.getIs_edit(), "1")
                            || (TextUtils.equals(selectedItem.getIs_edit(), "2") && JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_UNPAIRED)) {
                        showBQMenuDialog();
                    }
                    break;

                case R.id.the_heart_bq_dialog_send:
                    sendCustomBQMessage(selectedItem.getEmoji_encoding(), selectedItem.getText());
                    break;
                default:
                    break;
            }
        }
    }


    static class SimpleAdapter extends BaseBannerAdapter<BQItemDataBean> {
        @Override
        protected void bindData(BaseViewHolder<BQItemDataBean> holder, BQItemDataBean data, int position, int pageSize) {
            holder.setImageResource(R.id.bq_item_image, CommonUtils.getImg(data.getEmoji_encoding()));
        }

        @Override
        public int getLayoutId(int viewType) {
            return R.layout.bq_item_layout;
        }
    }


    public interface Listener {
        void onResult(BQItemDataBean data);
    }
}
