package com.totwoo.totwoo.widget;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Typeface;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import com.totwoo.totwoo.R;


public class SettingDialog extends Dialog {

    Context context;
    View.OnClickListener onClickListener;
    View.OnClickListener onCancelListener;

    public SettingDialog(Context context, View.OnClickListener onClickListener,View.OnClickListener onCancelListener) {
        super(context, R.style.MyDialog);
        this.context = context;
        this.onClickListener = onClickListener;
        this.onCancelListener = onCancelListener;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setCanceledOnTouchOutside(false);
        this.setContentView(R.layout.my_dialog);
        TextView tvTitle = (TextView) findViewById(R.id.setting_dialog_title);
        tvTitle.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));

//        TextView tvInfo = (TextView) findViewById(R.id.setting_dialog_info);

//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M
//                && (Build.MANUFACTURER.equalsIgnoreCase("HUAWEI")
//                || Build.MANUFACTURER.equalsIgnoreCase("HONOR"))) {
//            tvInfo.setText(R.string.important_setting_info_need_battery_optimization);
//        } else if (Build.MANUFACTURER.equalsIgnoreCase("Xiaomi")) {
//            tvInfo.setText(R.string.important_setting_info_mi);
//        } else {
//            tvInfo.setText(R.string.important_setting_info_other);
//        }

        findViewById(R.id.btnConfirm).setOnClickListener(onClickListener);
        findViewById(R.id.btnCancel).setOnClickListener(onCancelListener);
    }
}