package com.totwoo.totwoo.widget;

import static com.totwoo.totwoo.ToTwooApplication.owner;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.Utils;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.RoundRectOutlineProvider;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.bean.TogetherBean;
import com.totwoo.totwoo.utils.ACache;
import com.totwoo.totwoo.utils.BitmapShareUtil;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.DialogHelper;

public class ShareFootDialog extends Dialog {
    BaseActivity context;
    private TogetherBean togetherBean;
    private String pairedNames;
    private String pairedHeadUrl;


    DialogHelper.MyFunction function;

    public ShareFootDialog(BaseActivity context, TogetherBean togetherBean, String pairedNames, String pairedHeadUrl, DialogHelper.MyFunction function) {
        super(context, R.style.MyDialog);
        this.context = context;
        this.togetherBean = togetherBean;
        this.pairedNames = pairedNames;
        this.pairedHeadUrl = pairedHeadUrl;
        this.function = function;
    }

    @SuppressLint("StringFormatInvalid")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.setContentView(R.layout.dialog_share_foot);

        View dialogShareFoot = findViewById(R.id.dialog_share_foot);


        RoundImageView selfIv = findViewById(R.id.love_manage_me);
        RoundImageView otherIv = findViewById(R.id.love_manage_other);
        TextView textView7 = findViewById(R.id.textView7);

        ACache aCache = ACache.get(context);
        int partner_gender = TextUtils.isEmpty(aCache.getAsString(CommonArgs.PARTNER_GENDER)) ? (1 - owner.getGender()) : Integer.valueOf(aCache.getAsString(CommonArgs.PARTNER_GENDER));
        BitmapHelper.setHead(ToTwooApplication.baseContext, selfIv, ToTwooApplication.owner.getHeaderUrl(), owner.getGender());
        BitmapHelper.setHead(ToTwooApplication.baseContext, otherIv, pairedHeadUrl, partner_gender);

        TextView mNationTv = findViewById(R.id.together_share_country_tv);
        TextView mProvinceTv = findViewById(R.id.together_share_province_tv);
        TextView mCityTv = findViewById(R.id.together_share_city_tv);
        TextView mBeyondTv = findViewById(R.id.together_share_beyond_tv);
        TextView mNameTv = findViewById(R.id.together_share_name_tv);

        mNationTv.setText(String.valueOf(togetherBean.getCountry_total()));
        mProvinceTv.setText(String.valueOf(togetherBean.getProvince_total()));
        mCityTv.setText(String.valueOf(togetherBean.getCity_total()));
        mBeyondTv.setText(setStyle(context.getString(R.string.foot_print_beyond_share, togetherBean.getPercentage()), togetherBean.getPercentage()));
        mNameTv.setText(pairedNames);
        textView7.setText(setStyle(context.getString(R.string.love_space_share_text), "Totwoo"));

        setOnShowListener(dialog -> {
                    //把dialog 截图，通过系统分享分享出去
                    BitmapShareUtil.shareBitmap(context,ImageUtils.view2Bitmap(dialogShareFoot));
                }
        );

        dialogShareFoot.setOutlineProvider(new RoundRectOutlineProvider(Apputils.dp2px(Utils.getApp(), 15)));
        dialogShareFoot.setClipToOutline(true);
    }


    private SpannableString setStyle(String string, String inside) {
        int index = string.indexOf(inside);
        int endIndex = index + inside.length();
        SpannableString spannableString = new SpannableString(string);
//        spannableString.setSpan(new AbsoluteSizeSpan(17, true), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
//        spannableString.setSpan(new StyleSpan(Typeface.BOLD), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(ContextCompat.getColor(context, R.color.color_main)), index, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }
}