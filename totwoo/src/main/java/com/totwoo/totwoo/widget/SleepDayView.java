package com.totwoo.totwoo.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.Nullable;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.SleepStateBean;
import com.totwoo.totwoo.bean.SleepUpdateBean;
import com.totwoo.totwoo.utils.CommonUtils;

public class SleepDayView extends View {
    private Rect rect;
    private Paint paint;
    private int width;
    private int state;
    private SleepUpdateBean sleepUpdateBean;
    private OnTouchItemListener onTouchItemListener;
    private boolean isSelected = false;

    public SleepDayView(Context context) {
        super(context, null);
    }

    public SleepDayView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SleepDayView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {

    }

    public void setViewInfo(int state, int length, SleepUpdateBean sleepUpdateBean) {
        width = (CommonUtils.getScreenWidth() - CommonUtils.dip2px(getContext(), 40)) * length / 1440;
        rect = new Rect(0,0,width,CommonUtils.dip2px(getContext(), 120));
        paint = new Paint();
        this.state = state;
        this.sleepUpdateBean = sleepUpdateBean;
        if(state == SleepStateBean.STATE_DEFAULT || state == SleepStateBean.STATE_NONE_SLEEP){
            paint.setColor(getResources().getColor(R.color.white));
        }else if(state == SleepStateBean.STATE_AWAKE){
            paint.setColor(getResources().getColor(R.color.sleep_awake));
        }else if(state == SleepStateBean.STATE_LIGHT_SLEEP){
            paint.setColor(getResources().getColor(R.color.sleep_light_sleep));
        }else if(state == SleepStateBean.STATE_DEEP_SLEEP){
            paint.setColor(getResources().getColor(R.color.sleep_deep_sleep));
        }
        requestLayout();
    }

    public void setOnTouchItemListener(OnTouchItemListener onTouchItemListener){
        this.onTouchItemListener = onTouchItemListener;
    }

    private void updateSelectedDraw(){
        if(state == SleepStateBean.STATE_DEFAULT){
            paint.setColor(getResources().getColor(R.color.white));
        }else if(state == SleepStateBean.STATE_AWAKE){
            if(isSelected){
                paint.setColor(getResources().getColor(R.color.sleep_awake_selected));
            }else{
                paint.setColor(getResources().getColor(R.color.sleep_awake));
            }
        }else if(state == SleepStateBean.STATE_LIGHT_SLEEP){
            if(isSelected){
                paint.setColor(getResources().getColor(R.color.sleep_light_sleep_selected));
            }else{
                paint.setColor(getResources().getColor(R.color.sleep_light_sleep));
            }
        }else if(state == SleepStateBean.STATE_DEEP_SLEEP){
            if(isSelected){
                paint.setColor(getResources().getColor(R.color.sleep_deep_sleep_selected));
            }else{
                paint.setColor(getResources().getColor(R.color.sleep_deep_sleep));
            }
        }
        invalidate();
    }

    public void setSelected(boolean isSelected){
        this.isSelected = isSelected;
    }

    public boolean getSelected(){
        return isSelected;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        setMeasuredDimension(width,heightMeasureSpec);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        canvas.drawRect(rect,paint);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if(state == SleepStateBean.STATE_DEFAULT || state == SleepStateBean.STATE_NONE_SLEEP){
            return true;
        }
        switch (event.getAction()){
            case MotionEvent.ACTION_DOWN:
            case MotionEvent.ACTION_MOVE:
                setSelected(true);
                updateSelectedDraw();
                onTouchItemListener.onTouchItem(true,sleepUpdateBean);
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                setSelected(false);
                updateSelectedDraw();
                onTouchItemListener.onTouchItem(false,sleepUpdateBean);
                break;
        }
        return true;
    }

    public interface OnTouchItemListener{
       void onTouchItem(boolean isTouch,SleepUpdateBean sleepUpdateBean);
    }

}
