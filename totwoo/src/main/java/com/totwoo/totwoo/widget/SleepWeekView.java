package com.totwoo.totwoo.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.util.AttributeSet;

import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;

import com.totwoo.totwoo.R;
import com.totwoo.totwoo.utils.CommonUtils;

public class SleepWeekView extends AppCompatTextView {
    private int light_height;
    private int deep_height;
    private Paint light_paint;
    private Paint deep_paint;
    private Rect rectLight;
    private Rect rectDeep;
    private int width;

    private float[] radiusArray = { 0f, 0f, 0f, 0f, 0f, 0f, 0f, 0f };

    public SleepWeekView(Context context) {
        this(context,null);
    }

    public SleepWeekView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs,0);
    }

    public SleepWeekView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init(){
        light_paint = new Paint();
        light_paint.setAntiAlias(true);
        light_paint.setColor(getResources().getColor(R.color.sleep_light_sleep));

        deep_paint = new Paint();
        deep_paint.setAntiAlias(true);
        deep_paint.setColor(getResources().getColor(R.color.sleep_deep_sleep));

        width = CommonUtils.dip2px(getContext(),43);
    }

    public void setSelected(boolean select_state){
        if(select_state){
            light_paint.setColor(getResources().getColor(R.color.sleep_light_sleep_selected));
            deep_paint.setColor(getResources().getColor(R.color.sleep_deep_sleep_selected));
        }else{
            light_paint.setColor(getResources().getColor(R.color.sleep_light_sleep));
            deep_paint.setColor(getResources().getColor(R.color.sleep_deep_sleep));
        }
        invalidate();
    }

    public void setInfo(int light_minute,int deep_minute, int max_minute){
        light_height = CommonUtils.dip2px(getContext(),120) * light_minute/max_minute;
        deep_height = CommonUtils.dip2px(getContext(),120) * deep_minute/max_minute;
        rectLight = new Rect(0,0,width,light_height);
        rectDeep = new Rect(0,light_height,width,light_height + deep_height);
        requestLayout();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        setMeasuredDimension(width,light_height + deep_height);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (rectLight == null || rectDeep == null) {
            return;
        }
        canvas.drawRect(rectLight,light_paint);
        canvas.drawRect(rectDeep,deep_paint);
    }

}
