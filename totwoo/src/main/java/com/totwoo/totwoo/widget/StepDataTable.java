package com.totwoo.totwoo.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Paint.Style;
import android.graphics.Typeface;
import android.os.Handler;
import android.os.Message;
import android.os.SystemClock;
import android.util.AttributeSet;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;

import com.totwoo.library.util.Apputils;
import com.totwoo.totwoo.R;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * 健步数据柱状图数据表格
 * 
 * <AUTHOR>
 * @date 2015-2015年7月20日
 */
public class StepDataTable extends View {

	/** 柱状图点击或滑动，导致选择变换回调 */
	public interface OnSelectChangeListener {
		/**
		 * 默认当数据更新的时候会调用一次
		 * 
		 * @param day_of_week
		 *            当前选择位于当前显示周的序号， 及柱状图柱子序号, 首个序号为0
		 */
		void OnSelectChange(int day_of_week);
	}

	/** 一周七天的名字描述 */
	private static final String[] DAYS_OF_WEEK = { "Sun", "Mon", "Tue", "Wed",
			"Thu", "Fri", "Sat" };

	private static final int INITAM = 0;

	private static final int SELECTEDCHANGE = 1;

	protected static final int CLICKABLE = 2;

	protected static final int NOCLICKABLE = 3;

	private Context mContext;
	/** 画笔 */
	private Paint paint;
	/** 柱状图颜色 */
	private int[] rectColorsRes = { Color.argb(220, 0, 0, 0),
			Color.rgb(141, 193, 120) };
	/** 柱 宽度 */
	private float rectWidth = 20, rectWidthSelect = 40;

	/** 目标数据值 */
	private int targetStep = 8000;
	/** 最大步数 ， 如果步数数据中全部小于20000， 则最大为20000， 如果最大数据超过20000， 则取最大数据值 */
	public int maxStep = 20000;
	/** 表格底部线位置 */
	private float table_bottom;

	/** 当前选择的天序号， 默认显示今天 */
	private int selectDay = 6;

	/** 一周的步数数据 */
	private List<Integer> stepData;

	private GestureDetector gd;

	/** 坐标系最左侧左边 */
	private float lineLeft;

	/** 坐标系宽度 */
	private float lineWidth;

	/** 选择改变监听 */
	private OnSelectChangeListener mListener;

	// 初始化动画参数
	private int amParams = 0;
	// 选择改变动画参数
	private int changeAmParams = 10;

	// 控制动画handler
	Handler handler = new Handler() {
		@Override
		public void handleMessage(Message msg) {

			switch (msg.what) {
			case INITAM:
				invalidate();
				break;
			case SELECTEDCHANGE:
				invalidate();
				break;
			case CLICKABLE:
				StepDataTable.this.setClickable(true);
				break;
			case NOCLICKABLE:
				StepDataTable.this.setClickable(false);
				break;
			}
			super.handleMessage(msg);
		}
	};

	private Bitmap bm;

	private Typeface gothicPlain;

	private Typeface gothicbiPlain;

	private Typeface gothiciPlain;

	public StepDataTable(Context context) {
		super(context);
		initTable(context);
	}

	public StepDataTable(Context context, AttributeSet attrs) {
		super(context, attrs);
		initTable(context);
	}

	public StepDataTable(Context context, AttributeSet attrs, int defStyleAttr) {
		super(context, attrs, defStyleAttr);
		initTable(context);
	}

	@SuppressLint("NewApi")
	public StepDataTable(Context context, AttributeSet attrs, int defStyleAttr,
			int defStyleRes) {
		super(context, attrs, defStyleAttr, defStyleRes);
		initTable(context);
	}

	/**
	 * 初始化视图
	 * 
	 * @param context
	 */
	private void initTable(Context context) {
        mContext = context;
        rectWidth = Apputils.dp2px(mContext, 6);
        bm = BitmapFactory.decodeResource(mContext.getResources(),
                R.drawable.step_value_bg);
//		gothicPlain = ResourcesCompat.getFont(mContext, R.font.agencyb)
//				R.font.gothic);
//		gothicbiPlain = ResourcesCompat.getFont(mContext, R.font.agencyb)
//				R.font.gothicbi.ttf);
//		gothiciPlain = ResourcesCompat.getFont(mContext, R.font.agencyb)
//				R.font.gothici);
        rectWidthSelect = Apputils.dp2px(mContext, 16);

        // 设置画笔
        paint = new Paint();
        paint.setAntiAlias(true);
        paint.setDither(true);
        paint.setStyle(Style.FILL);
        paint.setStrokeWidth(4);
        this.setLongClickable(true);
        this.setOnTouchListener((v, event) -> gd.onTouchEvent(event));

		gd = new GestureDetector(mContext,
				new GestureDetector.SimpleOnGestureListener() {
					@Override
					public boolean onSingleTapUp(MotionEvent e) {
						int cc = checkSelect(e.getX(), e.getY());
						if (cc >= 0 && cc != selectDay) {
							selectDay = cc;
							if (mListener != null) {
								mListener.OnSelectChange(selectDay);
							}
							startChangeAm();
							return true;
						}
						return false;
					}

					@Override
					public boolean onFling(MotionEvent e1, MotionEvent e2,
							float velocityX, float velocityY) {

						if (e2.getX() - e1.getX() > 100
								&& Math.abs(velocityX) > 100) {
							selectDay++;
							if (selectDay >= 7) {
								selectDay -= 7;
							}
							if (mListener != null) {
								mListener.OnSelectChange(selectDay);
							}
							startChangeAm();
						} else if ((e2.getX() - e1.getX() < -100 && Math
								.abs(velocityX) > 100)) {
							selectDay--;
							if (selectDay < 0) {
								selectDay += 7;
							}
							if (mListener != null) {
								mListener.OnSelectChange(selectDay);
							}
							startChangeAm();
						}
						return false;
					}
				});
	}

	/**
	 * 根据当前点击的点坐标， 判断当前点击的条目
	 * 
	 * @param x
	 * @param y
	 * @return
	 */
	protected int checkSelect(float x, float y) {
		float can_x = x - lineLeft;

		if (can_x < 0 || can_x > lineWidth) {
			return -1;
		}
		return (int) Math.floor(can_x / (lineWidth / 7));
	}

	@Override
	protected  void onDraw(Canvas canvas) {

		super.onDraw(canvas);

		if (stepData == null) {
			stepData = new ArrayList<>();
			for (int i = 0; i < 7; i++) {
				stepData.add(0);
			}
		}

		table_bottom = canvas.getHeight() - Apputils.dp2px(mContext, 29.5f)
				- (paint.descent() + -paint.ascent());

		// 绘制两条基线
		paint.setColor(mContext.getResources().getColor(
				R.color.text_color_black_note));
		float tar = table_bottom - targetStep / (maxStep + 0f)
				* (table_bottom - (-paint.ascent() + paint.descent()) / 2);
		// ;
		paint.setTextSize(Apputils.dp2px(mContext, 9));
		lineLeft = paint.measureText(String.valueOf(targetStep)) + 20;
		paint.setTypeface(gothiciPlain);
		canvas.drawText(String.valueOf(targetStep), 0,
				tar + paint.getTextSize() / 2 - paint.descent(), paint);
		canvas.drawText("0", paint.measureText(String.valueOf(targetStep))
				- paint.measureText("0"), table_bottom + paint.getTextSize()
				/ 2 - paint.descent(), paint);
		paint.setStrokeWidth(Apputils.dp2px(mContext, 0.5F));
		canvas.drawLine(lineLeft, tar, canvas.getWidth(), tar, paint);
		canvas.drawLine(lineLeft, table_bottom, canvas.getWidth(),
				table_bottom, paint);

		// 柱状体数据及 底部星期标注
		lineWidth = canvas.getWidth() - lineLeft;
		// 为了防止图标显示在控件外边看不见
		lineWidth = lineWidth - bm.getWidth() / 5;
		paint.setTextSize(mContext.getResources().getDimension(
				R.dimen.step_nomal_text_size));

		for (int i = 0; i < DAYS_OF_WEEK.length; i++) {
			if (stepData == null || stepData.size() < 7 || stepData.size() == 0) {
				break;
			}
			// 绘制柱形图数据
			float center_x = lineWidth / 7 * (i + 0.5f) + lineLeft;
			float rect_top_y = table_bottom - stepData.get(i) / (maxStep + 0f)
					* (table_bottom - (-paint.ascent() + paint.descent()) / 2)
					/ 100 * amParams;

			paint.setColor(stepData.get(i) > targetStep ? rectColorsRes[1]
					: rectColorsRes[0]);
			if (i == selectDay) {
				canvas.drawRect(center_x
						- (rectWidth / 2 + (rectWidthSelect - rectWidth) / 2
								/ 10 * changeAmParams), rect_top_y, center_x
						+ rectWidth / 2 + (rectWidthSelect - rectWidth) / 2
						/ 10 * changeAmParams, table_bottom, paint);
				// LogUtils.i("amparam", rect_top_y + "");
				// 后续星期绘制颜色
				paint.setColor(mContext.getResources().getColor(
						R.color.text_color_black));
				paint.setFakeBoldText(true);

//				paint.setTypeface(gothicbiPlain);

			} else {
				canvas.drawRect(center_x - rectWidth / 2, rect_top_y, center_x
						+ rectWidth / 2, table_bottom, paint);
				// 后续星期绘制颜色
				paint.setColor(mContext.getResources().getColor(
						R.color.step_info_color));

//				paint.setTypeface(gothiciPlain);
			}

			// 绘制底部星期 标注
			int off = Calendar.getInstance().get(Calendar.DAY_OF_WEEK);
			String text;
			if (i == DAYS_OF_WEEK.length - 1) {
				text = mContext.getResources().getString(R.string.today);
			} else {
				text = DAYS_OF_WEEK[(off + i) % DAYS_OF_WEEK.length];
			}

			canvas.drawText(text, center_x - paint.measureText(text) / 2,
					canvas.getHeight() - paint.getFontMetrics().descent, paint);
			paint.setFakeBoldText(false);
		}

		float center_x = lineWidth / 7 * (selectDay + 0.5f) + lineLeft;
		float rect_top_y = table_bottom - stepData.get(selectDay)
				/ (maxStep + 0f) * table_bottom / 100 * amParams
				- changeAmParams * 2;
		String text;
		// 绘制步数值数据
		// 国际版不显示单位
		if (Apputils.systemLanguageIsChinese(mContext)) {
			text = stepData.get(selectDay) + "";
		} else {
			text = stepData.get(selectDay) + "";
		}

		paint.setColor(mContext.getResources().getColor(
				R.color.text_color_white));
		paint.setAlpha(255 / 10 * changeAmParams);
		canvas.drawBitmap(bm, center_x - (bm.getWidth() / 2.0f), (rect_top_y
				- bm.getHeight() - 10), paint);

		paint.setTypeface(gothicPlain);
		paint.setFakeBoldText(false);
		paint.setTextSize(Apputils.dp2px(mContext, 13));
		canvas.drawText(text, center_x - (paint.measureText(text) / 2.0f),
				rect_top_y - bm.getHeight() - paint.getFontMetrics().ascent - 5,
				paint);

	}

	// 开启线程执行初始化动画
	public void startInitAm() {

		new Thread() {
			public void run() {
				amParams = 0;
				handler.sendEmptyMessage(NOCLICKABLE);
				SystemClock.sleep(300);
				for (int i = 0; i <= 100; i++) {
					SystemClock.sleep(10);
					amParams = i;
					handler.sendEmptyMessage(INITAM);
				}
				handler.sendEmptyMessage(CLICKABLE);
				// mHandler.sendEmptyMessage(0);
			}
        }.start();
	}

	// 开启线程执行选择切换动画
	public void startChangeAm() {

		new Thread() {
			public void run() {
				handler.sendEmptyMessage(NOCLICKABLE);
				changeAmParams = 0;
				for (int i = 0; i < 10; i++) {
					SystemClock.sleep(20);
					changeAmParams++;
					handler.sendEmptyMessage(SELECTEDCHANGE);
				}
				handler.sendEmptyMessage(CLICKABLE);
				// mHandler.sendEmptyMessage(0);
			}
        }.start();
	}

	public void setOnSelectChangeListener(OnSelectChangeListener mListener) {
		this.mListener = mListener;
	}

	public OnSelectChangeListener getOnSelectChangeListener() {
		return mListener;
	}

	public int[] getRectColorsRes() {
		return rectColorsRes;
	}

	public void setRectColorsRes(int[] rectColorsRes) {
		this.rectColorsRes = rectColorsRes;
		invalidate();
	}

	public int getTargetStep() {
		return targetStep;
	}

	public void setTargetStep(int targetStep) {
		this.targetStep = targetStep;
		invalidate();
	}

	public int getMaxStep() {
		return maxStep;
	}

	public void setMaxStep(int maxStep) {
		this.maxStep = maxStep;
		invalidate();
	}

	public int getSelectDay() {
		return selectDay;
	}

	public void setSelectDay(int selectDay) {
		this.selectDay = selectDay;
	}

	public List<Integer> getStepData() {
		return stepData;
	}

	public void setStepData(List<Integer> stepData) {
		if (stepData != null && stepData.size() < 7) {
			return;
		}
		this.stepData = stepData;

		if (mListener != null) {
			mListener.OnSelectChange(selectDay);
		}

		invalidate();
	}
}
