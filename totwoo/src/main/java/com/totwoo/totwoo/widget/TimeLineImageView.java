package com.totwoo.totwoo.widget;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.drawable.BitmapDrawable;
import android.util.AttributeSet;
import android.view.ViewTreeObserver;
import android.widget.ImageView;

import com.totwoo.library.util.Apputils;

/**
 * Created by <PERSON><PERSON>gaowei on 16/5/10.
 */
public class TimeLineImageView extends ImageView {

    /**
     * 时差滚动速度, 通过调节这个值调节时差速度
     */
    private static final float RELATE_SPEED = 0.72f;

    /**
     * 图片默认的高度, 用于判断当前控件是否被拉伸
     */
    private float defaultHeight;

    /**
     * 标题栏, 状态栏高度
     */
    private int mTopHeight;
    /**
     * 屏幕高度(减去了 mTopHeight)
     */
    private float screenHeight;
    /**
     * 最大的偏移量
     */
    private float paddingBottomMax;

    // 当前图片对应的 Bitmap
    private Bitmap mBitmap;

    /**
     * 画布按照 CENTER_CROP 变换的 Matrix
     */
    private Matrix canvasMatrix;

    private Matrix matrix;
    private int[] location;

    public TimeLineImageView(Context context) {
        this(context, null);
    }

    public TimeLineImageView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public TimeLineImageView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initListener();
    }

    private void initListener() {
        getViewTreeObserver().addOnScrollChangedListener(new ViewTreeObserver.OnScrollChangedListener() {
            @Override
            public void onScrollChanged() {
                invalidate();
            }
        });
        getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                invalidate();
            }
        });

        //mTopHeight = Apputils.dp2px(getContext(), 80);
        screenHeight = Apputils.getScreenHeight(getContext()) - mTopHeight ;

        if (getDrawable() != null) {
            mBitmap = ((BitmapDrawable) getDrawable()).getBitmap();
        }

        matrix = new Matrix();
        location = new int[2];

    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        if (defaultHeight == 0){
            // 记录图片控件原始尺寸
            defaultHeight = getHeight();
            paddingBottomMax = getMeasuredHeight() * RELATE_SPEED;
        }

        if (canvasMatrix == null && mBitmap != null){
            setCanvasMatrix();
        }
    }

    @Override
    public void setImageResource(int resId) {
        super.setImageResource(resId);

        if (getDrawable() != null){
            mBitmap = ((BitmapDrawable)getDrawable()).getBitmap();
        }
    }

    @Override
    public void setImageBitmap(Bitmap bm)
    {
        super.setImageBitmap(bm);
        mBitmap = bm;
        setCanvasMatrix();
    }

    private void setCanvasMatrix(){
        float scale;
        float dx = 0, dy = 0;

        int dwidth = mBitmap.getWidth();
        int dheight = mBitmap.getHeight();

        int vwidth = getMeasuredWidth() - getPaddingLeft() - getPaddingRight();
        int vheight = getMeasuredHeight() - getPaddingTop() - getPaddingBottom();

        if (dwidth * vheight > vwidth * dheight) {
            scale = (float) vheight / (float) dheight;
            dx = (vwidth - dwidth * scale) * 0.5f;
        } else {
            scale = (float) vwidth / (float) dwidth;
            dy = (vheight - dheight * scale) * 0.5f;
        }

        canvasMatrix = new Matrix();
        canvasMatrix.setScale(scale, scale);
        canvasMatrix.postTranslate((int) (dx + 0.5f), (int) (dy + 0.5f));
    }

    @Override
    protected void onDraw(Canvas canvas) {
        if (mBitmap == null){
            return;
        }

        canvas.concat(canvasMatrix);
        getLocationInWindow(location);
        location[1] -= mTopHeight;

        if (location[1] < screenHeight && location[1] > - getHeight()) {
            if (getHeight() != defaultHeight && defaultHeight != 0){
                int paddingBottom = (int) ((location[1] / screenHeight * paddingBottomMax));
                int offset = getPaddingTop() - paddingBottom ;

                // 处理下拉的放大效果
                matrix.setScale(getHeight()/defaultHeight, getHeight()/defaultHeight);
                matrix.postTranslate(-(getHeight()-defaultHeight)/2, offset);
                canvas.drawBitmap(mBitmap, matrix, null);
            }else{
                // 处理半速的移动效果
                int paddingBottom = (int) ((location[1] / screenHeight * paddingBottomMax));
                int offset = getPaddingTop() - paddingBottom ;
                canvas.drawBitmap(mBitmap, 0, offset, null);
            }
        }
    }
}
