package com.totwoo.totwoo.widget;

import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.RequiresApi;

import com.airbnb.lottie.LottieAnimationView;
import com.blankj.utilcode.util.ClickUtils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.activity.homeActivities.HomeBaseActivity;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BleUtils;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.TrackEvent;
import com.umeng.analytics.MobclickAgent;

import butterknife.BindView;
import butterknife.ButterKnife;


/**
 * 统一定制的顶部首饰状态栏, 包括首饰状态, 电量等不同状态的处理逻辑
 * <p>
 * Created by lixingmao on 2016/12/20.
 */

public class TopLayerLayout extends FrameLayout {
    @BindView(R.id.top_bar_layer_iamge)
    ImageView mTopBarLayerIamge;
    @BindView(R.id.top_bar_title_tv)
    TextView mTopBarTitleTv;
    @BindView(R.id.jew_connect_state_iv)
    ImageView mJewConnectStateIv;
    @BindView(R.id.jew_reconnect_iv)
    ImageView mJewReconnectIv;
    @BindView(R.id.jew_connect_state_tv)
    TextView mJewConnectStateTv;
    @BindView(R.id.jew_battery_state_tv)
    TextView mJewBatteryStateTv;
    @BindView(R.id.jew_battery_state_fl)
    FrameLayout mJewBatteryStateFl;
    @BindView(R.id.jew_battery_state_iv)
    ImageView mJewBatteryStateIv;

    @BindView(R.id.jew_battery_state_border_iv)
    ImageView jew_battery_state_border_iv;
    @BindView(R.id.jew_charge_state_iv)
    ImageView mJewChargeStateIv;
    @BindView(R.id.jew_state_bar_layout)
    FrameLayout mJewStateBarLayout;
    @BindView(R.id.jew_state_bar_content)
    LinearLayout mJewStateBarContent;
    @BindView(R.id.top_bar_fragment_right_icon)
    ImageView mRightIcon;
    @BindView(R.id.animation_view)
    LottieAnimationView mAnimation_view;
    @BindView(R.id.top_bar_fragment_right_2_icon)
    ImageView mRight2Icon;

    private Handler mHandler;

    private static final float PARALLAX_SPEED = 0.2f;

    // 顶部总高度
    private int souceHeight = -1;
    // 需要滚动隐藏的 bar 的高度
    private int scrollBarHeight = -1;

    private Context mContext;

    public TopLayerLayout(Context context) {
        super(context);
        init(context, null);
    }

    public TopLayerLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public TopLayerLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public TopLayerLayout(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attr) {
        mContext = context;
        LayoutInflater.from(context).inflate(R.layout.top_jew_state_bar, this, true);

        ButterKnife.bind(this);

        mHandler = new Handler(Looper.getMainLooper());

        if (attr != null) {
            TypedArray a = context.obtainStyledAttributes(attr, R.styleable.TopLayerLayout);
            String title = a.getString(R.styleable.TopLayerLayout_TopTitle);
            if (title != null) {
                mTopBarTitleTv.setText(title);
            }

            Drawable bg = a.getDrawable(R.styleable.TopLayerLayout_TopBackground);
            if (bg != null) {
                mTopBarLayerIamge.setImageDrawable(bg);
            }
            a.recycle();
        }

        setOnClickListener(v -> {
            // 啥也不干, 屏蔽顶部栏点击焦点
        });

        ClickUtils.expandClickArea(mJewStateBarLayout,60);
    }

    /**
     * 设置下一层的 View, 以供模糊使用
     *
     * @param view
     */
    public void setBuringLayerView(View view) {
        if (mTopBarLayerIamge != null && view != null) {
//            mTopBarLayerIamge.setBlurredView(view);
        }
    }

    public ImageView getRightIcon() {
        return mRightIcon;
    }

    public ImageView getmRight2Icon() {
        return mRight2Icon;
    }

    public FrameLayout getJewStateBarLayout() {
        return mJewStateBarLayout;
    }

    /**
     * 切换首饰状态
     */
    public void setJewState() {
        LogUtils.i("topLayer JewInfoSingleton.getInstance().getConnectState() = " + JewInfoSingleton.getInstance().getConnectState());
        mJewStateBarLayout.setOnClickListener(v -> {
            MobclickAgent.onEvent(mContext, TrackEvent.HOMEPAGE_TOP_MANAGE);
            CommonUtils.jumpToJewList(mContext);
        });

        switch (JewInfoSingleton.getInstance().getConnectState()) {
            case JewInfoSingleton.STATE_UNPAIRED:
                mAnimation_view.cancelAnimation();
                mAnimation_view.setVisibility(GONE);
                mJewConnectStateTv.setText(R.string.totwoo_unpaired);
                mJewBatteryStateTv.setVisibility(View.GONE);
                mJewConnectStateIv.setImageResource(R.drawable.disconnect_icon);
                mJewReconnectIv.setVisibility(GONE);
                mJewBatteryStateFl.setVisibility(GONE);
                mJewBatteryStateIv.setVisibility(GONE);
                mJewChargeStateIv.setVisibility(GONE);

                mJewStateBarLayout.setOnClickListener(v -> {
                    MobclickAgent.onEvent(mContext, TrackEvent.HOMEPAGE_TOP_CONNECT);
                    CommonUtils.jumpToJewList(mContext);
                });
                break;
            case JewInfoSingleton.STATE_DISCONNECTED:
                mAnimation_view.cancelAnimation();
                mAnimation_view.setVisibility(GONE);
                mJewConnectStateTv.setText(R.string.totwoo_disconnected);
                mJewConnectStateIv.setImageResource(R.drawable.disconnect_icon);
                mJewReconnectIv.setVisibility(VISIBLE);

//                mJewConnectStateTv.setCompoundDrawablesWithIntrinsicBounds(R.drawable.disconnect_icon, 0, R.drawable.reconnect_icon, 0);
                mJewBatteryStateFl.setVisibility(View.GONE);
                mJewBatteryStateTv.setVisibility(View.GONE);
                mJewBatteryStateIv.setVisibility(GONE);
                mJewChargeStateIv.setVisibility(GONE);

                mJewStateBarLayout.setOnClickListener(v -> {
                    MobclickAgent.onEvent(mContext, TrackEvent.HOMEPAGE_TOP_RECONNECT);
                    if (!BleUtils.isBlEEnable(mContext)) {
                        if (mContext instanceof HomeBaseActivity) {
                            ((HomeBaseActivity) mContext).showBluetoothDialog();
                        }
                        return;
                    }

                    if (!PermissionUtil.hasBluetoothPermission(mContext)) {
                        return;
                    }

                    BluetoothManage.getInstance().forceReconnect(true);
                    setJewState();
                    mHandler.postDelayed(mManualConnectTimeoutRunnable, BleParams.SCAN_DURATION);
                });

                break;
            case JewInfoSingleton.STATE_CONNECTED:
                mAnimation_view.cancelAnimation();
                mAnimation_view.setVisibility(GONE);
                mHandler.removeCallbacks(mManualConnectTimeoutRunnable);
                mJewConnectStateTv.setText(R.string.totwoo_connected);
                mJewConnectStateIv.setImageResource(R.drawable.connect_icon);
                mJewReconnectIv.setVisibility(GONE);
                mJewBatteryStateTv.setVisibility(View.VISIBLE);

                if (BleParams.isButtonBatteryJewelry()) {
                    mJewBatteryStateFl.setVisibility(View.GONE);
                    mJewBatteryStateTv.setVisibility(GONE);
                    mJewChargeStateIv.setVisibility(GONE);
                    return;
                }
                mJewBatteryStateTv.setVisibility(VISIBLE);
                int battery = JewInfoSingleton.getInstance().getBattery();
                if (battery > 0 || JewInfoSingleton.getInstance().isCharge()) {
                    mJewBatteryStateFl.setVisibility(View.VISIBLE);
                    mJewBatteryStateTv.setText(getResources().getString(R.string.jew_battery, Math.max(battery, 0)));
                    mJewBatteryStateIv.setVisibility(VISIBLE);
                    CommonUtils.setTintColor(mJewBatteryStateIv, CommonUtils.getBatteryIconColor(battery,false));
                    mJewBatteryStateIv.setImageResource(getBatteryIcon(battery));
                    if (JewInfoSingleton.getInstance().isCharge()) {
                        mJewChargeStateIv.setVisibility(GONE);
                        mJewBatteryStateIv.setVisibility(GONE);
                        jew_battery_state_border_iv.setImageResource(R.drawable.battery_charge);
//                        mJewChargeStateIv.setImageResource(R.drawable.battery_charge);
                    } else {
                        jew_battery_state_border_iv.setImageResource(R.drawable.battery_border);
                        mJewBatteryStateIv.setVisibility(VISIBLE);

                        mJewChargeStateIv.setVisibility(GONE);
                    }
                } else {
                    mJewBatteryStateFl.setVisibility(View.GONE);
                    mJewBatteryStateIv.setVisibility(GONE);
                    mJewChargeStateIv.setVisibility(GONE);
                }
                break;
            case JewInfoSingleton.STATE_RECONNECTING:
                mAnimation_view.playAnimation();
                mAnimation_view.setVisibility(VISIBLE);
                mJewConnectStateTv.setText(R.string.totwoo_connecting);
                mJewBatteryStateTv.setVisibility(View.GONE);
                mJewConnectStateIv.setImageResource(R.drawable.disconnect_icon);
                mJewReconnectIv.setVisibility(GONE);
                mJewBatteryStateFl.setVisibility(View.GONE);
                mJewBatteryStateIv.setVisibility(GONE);
                mJewChargeStateIv.setVisibility(GONE);
                mHandler.postDelayed(mManualConnectTimeoutRunnable, BleParams.SCAN_DURATION);
                break;
        }

        mJewConnectStateTv.forceLayout();
        mJewBatteryStateTv.forceLayout();
//        mJewStateBarContent.forceLayout();
    }

    private Runnable mManualConnectTimeoutRunnable = () -> {
        if (JewInfoSingleton.getInstance().getConnectState() == JewInfoSingleton.STATE_UNPAIRED) {
            return;
        }
//            SnackBarUtil.showLong(mJewBatteryStateTv, R.string.re_connect_failed, R.string.manage_my_jewelry, new View.OnClickListener() {
//                @Override
//                public void onClick(View v) {
//                    CommonUtils.jumpToJewList(mContext);
//                }
//            });
//        JewInfoSingleton.getInstance().setConnectState(JewInfoSingleton.STATE_DISCONNECTED);
        setJewState();
    };



    private int getBatteryIcon(int battery) {
        if (battery <= 10) {
            return R.drawable.battery_10;
        } else if (battery <= 20) {
            return R.drawable.battery_20;
        } else if (battery <= 30) {
            return R.drawable.battery_30;
        } else if (battery <= 40) {
            return R.drawable.battery_40;
        } else if (battery <= 50) {
            return R.drawable.battery_50;
        } else if (battery <= 60) {
            return R.drawable.battery_60;
        } else if (battery <= 70) {
            return R.drawable.battery_70;
        } else if (battery <= 80) {
            return R.drawable.battery_80;
        } else if (battery <= 90) {
            return R.drawable.battery_90;
        } else if (battery <= 100) {
            return R.drawable.battery_100;
        } else {
            return R.drawable.battery_100;
        }
    }


    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);

        if (scrollBarHeight < 0) {
            scrollBarHeight = mJewStateBarLayout.getMeasuredHeight();
        }

        if (souceHeight < 0) {
            souceHeight = getMeasuredHeight();
        }
    }

    float offset = 0;

    /**
     * 界面发生竖向滚动时回调, 刷新动态模糊, 并调整顶部状态栏
     *
     * @param scrollY 竖向滚动位置 大于零
     */
    public void scroll(float scrollY) {
        mTopBarLayerIamge.invalidate();

        ViewGroup.LayoutParams lp = getLayoutParams();

        offset = scrollY * PARALLAX_SPEED;
        if (offset > scrollBarHeight || offset < 0) {
            return;
        }

        lp.height = (int) (souceHeight - offset);

        setLayoutParams(lp);
        mJewStateBarContent.setTranslationY(-offset);
    }

    public void stopScroll() {
        if (offset > scrollBarHeight || offset < 0) {
            return;
        }

        if (offset > scrollBarHeight / 2) {
            autoScroll(false); // 自动隐藏
        } else {
            autoScroll(true); // 自动展示
        }
    }

    private void autoScroll(boolean show) {
        try {
            ValueAnimator scaleAnimation = new ValueAnimator();
            scaleAnimation.setDuration(100);
            scaleAnimation.setFloatValues(offset, show ? 0 : scrollBarHeight);
            scaleAnimation.addUpdateListener(animation -> scroll((Float) animation.getAnimatedValue() / PARALLAX_SPEED));
            scaleAnimation.start();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void setTitle(String title) {
        if (title != null) {
            mTopBarTitleTv.setText(title);
        }
    }

}
