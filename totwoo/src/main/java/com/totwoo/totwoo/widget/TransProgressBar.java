package com.totwoo.totwoo.widget;

import android.annotation.SuppressLint;
import android.annotation.TargetApi;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.RectF;
import android.graphics.Shader;
import android.os.Build;
import android.util.AttributeSet;
import android.view.View;

import com.totwoo.totwoo.R;

public class TransProgressBar extends View {
    // ColorInt
    private int fillColor;

    // ColorInt
    private int backgroundColor;
    private int startColor;
    private int endColor;

    private Paint fillPaint;
    private Paint backgroundPaint;

    private float percent;

    public TransProgressBar(Context context) {
        super(context);
        init(context, null);
    }

    public TransProgressBar(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public TransProgressBar(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    public TransProgressBar(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init(context, attrs);
    }

    private void init(final Context context, AttributeSet attrs) {
        if (context == null || attrs == null) {
            return;
        }

        fillColor = getResources().getColor(R.color.certification_start);
        fillPaint = new Paint();
        fillPaint.setColor(fillColor);
        fillPaint.setAntiAlias(true);

        backgroundColor = getResources().getColor(R.color.white);
        backgroundPaint = new Paint();
        backgroundPaint.setColor(backgroundColor);
        backgroundPaint.setAntiAlias(true);
        startColor = getResources().getColor(R.color.certification_start);
        endColor = getResources().getColor(R.color.certification_end);

    }

    public float getPercent() {
        return this.percent;
    }

    /**
     * @param percent FloatRange(from = 0.0, to = 1.0)
     */
    public void setPercent(final float percent) {
        this.percent = percent;
        invalidate();
    }

    private final RectF rectF = new RectF();

    @SuppressLint("DrawAllocation")
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        float drawPercent = percent;

        canvas.save();
        final int height = getMeasuredHeight() - getPaddingTop() - getPaddingBottom();
        final int width = getMeasuredWidth() - getPaddingLeft() - getPaddingRight();

        final int fillWidth = (int) (drawPercent * width);
        final float radius = height / 2.0f;


        rectF.left = 0;
        rectF.top = 0;
        rectF.right = width;
        rectF.bottom = height;

        // draw background
        if (backgroundColor != 0) {
            canvas.drawRoundRect(rectF, radius, radius, backgroundPaint);
        }

        // draw fill
        if (fillColor != 0) {
            rectF.right = fillWidth;
//            rectF.bottom = height;

            LinearGradient linearGradient = new LinearGradient(0, 0, fillWidth, height, startColor, endColor, Shader.TileMode.REPEAT);
            fillPaint.setShader(linearGradient);
            canvas.drawRoundRect(rectF, radius, radius, fillPaint);
        }

        canvas.restore();
    }
}
