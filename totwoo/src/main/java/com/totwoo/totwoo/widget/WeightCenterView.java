package com.totwoo.totwoo.widget;

import android.annotation.TargetApi;
import android.content.Context;
import android.os.Build;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.FrameLayout;

import com.totwoo.library.net.HttpRequest;
import com.totwoo.library.net.RequestParams;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.data.SysLocalDataBean;
import com.totwoo.totwoo.fragment.OnInfoConfirmListener;
import com.totwoo.totwoo.utils.ConfigData;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.RequestCallBack;
import com.totwoo.totwoo.widget.WheelView.OnWheelViewListener;

public class WeightCenterView extends FrameLayout implements OnInfoConfirmListener {

    private Context mContext;
    /**
     * 最小体重
     */
    private final int MIN_WEIGHT = 35;

    private WheelView wheelView;

    /**
     * 体重值
     */

    public int getN_weight() {
        return n_weight;
    }

    public void setN_weight(int n_weight) {
        this.n_weight = n_weight;
    }

    /**
     * 当前表盘指示体重
     */
    private int n_weight;


    public WeightCenterView(Context context) {
        this(context, null);
    }

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    public WeightCenterView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        this(context, attrs,defStyleAttr);

    }

    public WeightCenterView(Context context, AttributeSet attrs, int defStyleAttr) {
        this(context, attrs);
    }

    /**
     * 包含起始动画的构造方法， 如果需要有过度动画， 必须通过该构造方法构建
     *
     * @param context
     * @param attrs         可以为null
     */
    public WeightCenterView(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;

        LayoutInflater.from(context).inflate(R.layout.weight_setting, this);

        wheelView = (WheelView) findViewById(R.id.setting_weight_wheelview);

        wheelView.setItems(ConfigData.SETTING_WEIGHT, 5, "");
        wheelView.setOnWheelViewListener(wheelViewListener);
        wheelView.setSeletion(ToTwooApplication.owner.getWeight() == 0 ? 15
                : ToTwooApplication.owner.getWeight() - MIN_WEIGHT);
    }

    // OnWheelScrollListener scrollListener = new OnWheelScrollListener() {
    // @Override
    // public void onScrollingStarted(WheelView wheel) {
    // }
    //
    // // 保存数据结果
    // @Override
    // public void onScrollingFinished(WheelView wheel) {
    //
    // }
    // };
    //
    // OnWheelChangedListener changeListener = new OnWheelChangedListener() {
    //
    // // 动态设置动画
    // @Override
    // public void onChanged(WheelView wheel, int oldValue, int newValue) {
    // float scle = (newValue + MIN_WEIGHT) / (50 + 0f);
    // personBody.setScaleX(scle);
    // weightValueTv.setText(newValue + MIN_WEIGHT + "kg");
    // }
    //
    // };
    OnWheelViewListener wheelViewListener = new OnWheelViewListener() {
        public void onSelected(WheelView wheelView, int selectedIndex,
                               String item) {
            n_weight = wheelView.getSeletedIndex() + MIN_WEIGHT;
        }
    };

    @Override
    public void onSaved() {
        ToTwooApplication.owner.setWeight(n_weight);

        // 同步服务器, 当前设置的用户信息
        RequestParams params = HttpHelper.getBaseParams(true);
        params.addFormDataPart("weight", ToTwooApplication.owner.getWeight()
                + "");
        HttpRequest.post(
                HttpHelper.URL_UPDATE_USER_OPTION
                        + ToTwooApplication.owner.getTotwooId() + "/", params,
                new RequestCallBack<String>() {
                    @Override
                    public void onLogicFailure(HttpBaseBean<String> t) {
                        PreferencesUtils.put(mContext,
                                SysLocalDataBean.SYN_TYPE_USER_INFO, true);
                    }

                    @Override
                    public void onFailure(int error, String msg) {
                        PreferencesUtils.put(mContext,
                                SysLocalDataBean.SYN_TYPE_USER_INFO, true);
                    }
                });

    }
}
