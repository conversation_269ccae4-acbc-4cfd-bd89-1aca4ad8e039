package com.totwoo.totwoo.widget;

import android.annotation.TargetApi;
import android.content.Context;
import android.graphics.Point;
import android.os.Build;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.view.animation.Animation.AnimationListener;
import android.view.animation.TranslateAnimation;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.totwoo.library.net.HttpRequest;
import com.totwoo.library.net.RequestParams;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.data.SysLocalDataBean;
import com.totwoo.totwoo.fragment.OnConfirmListener;
import com.totwoo.totwoo.utils.ConfigData;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.RequestCallBack;
import com.totwoo.totwoo.widget.WheelView.OnWheelViewListener;

public class WeightSettingView extends FrameLayout implements OnConfirmListener {

    private Context mContext;
    /**
     * 最小体重
     */
    private final int MIN_WEIGHT = 35;

    /**
     * 头部
     */
    private ImageView personHead;
    /**
     * 身体
     */
    private ImageView personBody;
    /**
     * 体磅秤
     */
    private ImageView weightSale;
    /**
     * 体重标尺
     */
    private WheelView wheelView;

    /**
     * 体重值
     */
    private TextView weightValueTv;

    public int getN_weight() {
        return n_weight;
    }

    public void setN_weight(int n_weight) {
        this.n_weight = n_weight;
    }

    /**
     * 人体全部
     */
    private RelativeLayout personLayout;

    /**
     * 当前表盘指示体重
     */
    private int n_weight;

    /**
     * 起始动画的左上方的点
     */
    private Point centerPoint;

    public WeightSettingView(Context context) {
        this(context, null, null);
    }

    public WeightSettingView(Context context, AttributeSet attrs) {
        this(context, attrs, null);
    }

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    public WeightSettingView(Context context, AttributeSet attrs,
                             int defStyleAttr, int defStyleRes) {
        this(context, attrs, null);

    }

    public WeightSettingView(Context context, AttributeSet attrs,
                             int defStyleAttr) {
        this(context, attrs, null);
    }

    /**
     * 包含起始动画的构造方法， 如果需要有过度动画， 必须通过该构造方法构建
     *
     * @param context
     * @param attrs         可以为null
     * @param left_topPoint 动画起始点
     */
    public WeightSettingView(Context context, AttributeSet attrs,
                             Point left_topPoint) {
        super(context, attrs);
        mContext = context;
        this.centerPoint = left_topPoint;

        LayoutInflater.from(context).inflate(R.layout.fragment_weight_setting,
                this);

        personHead = (ImageView) findViewById(R.id.setting_weight_person_head);
        personBody = (ImageView) findViewById(R.id.setting_weight_person_body);
        weightSale = (ImageView) findViewById(R.id.setting_weight_weight_sale);
        wheelView = (WheelView) findViewById(R.id.setting_weight_wheelview);
        weightValueTv = (TextView) findViewById(R.id.setting_weight_weight_value);
        personLayout = (RelativeLayout) findViewById(R.id.setting_weight_person_layout);

        // 根据用户性别设置当前页面形象的性别（默认为男， ）
        if (ToTwooApplication.owner.getGender() == 1) {
            personHead.setImageResource(R.drawable.person_female_head);
            personBody.setImageResource(R.drawable.person_female_body);
        }
        if (left_topPoint == null) {
            personHead.setVisibility(View.VISIBLE);
            personBody.setVisibility(View.VISIBLE);
            wheelView.setVisibility(View.VISIBLE);
            weightSale.setVisibility(View.VISIBLE);
            weightValueTv.setVisibility(View.VISIBLE);
        } else {
            personHead.setAlpha(0f);
            personBody.setAlpha(0f);
            wheelView.setAlpha(0f);
            weightSale.setAlpha(0f);
            weightValueTv.setAlpha(0f);
            personHead.setVisibility(View.VISIBLE);
            personBody.setVisibility(View.VISIBLE);
            wheelView.setVisibility(View.VISIBLE);
            weightSale.setVisibility(View.VISIBLE);
            weightValueTv.setVisibility(View.VISIBLE);
        }

        // NumericWheelAdapter numericWheelAdapter1 = new NumericWheelAdapter(
        // mContext, MIN_WEIGHT, 100);
        // numericWheelAdapter1.setLabel(" kg");
        // wheelView.setViewAdapter(numericWheelAdapter1);
        // wheelView.setCyclic(false);// 是否可循环滑动
        // wheelView.setCurrentItem(50 - MIN_WEIGHT);
        // wheelView.setDrawShadows(false);
        // wheelView.addScrollingListener(scrollListener);
        // wheelView.addChangingListener(changeListener);
        wheelView.setItems(ConfigData.SETTING_WEIGHT, 5, "");
        wheelView.setOnWheelViewListener(wheelViewListener);
        wheelView.setSeletion(ToTwooApplication.owner.getWeight() == 0 ? 15
                : ToTwooApplication.owner.getWeight() - MIN_WEIGHT);
    }

    // OnWheelScrollListener scrollListener = new OnWheelScrollListener() {
    // @Override
    // public void onScrollingStarted(WheelView wheel) {
    // }
    //
    // // 保存数据结果
    // @Override
    // public void onScrollingFinished(WheelView wheel) {
    //
    // }
    // };
    //
    // OnWheelChangedListener changeListener = new OnWheelChangedListener() {
    //
    // // 动态设置动画
    // @Override
    // public void onChanged(WheelView wheel, int oldValue, int newValue) {
    // float scle = (newValue + MIN_WEIGHT) / (50 + 0f);
    // personBody.setScaleX(scle);
    // weightValueTv.setText(newValue + MIN_WEIGHT + "kg");
    // }
    //
    // };
    OnWheelViewListener wheelViewListener = new OnWheelViewListener() {
        public void onSelected(WheelView wheelView, int selectedIndex,
                               String item) {
            n_weight = wheelView.getSeletedIndex() + MIN_WEIGHT;
            float scle = (selectedIndex + MIN_WEIGHT) / (50 + 0f);
            // 缩小点放大的比例
            scle = 1 + (scle - 1) / 3 * 2;
            personBody.setScaleX(scle);
            weightValueTv.setText(selectedIndex + MIN_WEIGHT + "kg");

        }
    };

    @Override
    public void onSaved() {
        ToTwooApplication.owner.setWeight(n_weight);

        // 同步服务器, 当前设置的用户信息
        RequestParams params = HttpHelper.getBaseParams(true);
        params.addFormDataPart("weight", ToTwooApplication.owner.getWeight()
                + "");
        HttpRequest.post(
                HttpHelper.URL_UPDATE_USER_OPTION
                        + ToTwooApplication.owner.getTotwooId() + "/", params,
                new RequestCallBack<String>() {
                    @Override
                    public void onLogicFailure(HttpBaseBean<String> t) {
                        PreferencesUtils.put(mContext,
                                SysLocalDataBean.SYN_TYPE_USER_INFO, true);
                    }

                    @Override
                    public void onFailure(int error, String msg) {
                        PreferencesUtils.put(mContext,
                                SysLocalDataBean.SYN_TYPE_USER_INFO, true);
                    }
                });

    }

    @Override
    public Point getCenterPoint() {
        int[] loc = new int[2];
        personLayout.getLocationOnScreen(loc);
        int x = loc[0] + personLayout.getWidth() / 2;
        int y = loc[1] + personLayout.getHeight() / 2;

        return new Point(x, y);
    }

    @Override
    public void loadAnim() {
        int[] loc = new int[2];
        personLayout.getLocationOnScreen(loc);
        int x = loc[0] + personLayout.getWidth() / 2;
        int y = loc[1] + personLayout.getHeight() / 2;

        if (centerPoint != null) {
            TranslateAnimation ta = new TranslateAnimation(centerPoint.x - x,
                    0, centerPoint.y - y, 0);
            ta.setDuration(1000);
            ta.setFillAfter(true);
            personHead.setAlpha(1f);
            personBody.setAlpha(1f);

            ta.setAnimationListener(new AnimationListener() {
                @Override
                public void onAnimationStart(Animation animation) {

                }

                @Override
                public void onAnimationRepeat(Animation animation) {

                }

                @Override
                public void onAnimationEnd(Animation animation) {
                    wheelView.setAlpha(1f);
                    weightValueTv.setAlpha(1f);
                    weightSale.setAlpha(1f);
                    // 体重秤渐出动画
                    AlphaAnimation aam = new AlphaAnimation(0, 1);
                    aam.setDuration(10000);
                    aam.setFillAfter(true);
                    weightSale.startAnimation(aam);
                    weightValueTv.startAnimation(aam);
                }
            });
            personLayout.startAnimation(ta);
        }

    }
}
