package com.totwoo.totwoo.widget;

import android.app.Activity;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.ColorFilter;
import android.graphics.Paint;
import android.graphics.PixelFormat;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.utils.ViewUtil;

import java.util.ArrayList;
import java.util.List;

public class WheelView extends ScrollView {

    public static final String TAG = WheelView.class.getSimpleName();

    public interface OnWheelViewListener {
        void onSelected(WheelView wheelView, int selectedIndex,
                        String item);

    }

    private Context context;
    // private ScrollView scrollView;

    private LinearLayout views;

    private String Suffix;

    public WheelView(Context context) {
        super(context);
        init(context);
    }

    public WheelView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public WheelView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init(context);
    }

//    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
//    public WheelView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
//        super(context, attrs, defStyleAttr, defStyleRes);
//        init(context);
//    }

    // String[] items;
    private List<String> items;

    private List<String> getItems() {
        return items;
    }

    /**
     * @param list             列表数据
     * @param displayItemCount 显示条目
     * @param Suffix           选中item的特别后缀
     */
    public void setItems(List<String> list, int displayItemCount, String Suffix) {

        this.Suffix = Suffix;
        this.displayItemCount = displayItemCount;
        offset = displayItemCount / 2;
        if (selectedIndex == 0) {
            selectedIndex = offset;
        }
        if (null == items) {
            items = new ArrayList<String>();
        }
        items.clear();
        items.addAll(list);

        // 前面和后面补全
        for (int i = 0; i < offset; i++) {
            items.add(0, "");
            items.add("");
        }
        init(context);
        initData();

    }

    public static final int OFF_SET_DEFAULT = 1;
    int offset = OFF_SET_DEFAULT; // 偏移量（需要在最前面和最后面补全）

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    private int displayItemCount; // 每页显示的数量

    private int selectedIndex = 0;

    private void init(Context context) {
        this.context = context;

        // scrollView = ((ScrollView)this.getParent());
        // Logger.d(TAG, "scrollview: " + scrollView);
        // Logger.d(TAG, "parent: " + this.getParent());
        // this.setOrientation(VERTICAL);
        this.setVerticalScrollBarEnabled(false);

        if (views == null) {
            views = new LinearLayout(context);
            views.setOrientation(LinearLayout.VERTICAL);
            this.addView(views);
        }

        scrollerTask = new Runnable() {

            public void run() {

                int newY = getScrollY();
                if (initialY - newY == 0) { // stopped
                    final int remainder = initialY % itemHeight;
                    final int divided = initialY / itemHeight;
                    // Logger.d(TAG, "initialY: " + initialY);
                    // Logger.d(TAG, "remainder: " + remainder + ", divided: " +
                    // divided);
                    if (remainder == 0) {
                        selectedIndex = divided + offset;

                        onSeletedCallBack();
                    } else {
                        if (remainder > itemHeight / 2) {
                            WheelView.this.post(new Runnable() {
                                @Override
                                public void run() {
                                    WheelView.this.smoothScrollTo(0, initialY
                                            - remainder + itemHeight);

                                    selectedIndex = divided + offset + 1;
                                    onSeletedCallBack();
                                }
                            });
                        } else {
                            WheelView.this.post(new Runnable() {
                                @Override
                                public void run() {
                                    WheelView.this.smoothScrollTo(0, initialY
                                            - remainder);
                                    selectedIndex = divided + offset;
                                    onSeletedCallBack();
                                }
                            });
                        }
                    }

                } else {
                    initialY = getScrollY();
                    WheelView.this.postDelayed(scrollerTask, newCheck);
                }
            }
        };

    }

    private int initialY;

    private Runnable scrollerTask;
    private int newCheck = 50;

    private int displayCount;// 总体显示数量

    public void startScrollerTask() {

        initialY = getScrollY();

        LogUtils.i("Wheel", "initialY" + initialY);
        this.postDelayed(scrollerTask, newCheck);
    }

    private void initData() {
        if (views.getChildCount() == 0) {
            for (String item : items) {
                views.addView(createView(item));
                displayCount++;
            }
        }
        int newDiffer = items.size() - displayCount;

        // 判断大于0就添加Views
        if (newDiffer > 0) {
            if (views.getChildCount() == displayCount) {
                for (int i = views.getChildCount(); i < items.size(); i++) {

                    views.addView(createView(items.get(i - offset)), i - offset);

                    displayCount++;
                }
                // else {
                // views.getChildAt(i - 1 - offset)
                // .setVisibility(View.VISIBLE);
                // displayCount++;
                // }
                //
                // }
                // } else if (newDiffer < 0) {
                // for (int i = views.getChildCount(); i > items.size(); i--) {
                // views.getChildAt(i - 1 - offset).setVisibility(View.GONE);
                // displayCount--;
                // }

            }
        }
        // 每当重新设置items的时候不是重新生成View而是使用之前的缓存
        for (int i = offset; i < views.getChildCount(); i++) {
            if (i < items.size()) {
                if (views.getChildAt(i).getVisibility() == View.GONE) {
                    displayCount++;
                }

                views.getChildAt(i).setVisibility(View.VISIBLE);

            } else {
                if (views.getChildAt(i).getVisibility() == View.VISIBLE) {
                    displayCount--;
                }
                views.getChildAt(i - offset).setVisibility(View.GONE);
                if (i - offset <= selectedIndex) {
                    selectedIndex = items.size() - offset - 1;
                }
            }
        }
        refreshItemView(0);
    }

    private int itemHeight = 0;



    private TextView createView(String item) {
        TextView tv = new TextView(context);
        tv.setLayoutParams(new LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));
        tv.setSingleLine(true);
        tv.setTextSize(TypedValue.COMPLEX_UNIT_SP, 15);
        tv.setText(item);
        tv.setGravity(Gravity.CENTER);
        int padding = Apputils.dp2px(context, 11.5f);
        tv.setPadding(0, padding, 0, padding);
        if (0 == itemHeight) {
            itemHeight = ViewUtil.getViewMeasuredHeight(tv);
            // Logger.d(TAG, "itemHeight: " + itemHeight);
            views.setLayoutParams(new LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT, itemHeight
                    * displayItemCount));

            ViewGroup.LayoutParams lp = this
                    .getLayoutParams();
            lp.height =itemHeight * displayItemCount ;


            // LinearLayout.LayoutParams layoutParams = new
            // LinearLayout.LayoutParams(
            // lp.width, lp.height);
            // layoutParams.weight = 1;
            this.setLayoutParams(lp);
        }
        return tv;
    }

    public  int getItemHetght(){
        return  itemHeight;
    }

    @Override
    protected void onScrollChanged(int l, int t, int oldl, int oldt) {
        super.onScrollChanged(l, t, oldl, oldt);

        // Logger.d(TAG, "l: " + l + ", t: " + t + ", oldl: " + oldl +
        // ", oldt: " + oldt);

        // try {
        // Field field = ScrollView.class.getDeclaredField("mScroller");
        // field.setAccessible(true);
        // OverScroller mScroller = (OverScroller) field.get(this);
        //
        //
        // if(mScroller.isFinished()){
        // Logger.d(TAG, "isFinished...");
        // }
        //
        // } catch (Exception e) {
        // e.printStackTrace();
        // }

        refreshItemView(t);

        if (t > oldt) {
            // Logger.d(TAG, "向下滚动");
            scrollDirection = SCROLL_DIRECTION_DOWN;
        } else {
            // Logger.d(TAG, "向上滚动");
            scrollDirection = SCROLL_DIRECTION_UP;

        }

    }

    private void refreshItemView(int y) {

        int position = y / itemHeight + offset;
        int remainder = y % itemHeight;
        int divided = y / itemHeight;
        // LogUtils.i("Wheel", "remainder"+remainder);

        if (remainder == 0) {
            position = divided + offset;
        } else {
            if (remainder > itemHeight / 2) {
                position = divided + offset + 1;
            }
            if (position == selectedIndex) {
                return;
            }
            if (srcolled) {
                selectedIndex = position;
                onSeletedCallBack();
            }

            // LogUtils.i("Wheel","position:"+position);
            // if(remainder > itemHeight / 2){
            // if(scrollDirection == SCROLL_DIRECTION_DOWN){
            // position = divided + offset;
            // Logger.d(TAG, ">down...position: " + position);
            // }else if(scrollDirection == SCROLL_DIRECTION_UP){
            // position = divided + offset + 1;
            // Logger.d(TAG, ">up...position: " + position);
            // }
            // }else{
            // // position = y / itemHeight + offset;
            // if(scrollDirection == SCROLL_DIRECTION_DOWN){
            // position = divided + offset;
            // Logger.d(TAG, "<down...position: " + position);
            // }else if(scrollDirection == SCROLL_DIRECTION_UP){
            // position = divided + offset + 1;
            // Logger.d(TAG, "<up...position: " + position);
            // }
            // }
            // }

            // if(scrollDirection == SCROLL_DIRECTION_DOWN){
            // position = divided + offset;
            // }else if(scrollDirection == SCROLL_DIRECTION_UP){
            // position = divided + offset + 1;
        }

        int childSize = views.getChildCount();
        for (int i = -displayItemCount; i < displayItemCount; i++) {
            TextView itemView = (TextView) views.getChildAt(position + i);
            if (null == itemView) {
                continue;
            }
            String text = (String) itemView.getText();
            if (0 == i) {
                if (Suffix != null && (text.indexOf(Suffix) < 0)) {

                    itemView.setText(text + Suffix);
                }

                itemView.setTextColor(getResources().getColor(R.color.color_main));
            } else {
                if (Suffix != null) {
                    itemView.setText(text.replace(Suffix, ""));
                }

                itemView.setTextColor(Color.parseColor("#cccccc"));
            }
        }

    }

    /**
     * 获取选中区域的边界
     */
    private int[] selectedAreaBorder;

    private int[] obtainSelectedAreaBorder() {
        if (null == selectedAreaBorder) {
            selectedAreaBorder = new int[2];

            selectedAreaBorder[0] = itemHeight * (displayItemCount / 2);
            selectedAreaBorder[1] = itemHeight * (displayItemCount / 2 + 1);
        }
        return selectedAreaBorder;
    }

    private int scrollDirection = -1;
    private static final int SCROLL_DIRECTION_UP = 0;
    private static final int SCROLL_DIRECTION_DOWN = 1;

    Paint paint;
    int viewWidth;

    @Override
    public void setBackgroundDrawable(Drawable background) {

        if (viewWidth == 0 && getContext() != null) {
            viewWidth = ((Activity) getContext()).getWindowManager()
                    .getDefaultDisplay().getWidth();
            // Logger.d(TAG, "viewWidth: " + viewWidth);
        }

        if (null == paint) {
            paint = new Paint();
            paint.setColor(Color.parseColor("#20000000"));
            paint.setStrokeWidth(2);
        }

        background = new Drawable() {
            @Override
            public void draw(@NonNull Canvas canvas) {
                canvas.drawLine(0,
                        obtainSelectedAreaBorder()[0], viewWidth,
                        obtainSelectedAreaBorder()[0], paint);
                canvas.drawLine(0,
                        obtainSelectedAreaBorder()[1], viewWidth,
                        obtainSelectedAreaBorder()[1], paint);
            }

            @Override
            public void setAlpha(int alpha) {

            }

            @Override
            public void setColorFilter(ColorFilter cf) {

            }

            @Override
            public int getOpacity() {
                return PixelFormat.UNKNOWN;
            }
        };

        super.setBackgroundDrawable(background);

    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        // Logger.d(TAG, "w: " + w + ", h: " + h + ", oldw: " + oldw +
        // ", oldh: " + oldh);
        viewWidth = w;
        setBackgroundDrawable(null);
    }

    /**
     * 选中回调
     */
    private void onSeletedCallBack() {
        if (null != onWheelViewListener) {
            onWheelViewListener.onSelected(this, getSeletedIndex(),
                    items.get(selectedIndex));
        }

    }

    public void setSeletion(int position) {
        final int p = position;
        selectedIndex = p + offset;
        srcolled = false;
        onSeletedCallBack();
        this.post(new Runnable() {
            @Override
            public void run() {
                WheelView.this.smoothScrollTo(0, p * itemHeight);
                onSeletedCallBack();
            }
        });
    }

    public String getSeletedItem() {
        if(selectedIndex < 0){
            selectedIndex = 0;
        }
        return items.get(selectedIndex);
    }

    public int getSeletedIndex() {
        return selectedIndex - offset;
    }

    @Override
    public void fling(int velocityY) {
        super.fling(velocityY / 3);
    }

    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        srcolled = true;
        if (ev.getAction() == MotionEvent.ACTION_UP) {

            startScrollerTask();
        }
        if (ev.getAction() == MotionEvent.ACTION_MOVE) {

        }
        return super.onTouchEvent(ev);
    }

    private OnWheelViewListener onWheelViewListener;
    private boolean srcolled = true;

    public OnWheelViewListener getOnWheelViewListener() {
        return onWheelViewListener;
    }

    public void setOnWheelViewListener(OnWheelViewListener onWheelViewListener) {
        this.onWheelViewListener = onWheelViewListener;
    }

}