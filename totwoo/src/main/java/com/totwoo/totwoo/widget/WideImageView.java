package com.totwoo.totwoo.widget;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.RectF;
import android.util.AttributeSet;

import com.totwoo.totwoo.R;

/**
 * 固定宽度的 ImageView , 该 View 宽度固定,
 * <p>
 * 根据设置图片的高度自动调整比例, 显示完成的图片
 * Created by lixing<PERSON><PERSON> on 16/7/22.
 */
public class WideImageView extends androidx.appcompat.widget.AppCompatImageView {
    /**
     * 圆角半径
     */
    private float mRadius;

    private RectF mRectF;
    private Paint maskPaint;
    private Paint zonePaint;

    public WideImageView(Context context) {
        super(context);

        init(context);
    }

    public WideImageView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);

    }

    public WideImageView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);

    }


//    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
//    public WideImageView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
//        super(context, attrs, defStyleAttr, defStyleRes);
//        init(context);
//
//    }

    private void init(Context context) {
        setLayerType(LAYER_TYPE_SOFTWARE, null);
        maskPaint = new Paint();
        maskPaint.setAntiAlias(true);
        maskPaint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.SRC_IN));

        zonePaint = new Paint();
        zonePaint.setAntiAlias(true);
        zonePaint.setColor(Color.WHITE);

        mRectF = new RectF();
    }


    /**
     * 设置需要处理圆角的的半径
     *
     * @param mRadius
     */
    public void setRoundRadius(float mRadius) {
        this.mRadius = mRadius;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);

        int measureWidth = MeasureSpec.getSize(widthMeasureSpec);
        int measureHeight = MeasureSpec.getSize(heightMeasureSpec);
        int measureWidthMode = MeasureSpec.getMode(widthMeasureSpec);
        int measureHeightMode = MeasureSpec.getMode(heightMeasureSpec);

        int width ;
        int height ;

        int srcW = 0, srcH = 0;

        if (getDrawable() != null){
            srcW = getDrawable().getIntrinsicWidth();
            srcH = getDrawable().getIntrinsicHeight();
        }

        if (srcH <= 0 || srcW <= 0){
            return;
        }else{
            width = measureWidth;

            height = getDrawable().getIntrinsicHeight() * width / getDrawable().getIntrinsicWidth();

            // 保证最小的高度
            if (height < measureHeight){
                height = measureHeight;
            }
        }

        setMeasuredDimension(
                MeasureSpec.makeMeasureSpec(measureWidthMode == MeasureSpec.EXACTLY ? measureWidth : width, measureWidthMode),
                MeasureSpec.makeMeasureSpec(measureHeightMode == MeasureSpec.EXACTLY ? measureHeight : height, measureHeightMode));
    }



    @Override
    public void onDraw(Canvas canvas) {
        mRectF.right = getWidth();
        mRectF.bottom = getHeight();

        canvas.drawRoundRect(mRectF, mRadius, mRadius, zonePaint);

        canvas.saveLayer(mRectF, maskPaint, Canvas.ALL_SAVE_FLAG);

        super.onDraw(canvas);

        canvas.restore();
        // 填充四个角的透明区域
        if (mRadius != 0){
            canvas.drawColor(getContext().getResources().getColor(R.color.layer_bg_white), PorterDuff.Mode.DST_OVER);
        }
    }

    /**
     * 根据原图添加圆角
     *
     * @param source
     * @return
     */
    private Bitmap createRoundConerImage(Bitmap source, float mRadius){
        final Paint paint = new Paint();
        paint.setAntiAlias(true);
        Bitmap target = Bitmap.createBitmap(getWidth(), getHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(target);
        RectF rect = new RectF(0, 0, source.getWidth(), source.getHeight());
        canvas.drawRoundRect(rect, mRadius, mRadius, paint);
        paint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.SRC_IN));
        canvas.drawBitmap(source, 0, 0, paint);
        return target;
    }
}
