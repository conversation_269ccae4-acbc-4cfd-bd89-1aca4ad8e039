package com.totwoo.totwoo.widget;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.View;

import com.totwoo.totwoo.R;

public class WishShareDialog extends Dialog {

    Context context;
    View.OnClickListener onClickListener;
    View.OnClickListener onCancelListener;

    public WishShareDialog(Context context, View.OnClickListener onClickListener, View.OnClickListener onCancelListener) {
        super(context, R.style.MyDialog);
        this.context = context;
        this.onClickListener = onClickListener;
        this.onCancelListener = onCancelListener;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.setContentView(R.layout.wish_share_dialog);

        findViewById(R.id.wish_dialog_share).setOnClickListener(onClickListener);
        findViewById(R.id.wish_dialog_close).setOnClickListener(onCancelListener);
    }
}