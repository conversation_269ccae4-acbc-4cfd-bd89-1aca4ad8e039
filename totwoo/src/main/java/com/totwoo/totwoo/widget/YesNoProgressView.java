package com.totwoo.totwoo.widget;

import android.animation.ArgbEvaluator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.view.View;
import android.view.animation.DecelerateInterpolator;

import com.totwoo.library.util.Apputils;

/**
 * Yes or No 选择过程中过渡的进度动画
 * <p/>
 * Created by lixingmao on 16/4/14.
 */
public class YesNoProgressView extends View {
    /**
     * 进入绘制各个阶段对应的时间
     */
    private static final int[] YESNO_PROGRESS_DURINGS = {400, 2000, 400, 300, 800};

    // 背景颜色
    private final int BG_COLOR = Color.parseColor("#edede9");
    // 圆环背景颜色
    private final int RING_BG_COLOR = Color.parseColor("#827ba9");
    // 圆环颜色
    private final int RING_COLOR = Color.parseColor("#d6d4c8");

    private final int RIGHT_BG_COLOR = Color.parseColor("#e0eae5");

    // 圆环的宽度
    private int ringWidth;

    // 对号绘制前, 原图的缩放的半径
    private int ringScale;

    /**
     * 圆环尺寸
     */
    private int ringSize;

    /**
     * 动画绘制的几个阶段: 0,圆环轨道渐现  1, 圆环绘制 2, 圆环收缩渐变 3, 对号绘制, 4, 对号等待
     */
    private int progress_step;

    /**
     * 是否已经开始动画
     */
    private boolean isStart;
    private Context mContext;

    /**
     * 动画绘制时使用, 各个阶段对应的进度值
     */
    private float mAngle;

    private RectF ringBgRect;
    private RectF ringRect;

    private float max;

    /**
     * 颜色差值计算器
     */
    private ArgbEvaluator evaluator;

    /**
     * 对一系列属性动画的对象持有, 以便随时取消
     */
    private ValueAnimator anim;

    /**
     * 进度结束的回调函数
     */
    public interface OnProgressCompleteCallBack {
        void complete();
    }

    private OnProgressCompleteCallBack mCallBack;
    // 绘制画笔
    private Paint mPaint;


    public YesNoProgressView(Context context) {
        super(context);
        init(context);
    }

    public YesNoProgressView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public YesNoProgressView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

//    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
//    public YesNoProgressView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
//        super(context, attrs, defStyleAttr, defStyleRes);
//        init(context);
//    }

    /**
     * 初始化
     *
     * @param context
     */
    private void init(Context context) {
        mContext = context;

        mPaint = new Paint();
        mPaint.setDither(true);
        mPaint.setAntiAlias(true);

        evaluator = new ArgbEvaluator();
        ringWidth = Apputils.dp2px(mContext, 8);
        ringScale = Apputils.dp2px(mContext, 4);
    }

    /**
     * 设置进度完成回调
     *
     * @param mCallBack
     */
    public void setCallBack(OnProgressCompleteCallBack mCallBack) {
        this.mCallBack = mCallBack;
    }

    /**
     * 开始过渡动画
     */
    public void start() {
        if (isStart) {
            return;
        }

        progress_step = 0;
        startProgress();

        isStart = true;
    }

    /**
     * 开始不同阶段的动画
     */
    private void startProgress() {
        switch (progress_step) {
            case 0:
                max = Apputils.dp2px(mContext, 8);
                anim = ValueAnimator.ofFloat(0, max);
                anim.setDuration(YESNO_PROGRESS_DURINGS[progress_step]);
                anim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    @Override
                    public void onAnimationUpdate(ValueAnimator animation) {
                        mAngle = (Float) animation.getAnimatedValue();
                        if (mAngle == max) {
                            progress_step++;
                            startProgress();
                        }
                        invalidate();
                    }
                });
                anim.start();
                break;
            case 1:
                max = 360;
                anim = ValueAnimator.ofFloat(0, max);
                anim.setDuration(YESNO_PROGRESS_DURINGS[progress_step]);
                anim.setInterpolator(new DecelerateInterpolator(1f));
                anim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    @Override
                    public void onAnimationUpdate(ValueAnimator animation) {
                        mAngle = (Float) animation.getAnimatedValue();
                        if (mAngle == max) {
                            progress_step++;
                            startProgress();
                        }
                        invalidate();
                    }
                });
                anim.start();
                break;
            case 2:
                // 圆形先收缩后复原, 圆环向内延伸填满, 颜色整体渐变完成
                max = 1;
                anim = ValueAnimator.ofFloat(0, max);
                anim.setDuration(YESNO_PROGRESS_DURINGS[progress_step]);
                anim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    @Override
                    public void onAnimationUpdate(ValueAnimator animation) {
                        mAngle = (Float) animation.getAnimatedValue();
                        if (mAngle == max) {
                            progress_step++;
                            startProgress();
                        }
                        invalidate();
                    }
                });
                anim.start();
                break;
            case 3:
                max = ringSize / 5 * 3;
                anim = ValueAnimator.ofFloat(0, max);
                anim.setDuration(YESNO_PROGRESS_DURINGS[progress_step]);
                anim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    @Override
                    public void onAnimationUpdate(ValueAnimator animation) {
                        mAngle = (Float) animation.getAnimatedValue();
                        if (mAngle == max) {
//                            progress_step++;
                            if (mCallBack != null) {
                                new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        mCallBack.complete();
                                        isStart = false;
                                    }
                                }, YESNO_PROGRESS_DURINGS[progress_step + 1]);
                            }
                        }
                        invalidate();
                    }
                });
                anim.start();
                break;

        }

    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);

        // 为第三阶段圆环的放大预留空间
        ringSize = getWidth() - ringScale * 2;

        ringBgRect = new RectF(0, 0, ringSize, ringSize);
        ringRect = new RectF(ringWidth / 2, ringWidth / 2, ringSize - ringWidth / 2, ringSize - ringWidth / 2);

    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        // 画布定位到实际绘制的圆环左上角
        canvas.translate(ringScale, ringScale);
        float off = 0;
        switch (progress_step) {
            case 0:
                // 绘制背景
                mPaint.setColor(BG_COLOR);
                mPaint.setStyle(Paint.Style.FILL);
                canvas.drawOval(ringBgRect, mPaint);

                // 绘制圆环背景
                mPaint.setStrokeWidth(mAngle);
                mPaint.setColor(RING_BG_COLOR);
                mPaint.setStyle(Paint.Style.STROKE);
                canvas.drawOval(new RectF(mAngle / 2, mAngle / 2, ringSize - mAngle / 2, ringSize - mAngle / 2), mPaint);
                break;
            case 1:
                // 绘制背景
                mPaint.setColor(BG_COLOR);
                mPaint.setStyle(Paint.Style.FILL);
                canvas.drawOval(ringRect, mPaint);

                // 绘制轨道
                mPaint.setStrokeWidth(ringWidth);
                mPaint.setStyle(Paint.Style.STROKE);

                // 计算渐变色
                off = 150 - mAngle;
                if (off < 0) {
                    off = 0;
                }
                mPaint.setColor((Integer) evaluator.evaluate((1 - off / 150), RING_BG_COLOR, Color.parseColor("#f9f9f9")));
                canvas.drawOval(ringRect, mPaint);

                // 绘制弧度
                mPaint.setColor(RING_COLOR);
                canvas.drawArc(ringRect, 270, mAngle, false, mPaint);
                break;
            case 2:
                // 圆形先收缩后复原, 圆环向内延伸填满, 颜色整体渐变完成
                if (mAngle >= 0.5) {
                    off = (1 - mAngle) * ringScale;
                } else {
                    off = mAngle * ringScale;
                }
                // 绘制背景
                mPaint.setColor(BG_COLOR);
                mPaint.setStyle(Paint.Style.FILL);
                canvas.drawOval(new RectF(-off, -off, ringSize + off, ringSize + off), mPaint);

                canvas.translate(-off, -off);

                // 绘制圆环
                float newSize = ringSize + 2 * off;
                float width = (newSize / 2 - ringWidth) * mAngle + ringWidth;
                mPaint.setStrokeWidth(width);
                mPaint.setColor((Integer) evaluator.evaluate(mAngle, RING_COLOR, RIGHT_BG_COLOR));
                mPaint.setStyle(Paint.Style.STROKE);
                canvas.drawOval(new RectF(width / 2, width / 2, newSize - width / 2, newSize - width / 2), mPaint);

                break;
            case 3:
                // 绘制背景
                mPaint.setColor(RIGHT_BG_COLOR);
                mPaint.setStyle(Paint.Style.FILL);
                canvas.drawOval(ringBgRect, mPaint);

                mPaint.setStrokeWidth(Apputils.dp2px(mContext, 5));
                mPaint.setStyle(Paint.Style.FILL);
                mPaint.setColor(Color.WHITE);

                float bottomX  = ringSize * 0.22f;
                if (mAngle < bottomX) {
                    canvas.drawLine(ringSize / 5, ringSize / 2, ringSize / 5 + mAngle, ringSize / 2 + mAngle, mPaint);
                } else {
                    canvas.drawLine(ringSize / 5, ringSize / 2, ringSize / 5 + bottomX , ringSize / 2 + bottomX, mPaint);
                    canvas.drawLine(ringSize / 5 + bottomX - Apputils.dp2px(mContext, 2.5f), ringSize / 2 + bottomX, ringSize / 5 + mAngle, ringSize / 2 + ringSize / 5 - (mAngle - bottomX), mPaint);
                }
                break;
        }
    }

    @Override
    public void clearAnimation() {
        super.clearAnimation();

        if (anim != null && anim.isRunning())
            anim.cancel();
    }
}
