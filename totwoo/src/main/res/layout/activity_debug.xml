<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/layer_bg_white"
    android:orientation="vertical"
    android:paddingHorizontal="15dp">

    <include layout="@layout/totwoo_topbar_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/activity_actionbar_height"/>


    <Button
        android:id="@+id/btn02"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="0x02"/>


    <Button
        android:id="@+id/btn03"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="0x03"/>

    <Button
        android:id="@+id/btn04"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="0x04"/>





    <Button
        android:id="@+id/btn05"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="分享"/>

    <!-- JobScheduler 调试按钮 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:text="JobScheduler 调试"
        android:textSize="16sp"
        android:textStyle="bold"/>

    <Button
        android:id="@+id/btn_job_status"
        android:layout_width="200dp"
        android:layout_height="40dp"
        android:layout_marginTop="10dp"
        android:text="查看Job状态"/>

    <Button
        android:id="@+id/btn_job_reset"
        android:layout_width="200dp"
        android:layout_height="40dp"
        android:layout_marginTop="5dp"
        android:text="重置Job统计"/>

    <Button
        android:id="@+id/btn_job_cancel"
        android:layout_width="200dp"
        android:layout_height="40dp"
        android:layout_marginTop="5dp"
        android:text="取消Job"/>

    <Button
        android:id="@+id/btn_job_schedule"
        android:layout_width="200dp"
        android:layout_height="40dp"
        android:layout_marginTop="5dp"
        android:text="重新调度Job"/>


</LinearLayout>